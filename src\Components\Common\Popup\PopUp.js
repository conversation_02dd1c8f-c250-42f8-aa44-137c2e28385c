import styles from "./Popup.module.css";

const PopUp = ({ ifOK, ifCancel, text }) => {
    return (
        <div className={styles.modalOverlay}>
            <div className={styles.modalContent}>
                <div className={styles.modalBody}>
                    <p style={{ fontFamily: "Inter" }}>{text}</p>
                </div>
                <div className={styles.modalFooter}>
                    <button className={styles.okButton} onClick={ifOK}>
                        OK
                    </button>
                    <button className={styles.cancelButton} onClick={ifCancel}>
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    )
}

export default PopUp