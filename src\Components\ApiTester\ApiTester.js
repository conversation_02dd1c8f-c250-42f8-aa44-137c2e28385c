import { useState, useEffect } from 'react';
import styles from './ApiTester.module.css';
import globalStyles from  "../globalStyles.module.css"
const APITester = ({close, apiModelRes}) => {
    const [apiModel, setApiModel] = useState(apiModelRes  || {
        executor_method_type: 'GET',
        apiUrl: '',
        headers: [
            { key: 'apikey', value: '', description: '' },
            { key: 'pkey', value: '', description: '' }
        ],
        queryParams: [],
        pathParams: [],
        requestBody: {
            type: 'raw',
            value: ''
        },
        authModel: {
            authType: 'no_auth',
            authAttributes: {}
        }
    });

    const [activeTab, setActiveTab] = useState('HEADERS');
    const [requestBodySelected, setRequestBodySelected] = useState('raw');
    const [selectedRawType, setSelectedRawType] = useState('JSON');
    const [isApiExecuted, setIsApiExecuted] = useState(false);
    const [isApiExecutedOnce, setIsApiExecutedOnce] = useState(false);
    const [apiExecutionLoading, setApiExecutionLoading] = useState(false);
    const [latestResponseStatusCode, setLatestResponseStatusCode] = useState(null);
    const [latestResponseBody, setLatestResponseBody] = useState(null);
    const [isResultValidJson, setIsResultValidJson] = useState(true);

    const httpOperations = [
        { name: 'GET' },
        { name: 'POST' },
        { name: 'PUT' },
        { name: 'DELETE' },
        { name: 'PATCH' }
    ];

    const requestBodyTypes = [
        { value: 'none', displayName: 'None' },
        { value: 'raw', displayName: 'Raw' }
    ];

    const rawTypes = ['JSON', 'XML', 'Text'];

    const parameterHeaders = ['key', 'value', 'description'];
    const headers = ['key', 'value', 'description'];

    const authModels = [
        { value: 'no_auth', display_name: 'No Auth' },
        { value: 'bearer_token', display_name: 'Bearer Token' },
        { value: 'api_key', display_name: 'API Key' },
        { value: 'basic_authentication', display_name: 'Basic Auth' },
        { value: 'oauth_1_0', display_name: 'OAuth 1.0' }
    ];

    const signatureMethods = [
        { value: 'HMAC-SHA1', display_name: 'HMAC-SHA1' },
        { value: 'HMAC-SHA256', display_name: 'HMAC-SHA256' }
    ];

    const apiConnectorList = [];

    const addHeader = () => {
        setApiModel({
            ...apiModel,
            headers: [...apiModel.headers, { key: '', value: '', description: '' }]
        });
    };

    const removeHeader = (index) => {
        const updatedHeaders = [...apiModel.headers];
        updatedHeaders.splice(index, 1);
        setApiModel({
            ...apiModel,
            headers: updatedHeaders
        });
    };

    const addParameter = () => {
        setApiModel({
            ...apiModel,
            queryParams: [...apiModel.queryParams, { key: '', value: '', description: '' }]
        });
    };

    const removeParameter = (index) => {
        const updatedParams = [...apiModel.queryParams];
        updatedParams.splice(index, 1);
        setApiModel({
            ...apiModel,
            queryParams: updatedParams
        });
    };



    const refreshRequestBody = () => {
        // Logic to refresh request body based on selection
    };

    const checkQueryParams = (event) => {
        // Logic to check query parameters
    };

    const parameterDataCheck = (event) => {
        // Logic to check parameter data
    };

    const updateAuthModel = (authModel) => {
        setApiModel({
            ...apiModel,
            authModel
        });
    };

    const emitData = () => {

        setApiExecutionLoading(true);

        // // Simulating API execution
        // setTimeout(() => {
        //     setIsApiExecuted(true);
        //     setIsApiExecutedOnce(true);
        //     setApiExecutionLoading(false);
        //     setLatestResponseStatusCode(200);
        //     setLatestResponseBody({ success: true, data: [] });
        //     setIsResultValidJson(true);
        // }, 1000);
        console.log(apiModel, 'apimi');
        close(apiModel);
    };

    const cancel = () => {
       close()
    };

    const stringifyjson = (json) => {
        return JSON.stringify(json, null, 2);
    };

    const handleHeaderChange = (index, field, value) => {
        const updatedHeaders = [...apiModel.headers];
        updatedHeaders[index][field] = value;
        setApiModel({
            ...apiModel,
            headers: updatedHeaders
        });
    };

    const handleQueryParamChange = (index, field, value) => {
        const updatedParams = [...apiModel.queryParams];
        updatedParams[index][field] = value;
        setApiModel({
            ...apiModel,
            queryParams: updatedParams
        });
    };

    const handlePathParamChange = (index, field, value) => {
        const updatedParams = [...apiModel.pathParams];
        updatedParams[index][field] = value;
        setApiModel({
            ...apiModel,
            pathParams: updatedParams
        });
    };

    const handleAuthAttributesChange = (field, value) => {
        setApiModel({
            ...apiModel,
            authModel: {
                ...apiModel.authModel,
                authAttributes: {
                    ...apiModel.authModel.authAttributes,
                    [field]: value
                }
            }
        });
    };

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <div className={styles.methodSelect}>
                    <select
                        value={apiModel.executor_method_type}
                        onChange={(e) => setApiModel({...apiModel, executor_method_type: e.target.value})}
                        className={styles.select}
                    >
                        {httpOperations.map(op => (
                            <option key={op.name} value={op.name}>{op.name}</option>
                        ))}
                    </select>
                </div>
                <div className={styles.urlInput}>
                    <input
                        style={{width: "80%"}}
                        type="text"
                        placeholder="Enter the URL of API being tested"
                        value={apiModel.apiUrl}
                        onChange={(e) => setApiModel({...apiModel, apiUrl: e.target.value})}
                        onBlur={checkQueryParams}
                        onKeyUp={parameterDataCheck}
                        className={styles.input}
                    />
                </div>
                <div className={styles.actionButtons}>
                    <button onClick={emitData} className={globalStyles.mainButton} style={{width: "100%"}}>Save & Rerun Batch</button>
                    <button onClick={cancel} className={globalStyles.plainButton}  style={{width: "auto"}}>Cancel</button>
                </div>
            </div>

            <div className={styles.tabContainer}>
                <div className={styles.tabButtons}>
                    <button
                        className={`${styles.tabButton} ${activeTab === 'HEADERS' ? styles.activeTab : ''}`}
                        onClick={() => setActiveTab('HEADERS')}
                    >
                        HEADERS
                    </button>
                    <button
                        className={`${styles.tabButton} ${activeTab === 'AUTHORIZATION' ? styles.activeTab : ''}`}
                        onClick={() => setActiveTab('AUTHORIZATION')}
                    >
                        AUTHORIZATION
                    </button>
                    <button
                        className={`${styles.tabButton} ${activeTab === 'PARAMETERS' ? styles.activeTab : ''}`}
                        onClick={() => setActiveTab('PARAMETERS')}
                    >
                        PARAMETERS
                    </button>
                    <button
                        className={`${styles.tabButton} ${activeTab === 'REQUEST BODY' ? styles.activeTab : ''}`}
                        onClick={() => setActiveTab('REQUEST BODY')}
                    >
                        REQUEST BODY
                    </button>
                </div>

                <div className={styles.tabContent}>
                    {activeTab === 'HEADERS' && (
                        <div className={styles.section}>
                            <div className={styles.sectionHeader}>
                                <h3>Headers</h3>
                                <button onClick={addHeader} className={styles.iconButton}>+</button>
                            </div>
                            <table className={styles.table}>
                                <tbody>
                                {apiModel.headers.map((param, index) => (
                                    <tr key={index} className={styles.tableRow}>
                                        {headers.map(field => (
                                            <td key={field} className={styles.tableCell}>
                                                <div className={styles.inputGroup}>
                                                    {/*<label className={styles.inputLabel}>{field}</label>*/}
                                                    <input
                                                        type="text"
                                                        placeholder={field}
                                                        value={param[field]}
                                                        onChange={(e) => handleHeaderChange(index, field, e.target.value)}
                                                        className={styles.input}
                                                        list={field === 'key' ? 'headerTypes' : undefined}
                                                    />
                                                    {field === 'key' && (
                                                        <datalist id="headerTypes">
                                                            {['Accept', 'Accept-Encoding', 'Authorization', 'Cache-Control', 'Content-Transfer-Encoding', 'Date'].map(option => (
                                                                <option key={option} value={option} />
                                                            ))}
                                                        </datalist>
                                                    )}
                                                </div>
                                            </td>
                                        ))}
                                        <td className={styles.tableCell}>
                                            <button onClick={() => removeHeader(index)} className={styles.removeButton}>×</button>
                                        </td>
                                    </tr>
                                ))}
                                </tbody>
                            </table>
                        </div>
                    )}

                    {activeTab === 'AUTHORIZATION' && (
                        <div className={styles.section}>
                            <div className={styles.formGroup}>
                                <label className={styles.label}>Type of Authorization</label>
                                <select
                                    value={apiModel.authModel.authType}
                                    onChange={(e) => setApiModel({...apiModel, authModel: {...apiModel.authModel, authType: e.target.value}})}
                                    className={styles.select}
                                >
                                    {authModels.map(model => (
                                        <option key={model.value} value={model.value}>{model.display_name}</option>
                                    ))}
                                </select>
                            </div>

                            {apiModel.authModel.authType === 'no_auth' && (
                                <p className={styles.note}>This request does not use any authorization.</p>
                            )}

                            {apiModel.authModel.authType === 'bearer_token' && (
                                <div className={styles.formGroup}>
                                    <label className={styles.label}>Bearer Token</label>
                                    <input
                                        type="text"
                                        value={apiModel.authModel.authAttributes.token || ''}
                                        onChange={(e) => handleAuthAttributesChange('token', e.target.value)}
                                        className={styles.input}
                                    />
                                </div>
                            )}

                            {apiModel.authModel.authType === 'api_key' && (
                                <>
                                    <div className={styles.formGroup}>
                                        <label className={styles.label}>API Key</label>
                                        <input
                                            type="text"
                                            value={apiModel.authModel.authAttributes.key || ''}
                                            onChange={(e) => handleAuthAttributesChange('key', e.target.value)}
                                            className={styles.input}
                                        />
                                    </div>
                                    <div className={styles.formGroup}>
                                        <label className={styles.label}>API Key Value</label>
                                        <input
                                            type="text"
                                            value={apiModel.authModel.authAttributes.value || ''}
                                            onChange={(e) => handleAuthAttributesChange('value', e.target.value)}
                                            className={styles.input}
                                        />
                                    </div>
                                </>
                            )}

                            {apiModel.authModel.authType === 'basic_authentication' && (
                                <>
                                    <div className={styles.formGroup}>
                                        <label className={styles.label}>User Name</label>
                                        <input
                                            type="text"
                                            value={apiModel.authModel.authAttributes.username || ''}
                                            onChange={(e) => handleAuthAttributesChange('username', e.target.value)}
                                            className={styles.input}
                                        />
                                    </div>
                                    <div className={styles.formGroup}>
                                        <label className={styles.label}>Password</label>
                                        <input
                                            type="password"
                                            value={apiModel.authModel.authAttributes.password || ''}
                                            onChange={(e) => handleAuthAttributesChange('password', e.target.value)}
                                            className={styles.input}
                                        />
                                    </div>
                                </>
                            )}

                            {apiModel.authModel.authType === 'oauth_1_0' && (
                                <div className={styles.accordion}>
                                    <details>
                                        <summary>Advanced Options</summary>
                                        <div className={`${styles.formGroup} ${styles.formGroupWithMargin}`}>
                                            <label className={styles.label}>Signature Method</label>
                                            <select
                                                value={apiModel.authModel.authAttributes.signature || ''}
                                                onChange={(e) => handleAuthAttributesChange('signature', e.target.value)}
                                                className={styles.select}
                                            >
                                                {signatureMethods.map(method => (
                                                    <option key={method.value} value={method.value}>{method.display_name}</option>
                                                ))}
                                            </select>
                                        </div>
                                        <div className={`${styles.formGroup} ${styles.formGroupWithMargin}`}>
                                            <label className={styles.label}>Timestamp</label>
                                            <input
                                                type="text"
                                                value={apiModel.authModel.authAttributes.timestamp || ''}
                                                onChange={(e) => handleAuthAttributesChange('timestamp', e.target.value)}
                                                className={styles.input}
                                            />
                                        </div>
                                    </details>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'PARAMETERS' && (
                        <div className={styles.section}>
                            <div className={styles.accordion}>
                                <details>
                                    <summary className={styles.accordionHeader}>
                                        <span>Query Parameters</span>
                                        <button onClick={(e) => { e.preventDefault(); addParameter(); }} className={styles.iconButton}>+</button>
                                    </summary>
                                    <table className={styles.table}>
                                        <tbody>
                                        {apiModel.queryParams.map((param, index) => (
                                            <tr key={index} className={styles.tableRow}>
                                                {parameterHeaders.map(field => (
                                                    <td key={field} className={styles.tableCell}>
                                                        <div className={styles.inputGroup}>
                                                            <input
                                                                type="text"
                                                                placeholder={field}
                                                                value={param[field]}
                                                                onChange={(e) => handleQueryParamChange(index, field, e.target.value)}
                                                                className={styles.input}
                                                            />
                                                        </div>
                                                    </td>
                                                ))}
                                                <td className={styles.tableCell}>
                                                    <button onClick={() => removeParameter(index)} className={styles.removeButton}>×</button>
                                                </td>
                                            </tr>
                                        ))}
                                        </tbody>
                                    </table>
                                </details>
                            </div>

                            <div className={styles.accordion}>
                                <details>
                                    <summary className={styles.accordionHeader}>Path Parameters</summary>
                                    <table className={styles.table}>
                                        <tbody>
                                        {apiModel.pathParams.map((param, index) => (
                                            <tr key={index} className={styles.tableRow}>
                                                {parameterHeaders.map(field => (
                                                    <td key={field} className={styles.tableCell}>
                                                        <div className={styles.inputGroup}>
                                                            <input
                                                                type="text"
                                                                placeholder={field}
                                                                value={param[field]}
                                                                onChange={(e) => handlePathParamChange(index, field, e.target.value)}
                                                                className={styles.input}
                                                            />
                                                        </div>
                                                    </td>
                                                ))}
                                            </tr>
                                        ))}
                                        </tbody>
                                    </table>
                                </details>
                            </div>
                        </div>
                    )}

                    {activeTab === 'REQUEST BODY' && (
                        <div className={styles.section}>
                            <div className={styles.radioGroup}>
                                {requestBodyTypes.map(type => (
                                    <label key={type.value} className={styles.radioLabel}>
                                        <input
                                            type="radio"
                                            name="requestBodyType"
                                            value={type.value}
                                            checked={requestBodySelected === type.value}
                                            onChange={() => setRequestBodySelected(type.value)}
                                            className={styles.radioInput}
                                        />
                                        {type.displayName}
                                    </label>
                                ))}
                            </div>

                            {requestBodySelected === 'raw' && (
                                <div className={styles.rawBody}>
                                    <div className={styles.formGroup}>
                                        <select
                                            value={selectedRawType}
                                            onChange={(e) => setSelectedRawType(e.target.value)}
                                            className={styles.select}
                                        >
                                            {rawTypes.map(type => (
                                                <option key={type} value={type}>{type}</option>
                                            ))}
                                        </select>
                                    </div>

                                    {selectedRawType === 'JSON' && (
                                        <textarea
                                            value={apiModel.requestBody.value}
                                            onChange={(e) => setApiModel({...apiModel, requestBody: {...apiModel.requestBody, value: e.target.value}})}
                                            className={styles.jsonEditor}
                                            rows={10}
                                        />
                                    )}

                                    {selectedRawType === 'XML' && (
                                        <textarea
                                            value={apiModel.requestBody.value}
                                            onChange={(e) => setApiModel({...apiModel, requestBody: {...apiModel.requestBody, value: e.target.value}})}
                                            className={styles.xmlEditor}
                                            rows={10}
                                        />
                                    )}
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {isApiExecutedOnce && !apiExecutionLoading && (
                    <div className={styles.responseSection}>
                        <details open>
                            <summary className={styles.responseHeader}>
                                Response: {isApiExecuted && isResultValidJson && (
                                <span>Status code - {latestResponseStatusCode}</span>
                            )}
                            </summary>
                            <div className={styles.responseContent}>
                                {isApiExecuted && !isResultValidJson && (
                                    <span className={styles.note}>Invalid Response - Response must be a valid JSON.</span>
                                )}
                                {isApiExecuted && isResultValidJson && (
                                    <pre className={styles.jsonResponse}>{stringifyjson(latestResponseBody)}</pre>
                                )}
                            </div>
                        </details>
                    </div>
                )}

                {apiExecutionLoading && (
                    <div className={styles.loadingSpinner}>Loading...</div>
                )}
            </div>
        </div>
    );
};

export default APITester;
