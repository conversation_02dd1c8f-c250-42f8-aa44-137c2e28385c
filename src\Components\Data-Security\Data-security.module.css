.appContainer {
    display: flex;
    width: 100%;
    height: 100vh;
    justify-content: center;
}

.pageWrapper {
    width: 100%;
}

.mainSection {
    margin-left: 220px;
    padding: 20px 45px 20px 72px;
    flex: 1;
    transition: margin-left 0.3s ease;
    background-color: #FFFF;
    min-height: 100vh;
    overflow-x: hidden;
}

.mainSection.expanded {
    margin-left: 85px;
}

.responsive {
    width: 100%;
    box-sizing: border-box;
}

.headerContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 45px;
}

.summaryContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 45px;
}

.helpContainer {
    display: flex;
    align-items: center;
    margin-left: auto;
}

/* Content container */
.contentContainer {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 45px;
}

/* Tabs and controls alignment */
.tabsAndControlsContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

/* Ensure consistent alignment for all table elements */
.tableElementsContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

/* Table controls */
.tableControls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 0px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.tableWrapper {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 20px;
}

.tableContainer {
    width: 100%;
    min-width: 600px;
}

.filterContainer {
    position: relative;
    display: inline-block;
}

.filterDropdown {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 10;
    max-width: 200px;
    background-color: white;
    border: 1px solid #DCDAD9;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.scrollContainer {
    max-height: 250px;
    overflow-y: auto;
}

.filterOption {
    padding: 8px 12px;
    cursor: pointer;
    white-space: nowrap;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    transition: background-color 0.15s ease-in-out;
}

.filterOption:hover {
    background-color: #f2f2f2;
}

.buttonStyle {
    border: 1px solid #DCDAD9;
    padding: 5px 15px;
    border-radius: 3px;
    background-color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
}

.iconStyle {
    height: 24px;
    width: 24px;
    cursor: pointer;
}

/* Empty and No Data States */
.noDataContainer {
    width: 100%;
    height: 44px;
    justify-content: center;
    border: 1px solid #DCDAD9;
    background-color: #F8F8F7;
    display: flex;
    align-items: center;
    margin: 45px 0;
    box-sizing: border-box;
}

.fullWidth {
    width: 100% !important;
}

.noResults {
    text-align: center;
    padding: 45px;
    font-size: 16px;
    color: #746B68;
    background-color: #FFFFFF;
    border-bottom: 1px solid #E0E0E0;
    font-weight: 500;
    font-family: inter;
}

/* Tab styles */
.tab {
    background-color: #170903;
    display: flex !important;
    justify-content: stretch !important;
    margin-bottom: 20px;
}

/* Delete button */
.deleteButton {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
}

/* Data cell styles */
.dataCell {
    font-size: 12px;
    padding: 12px 16px;
    border-bottom: 1px solid #E0E0E0;
    color: #170903;
    font-family: Inter;
    font-weight: 400;
    line-height: 1.5;
}

/* Left alignment for headers and cells */
.leftAlignHeader {
    text-align: left !important;
}

.leftAlignCell {
    text-align: left !important;
}

/* Pagination styles */
.paginationContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    gap: 20px;
    flex-wrap: wrap;
}

.pageInfo,
.rowsPerPageContainer {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.pageButtons,
.rowsButtons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.pageButton,
.rowsButton,
.arrowButton {
    border: 1px solid #ddd;
    background-color: white;
    padding: 5px 10px;
    cursor: pointer;
    min-width: 30px;
    text-align: center;
    font-size: 14px;
}

.pageButton.active,
.rowsButton.active {
    background-color: #333;
    color: white;
}

.paginationText {
    color: #170903;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

.pageEllipsis {
    margin: 0 5px;
}


.dFlex {
    display: flex;
    align-items: center;
}

.centerText {
    text-align: center;
    justify-items: center;
}

.responsiveCell[data-label] {
    position: relative;
}

@media (max-width: 1024px) {
    .mainSection {
        padding: 20px 20px 20px 45px;
    }

    .tableControls {
        gap: 10px;
    }

    .filterText {
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@media (max-width: 768px) {
    .mainSection {
        margin-left: 70px;
        padding: 20px 10px 20px 20px;
    }

    .mainSection.expanded {
        margin-left: 0;
    }

    .responsive {
        padding-left: 10px;
        padding-right: 10px;
    }

    .tableControls {
        gap: 8px;
        margin-bottom: 15px;
    }

    .pageInfo,
    .rowsPerPageContainer {
        flex: 0 0 100%;
        justify-content: center;
        margin-bottom: 15px;
    }


    .tableContainer table {
        border: 0;
    }

    .tableContainer table thead {
        display: none;
    }

    .tableContainer table tr {
        display: block;
        margin-bottom: 10px;
        border: 1px solid #ddd;
    }

    .tableContainer table td {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: right;
        padding: 10px 15px;
        border-bottom: 1px solid #eee;
    }

    /* Override mobile text alignment for left-aligned cells */
    .tableContainer table td.leftAlignCell {
        text-align: left;
    }

    .tableContainer table td:last-child {
        border-bottom: 0;
    }

    .tableContainer table td::before {
        content: attr(data-label);
        font-weight: 600;
        text-align: left;
        padding-right: 10px;
        font-size: 12px;
    }

    .filterText {
        max-width: 60px;
    }
}

@media (max-width: 480px) {
    .buttonStyle {
        padding: 5px 8px;
    }

    .filterText {
        display: none;
    }

    .pageButton,
    .rowsButton,
    .arrowButton {
        padding: 5px 8px;
        min-width: 25px;
        font-size: 12px;
    }

    .pageButtons,
    .rowsButtons {
        gap: 3px;
    }
}