import React, { useEffect, useState } from "react"
import styles from "./Value-mapping.module.css"
import globalStyles from "../../../globalStyles.module.css"
import { HiArrowRight, HiArrowTurnDownRight, HiChevronDown, HiXMark, HiLink } from "react-icons/hi2"
import { SearchIcon } from "@heroicons/react/solid"
import { HiChevronUp, HiPlus } from "react-icons/hi"
import Dropdown from "../../Dropdown/Dropdown"
import RestoreDetailsModal from "../Restoredetails/Restoredetails"
import { Dialog, DialogContent } from "@mui/material"
import { toast } from "react-toastify"

export default function ValueMapping({
  close,
  attribute,
  selectedObjectData,
  selectedEntityData,
  sourceMapRes,
  source,
  target,
  targetData,
  targetExeRes,
  onSave,
}) {
  const [openGuide, setOpenGuide] = useState(false)
  const [openRestore, setOpenRestore] = useState(false)
  
  // Use cached mappings from attribute or initialize new mappings
  const [mainMappings, setMainMappings] = useState(() => {
    if (attribute?.mappings && Array.isArray(attribute.mappings) && attribute.mappings.length > 0) {
      return attribute.mappings.map((mapping) => ({
        ...mapping,
        sourcevalue: Array.isArray(mapping.sourcevalue) 
          ? mapping.sourcevalue.map(value => value === null ? "null" : value === "" ? '<"">' : value)
          : mapping.sourcevalue,
      }))
    }
    return [{
      sourcevalue: [],
      targetvalue: null, 
      subCategories: [],
    }]
  })

  const [expandedMappings, setExpandedMappings] = useState(() => {
    const length = attribute?.mappings?.length || 1
    return {
      main: Array(length).fill(false),
      subCategory: Array(length).fill([]).map(() => [])
    }
  })

  // Only update mappings if attribute content actually changes
  useEffect(() => {
    if (!attribute?.mappings) return

    const serializedNewMappings = JSON.stringify(attribute.mappings)
    const serializedCurrentMappings = JSON.stringify(mainMappings.map(m => ({
      ...m,
      sourcevalue: Array.isArray(m.sourcevalue) 
        ? m.sourcevalue.map(v => v === "null" ? null : v === '<"">' ? "" : v)
        : m.sourcevalue
    })))

    if (serializedNewMappings !== serializedCurrentMappings) {
      const updatedMappings = attribute.mappings.map((mapping) => ({
        ...mapping,
        sourcevalue: Array.isArray(mapping.sourcevalue)
          ? mapping.sourcevalue.map(value => value === null ? "null" : value === "" ? '<"">' : value)
          : mapping.sourcevalue,
      }))
      setMainMappings(updatedMappings)
      
      setExpandedMappings({
        main: Array(updatedMappings.length).fill(false),
        subCategory: Array(updatedMappings.length).fill([]).map(() => [])
      })
    }
  }, [attribute?.mappings])

  const toggleMainMapping = (index) => {
    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.main[index] = !newExpandedMappings.main[index]

    if (newExpandedMappings.main[index] && mainMappings[index].subCategories.length === 0) {
      const updatedMappings = [...mainMappings]
      updatedMappings[index].subCategories = [
        {
          sourcevalue: [],
          targetvalue: null,
          items: [],
        },
      ]
      setMainMappings(updatedMappings)

      if (!newExpandedMappings.subCategory[index]) {
        newExpandedMappings.subCategory[index] = []
      }
      newExpandedMappings.subCategory[index] = [false]
    }

    setExpandedMappings(newExpandedMappings)
  }
  const handleSave = () => {
    // Check if there's any mapping with source value but no target value
    const hasIncompleteMappings = mainMappings.some(
      (mapping) =>
        mapping.sourcevalue &&
        Array.isArray(mapping.sourcevalue) &&
        mapping.sourcevalue.length > 0 &&
        !mapping.targetvalue,
    )

    if (hasIncompleteMappings && (target.name === "Groups" || target.name === "Agents")) {
      toast.error("Please select target values.", {
        position: "top-right",
        autoClose: 3000,
      })
      return
    }

    const updatedMappings = mainMappings.map((mapping) => {
      const processedMapping = {
        ...mapping,
        sourcevalue: Array.isArray(mapping.sourcevalue)
          ? mapping.sourcevalue.map((value) => (value === "null" ? null : value === '<"">' ? "" : value))
          : mapping.sourcevalue,
      };

      if (mapping.subCategories && mapping.subCategories.length > 0) {
        processedMapping.subCategories = mapping.subCategories.map(subCategory => ({
          ...subCategory,
          sourcevalue: Array.isArray(subCategory.sourcevalue)
            ? subCategory.sourcevalue.map((value) => (value === "null" ? null : value === '<"">' ? "" : value))
            : subCategory.sourcevalue,
          items: subCategory.items && subCategory.items.length > 0
            ? subCategory.items.map(item => ({
                ...item,
                sourcevalue: Array.isArray(item.sourcevalue)
                  ? item.sourcevalue.map((value) => (value === "null" ? null : value === '<"">' ? "" : value))
                  : item.sourcevalue,
              }))
            : subCategory.items
        }));
      }

      return processedMapping;
    });

    onSave(updatedMappings, attribute, target.name);
    close();
  }

  const toggleSubCategory = (mainIndex, subIndex) => {
    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.subCategory[mainIndex][subIndex] = !newExpandedMappings.subCategory[mainIndex][subIndex]

    if (
      newExpandedMappings.subCategory[mainIndex][subIndex] &&
      mainMappings[mainIndex].subCategories[subIndex].items.length === 0
    ) {
      const updatedMappings = [...mainMappings]
      updatedMappings[mainIndex].subCategories[subIndex].items = [
        {
          sourcevalue: [],
          targetvalue: null,
        },
      ]
      setMainMappings(updatedMappings)
    }

    setExpandedMappings(newExpandedMappings)
  }

  const addNewMapping = () => {
    const updatedMappings = [
      ...mainMappings,
      {
        sourcevalue: [],
        targetvalue: null,
        subCategories: [],
      },
    ]
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.main.push(false)
    newExpandedMappings.subCategory.push([])
    setExpandedMappings(newExpandedMappings)
  }

  const addNewSubCategory = (mainIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories.push({
      sourcevalue: [],
      targetvalue: null,
      items: [],
    })
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.subCategory[mainIndex].push(false)
    setExpandedMappings(newExpandedMappings)
  }

  const addNewItem = (mainIndex, subIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].items.push({
      sourcevalue: [],
      targetvalue: null,
    })
    setMainMappings(updatedMappings)
  }

  const deleteMapping = (index) => {
    const updatedMappings = [...mainMappings]
    updatedMappings.splice(index, 1)
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.main.splice(index, 1)
    newExpandedMappings.subCategory.splice(index, 1)
    setExpandedMappings(newExpandedMappings)
  }

  const deleteSubCategory = (mainIndex, subIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories.splice(subIndex, 1)
    setMainMappings(updatedMappings)

    const newExpandedMappings = { ...expandedMappings }
    newExpandedMappings.subCategory[mainIndex].splice(subIndex, 1)
    setExpandedMappings(newExpandedMappings)
  }

  const deleteItem = (mainIndex, subIndex, itemIndex) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].items.splice(itemIndex, 1)
    setMainMappings(updatedMappings)
  }

  const handleSubCategorySourceValueChange = (mainIndex, subIndex, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].sourcevalue = newValue
    setMainMappings(updatedMappings)
  }

  const handleSubCategoryTargetValueChange = (mainIndex, subIndex, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].targetvalue = newValue
    if (updatedMappings[mainIndex].subCategories[subIndex].items?.length > 0) {
      updatedMappings[mainIndex].subCategories[subIndex].items = [
        {
          sourcevalue: [],
          targetvalue: null,
        },
      ]
    }

    setMainMappings(updatedMappings)
  }

  const handleItemSourceValueChange = (mainIndex, subIndex, itemIndex, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].items[itemIndex].sourcevalue = newValue
    setMainMappings(updatedMappings)
  }

  const handleItemTargetValueChange = (mainIndex, subIndex, itemIndex, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[mainIndex].subCategories[subIndex].items[itemIndex].targetvalue = newValue
    setMainMappings(updatedMappings)
  }

  const filterNulls = (arr) => {
    return arr.filter((item) => item !== null)
  }

  const handleOpen = () => {
    setOpenGuide(!openGuide)
  }

  const getValuesByColumnName = (arr, columnName, searchFieldName, returnFieldName) => {
    if (!arr) return []

    const columnObject = arr.find((obj) => obj[searchFieldName] === columnName)
    return columnObject ? columnObject[returnFieldName] : []
  }

  const getSourceMappingObjectKey = (sourceMappingConfig) => {
    return sourceMappingConfig?.sourceMappingObjectKey?.trim() || "ticket_fields"
  }

  const getSourceMappingFieldName = (sourceMappingConfig) => {
    return sourceMappingConfig?.fieldName?.trim() || "title"
  }

  const getChoiceMappingAttribute = (sourceMappingConfig) => {
    return sourceMappingConfig?.mappingAttribute?.trim() || "title"
  }

  const getValuesByColumnName1 = (arr, columnName, searchFieldName, returnFieldName) => {
    if (arr) {
      if (columnName !== "sub_category" && columnName !== "item_category") {
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === columnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        return columnObject ? columnObject[returnFieldName] : []
      } else if (columnName == "sub_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const sub_categories = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                sub_categories.push(options)
              }
            }
          }
        }
        return sub_categories
      } else if (columnName == "item_category") {
        const tempColumnName = "category"
        const columnObject = arr.find((obj) => {
          if (obj[searchFieldName] && typeof obj[searchFieldName] === "string") {
            if (obj[searchFieldName].toLowerCase() === tempColumnName.toLowerCase()) {
              return true
            }
          }
          return false
        })
        const choices = columnObject ? columnObject[returnFieldName] : []
        const item_category = []
        if (choices && choices.length > 0) {
          for (const item of choices) {
            if (item.nested_options && item.nested_options.length > 0) {
              for (const options of item.nested_options) {
                if (options.nested_options && options.nested_options.length > 0) {
                  for (const item1 of options.nested_options) {
                    item_category.push(item1)
                  }
                }
              }
            }
          }
        }
        return item_category
      }
    }
    return []
  }
  const getTargetFieldDropdownValues = (name, attribute) => {
    const searchFieldName = source.type === "api" || attribute.is_custom_field ? "name" : "label"

    if (name === selectedEntityData?.mainTarget[0]?.name && targetExeRes?.ticketFields) {
      return getValuesByColumnName1(targetExeRes.ticketFields, attribute.targetfield, searchFieldName, "choices")
    } else if (name && targetExeRes?.[name]) {
      return targetExeRes[name]
    }
    return []
  }

  const getDropdownOptions = (target, attribute) => {
    const groups = getTargetFieldDropdownValues(target.name, attribute)

    return groups.map((group) => ({
      label: getTargetMappingDisplayValue(group, target.name),
      value: group,
    }))
  }

  const getSubDropdownOptions = (attribute, index) => {
    if (attribute?.mappings?.[index]?.targetvalue?.nested_options) {
      const groups = attribute.mappings[index].targetvalue.nested_options || []
      return groups.map((group) => ({
        label: group?.value || "",
        value: group,
      }))
    }

    if (mainMappings[index]?.targetvalue?.nested_options) {
      const groups = mainMappings[index].targetvalue.nested_options || []
      return groups.map((group) => ({
        label: group?.value || "",
        value: group,
      }))
    }

    return []
  }

  const getItemDropdownOptions = (attribute, mainIndex, subIndex) => {
    if (attribute?.mappings?.[mainIndex]?.subCategories?.[subIndex]?.targetvalue?.nested_options) {
      return (
        attribute.mappings[mainIndex].subCategories[subIndex].targetvalue.nested_options?.map((group) => ({
          label: group.value,
          value: group,
        })) || []
      )
    }

    if (mainMappings[mainIndex]?.subCategories?.[subIndex]?.targetvalue?.nested_options) {
      return (
        mainMappings[mainIndex].subCategories[subIndex].targetvalue.nested_options?.map((group) => ({
          label: group.value,
          value: group,
        })) || []
      )
    }

    return []
  }

  const getTargetMappingDisplayValue = (group, name) => {
    if (!group) return null
    if (!name) return null

    const selectedObject = selectedEntityData?.mappingTargets?.find((obj) => obj.name === name)

    if (name === selectedEntityData?.mainTarget[0]?.name) {
      if (group.value) {
        return group.value
      } else {
        return group
      }
    } else if (selectedObject?.mappingDisplayField) {
      return group[selectedObject.mappingDisplayField]
    }

    return ""
  }

  const handleSourceValueChange = (index, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[index].sourcevalue = newValue
    setMainMappings(updatedMappings)
  }

  const handleTargetValueChange = (index, newValue) => {
    const updatedMappings = [...mainMappings]
    updatedMappings[index].targetvalue = newValue
    setMainMappings(updatedMappings)
  }
  const getTotalSourceCount = (attribute, name) => {
    if (!attribute) return 0

    if (source.type === "api") {
      if (target.name !== selectedEntityData.mainTarget[0].name) {
        return sourceMapRes[target.name]?.length || 0
      } else if (name === selectedEntityData.mainTarget[0].name) {
        return getSourceFieldDropdownValues(attribute, name).filter((item) => item !== '<"">' && item !== "null").length
      } else {
        const sourceMappingKey = getSourceMappingObjectKey()
        const fieldName = getSourceMappingFieldName()

        if (sourceMapRes[sourceMappingKey]?.length) {
          for (const field of sourceMapRes[sourceMappingKey]) {
            if (attribute.sourcefield === field[fieldName]) {
              return field.choices.length
            }
          }
        }
      }
    } else if (source.type === "csv") {
      return getSourceFieldDropdownValues(attribute).filter((item) => item !== '<"">' && item !== "null").length
    }

    return 0
  }
  const getTotalMappedCount = (mappings) => {
    if (mappings) {
      const filteredMappings = mappings.filter(
        (obj) =>
          Array.isArray(obj.sourcevalue) && obj.sourcevalue.some((value) => value !== '<"">' && value !== "null"),
      )

      return filteredMappings.reduce((acc, obj) => {
        const validValues = obj.sourcevalue.filter((value) => value !== '<"">' && value !== "null")
        return acc + validValues.length
      }, 0)
    }
    return 0
  }

  const openRestoreDialog = () => {
    setOpenRestore(true)
  }
  const buildTargetIndexMap = (targetData) => {
    const map = new Map()

    if (!Array.isArray(targetData)) {
      return map
    }

    targetData.forEach((target, index) => {
      const key = target.value?.toLowerCase() || target.name?.toLowerCase()
      if (key) {
        map.set(key, index)
      }
    })

    return map
  }

  const findMatchIndex = (name, options) => {
    if (!name) return -1

    const nameLower = name.toLowerCase()

    const optionsMap = new Map()

    for (let i = 0; i < options.length; i++) {
      const key = options[i].value?.toLowerCase() || options[i].name?.toLowerCase()
      if (key) {
        optionsMap.set(key, i)
      }
    }

    return optionsMap.has(nameLower) ? optionsMap.get(nameLower) : -1
  }

  const reMap = () => {
    const source = getSourceFieldDropdownValues(attribute, target.name)
    const targets = JSON.parse(JSON.stringify(getTargetFieldDropdownValues(target.name, attribute)))

    const newData = {}
    newData.attribute = { ...attribute }
    newData.attribute.mappings = []

    const targetMap = new Map()
    for (let i = 0; i < targets.length; i++) {
      const item = targets[i]
      const key = item.value?.toLowerCase() || item.name?.toLowerCase()
      if (key) {
        targetMap.set(key, { item, index: i })
      }
    }

    const usedIndices = new Set()
    for (const src of source) {
      if (!src) continue

      const srcLower = src.toLowerCase()
      const match = targetMap.get(srcLower)

      if (match && !usedIndices.has(match.index)) {
        usedIndices.add(match.index)
        newData.attribute.mappings.push({
          sourcevalue: [src],
          expand: false,
          subcategories: [
            {
              sourcevalue: "",
              targetvalue: "",
            },
          ],
          targetvalue: match.item,
        })
      }
    }

    return newData
  }

  const onRestore = () => {
    const newData = reMap()
    if (newData.attribute && newData.attribute.mappings) {
      setMainMappings([...newData.attribute.mappings])
    }
    setOpenRestore(false)

    return newData
  }

  const onClose = () => {
    setOpenRestore(false)
  }
  const getFieldByTargetField = (target, targetField) => {
    if (target && Array.isArray(target.fieldMappings)) {
      for (const item of target.fieldMappings) {
        if (item.targetfield === targetField) {
          return item
        }
      }
    }
    return ""
  }

  const getSourceFieldColumnValue = (target, targetField) => {
    return target?.fieldMappings?.find((item) => item.targetfield === targetField)?.sourcefield || ""
  }

  const getSubCategorySourceDropdownValues = (attribute, index, subCatName) => {
    if (source.type == "csv") {
      const temp = []
      const SubCategorySourceColumn = getSourceFieldColumnValue(target, subCatName)
      if (source.uniqueSourceValues) {
        const temp1 = source.uniqueSourceValues.find((col) => col.column === SubCategorySourceColumn)
        return temp1.values
      }
      return temp
    } else if (source.type == "api") {
      const SubCategoryObj = getFieldByTargetField(target, subCatName)
      return getSourceFieldDropdownValues(SubCategoryObj, target.name)
    }
  }

  const getSourceFieldDropdownValues = (attribute, name = "") => {
    if (source.type === "api") {
      const selectedObject = selectedEntityData?.mappingTargets?.find((obj) => obj.name === name)

      if (name === selectedEntityData?.mainTarget[0]?.name) {
        const sourceMappingKey = getSourceMappingObjectKey(
          selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
        )
        const choiceKey = getChoiceMappingAttribute(
          selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
        )
        const fieldName = getSourceMappingFieldName(
          selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
        )

        if (sourceMapRes[sourceMappingKey]?.length) {
          for (const field of sourceMapRes[sourceMappingKey]) {
            if (attribute.sourcefield === field[fieldName]) {
              return field.choices
                .map((choice) => choice[choiceKey])
                .sort()
                .concat(['<"">', "null"])
            }
          }
        }
      } else if (selectedObject?.sourceMappingDisplayField) {
        return [...sourceMapRes[target.name].map((obj) => obj[selectedObject?.sourceMappingDisplayField])]
          .sort()
          .concat(['<"">', "null"])
      }
    } else if (source.type === "csv" && attribute.mappingEnabled) {
      return getValuesByColumnName(source.uniqueSourceValues, attribute.sourcefield, "column", "values")
        .sort()
        .concat(['<"">', "null"])
    }

    return []
  }

  const getStatusIcon = (mappings, k) => {
    const existingGroup = checkExistingGroupSelected(mappings, k)

    if (existingGroup.status) {
      return (
        <div className={globalStyles.statusIconError}>
          <span className={globalStyles.iconText}>×</span>
        </div>
      )
    } else {
      return (
        <div className={globalStyles.statusIconSuccess}>
          <span className={globalStyles.iconText}>✓</span>
        </div>
      )
    }
  }
  const checkExistingGroupSelected = (mappings, k) => {
    // If no mappings or current mapping is invalid, return false status
    if (!mappings || !mappings[k] || !mappings[k].sourcevalue) {
      return { status: false, index: 0, value: "" }
    }

    // Skip empty or special values
    const currentValues = mappings[k].sourcevalue.filter(value => 
      value && value !== '<"">' && value !== "null"
    );

    // If no valid values to check, return false status
    if (currentValues.length === 0) {
      return { status: false, index: 0, value: "" }
    }

    // Check for duplicates in previous mappings
    for (const value of currentValues) {
      for (let i = 0; i < k; i++) {
        if (mappings[i]?.sourcevalue?.includes(value)) {
          return { status: true, index: i, value }
        }
      }
    }
    return { status: false, index: 0, value: "" }
  }

  const getSubCategoryTargetDropdownValues = (attribute, index) => {
    if (!attribute || !attribute.mappings || !attribute.mappings[index] || !attribute.mappings[index].targetvalue) {
      return []
    }
    return attribute.mappings[index].targetvalue.nested_options || []
  }

  return (
    <div style={{ height: "700px", padding: "0 0 20px 0", marginBottom: "150px" }}>
      <div className={styles.header}>
        <div style={{ backgroundColor: "#170903", padding: "10px 0" }}>
          <div className={styles.dFlex} style={{ alignItems: "center", justifyContent: "space-between" }}>
            <div className={globalStyles.headerStyle} style={{ paddingLeft: "45px" }}>
              Value Mapping
            </div>
            <div>
              <HiXMark
                style={{ fontSize: "16px", cursor: "pointer", padding: "0 24px", color: "#ffffff" }}
                onClick={close}
              />
            </div>
          </div>
        </div>

        {!openGuide ? (
          <div className={`${styles.mappingGuide} ${openGuide ? styles.mappingGuide : styles.closemap}`}>
            <div className={`${styles.dFlex} ${styles.closemg}`}>
              <span className={globalStyles.selectionName}>SEE MAPPING GUIDE</span>
              <HiChevronDown style={{ marginLeft: "auto" }} onClick={handleOpen} />
            </div>
          </div>
        ) : (
          <div className={` ${openGuide ? styles.mappingGuide : styles.closemap}`}>
            <div className={`${styles.dFlex} ${styles.closemg}`}>
              <span className={globalStyles.selectionName}>MAPPING GUIDE</span>
              <HiChevronUp style={{ marginLeft: "auto" }} onClick={handleOpen} />
            </div>
            <div className={globalStyles.poppinsStyle} style={{ fontWeight: "600", marginTop: "10px" }}>
              Hierarchy of data
            </div>
            <div className={styles.guideStyle}>
              Your data follows a 4-level nesting structure - <b>Field → Value → Sub-Category → Item.</b>
            </div>
            <div className={globalStyles.poppinsStyle} style={{ fontWeight: "600", marginTop: "10px" }}>
              Manual mapping{" "}
              <span>
                {" "}
                <HiLink className={globalStyles.closeIcon} />{" "}
              </span>
            </div>
            <div className={styles.guideStyle}>
              Map the associated values with the source and target fields you have already selected. Ensure each
              sub-category and item within each value is mapped appropriately.
            </div>
            <div className={globalStyles.poppinsStyle} style={{ fontWeight: "600", marginTop: "10px" }}>
              Automated mapping
            </div>
            <div className={styles.guideStyle}>
              Upload a CSV file with predefined mappings for bulk automation. Ensure the CSV follows the correct data
              hierarchy format.
            </div>
            <div>
              <div className={globalStyles.poppinsStyle} style={{ fontWeight: "600", marginTop: "10px" }}>
                Sample mapping
              </div>
              <div className={styles.dFlex} style={{ gap: "180px", margin: "20px 0" }}>
                <div className={styles.guideStyle}>Source field- Ticket category</div>
                <div className={styles.guideStyle}>Target field- Ticket category</div>
              </div>

              <div className={styles.mappingRow}>
                <div className={styles.mappingContent}>
                  <div className={styles.sourceContainer}>
                    <HiArrowTurnDownRight className={globalStyles.closeIcon} style={{ marginRight: "20px" }} />
                    <div className={styles.dropdownWrapper}>
                      <Dropdown
                        value={["Values- HR requests"]}
                        options={filterNulls(["Values- HR requests", "Values- IT Support", "Values- Facilities"])}
                        placeholder="Select Source"
                      />
                    </div>
                  </div>

                  <div className={styles.arrowContainer}>
                    <HiArrowRight />
                  </div>

                  <div className={styles.targetContainer}>
                    <Dropdown
                      value={["Values- HR"]}
                      options={["Values- HR", "Values- IT", "Values- Facilities"]}
                      placeholder="Select Target"
                    />
                  </div>
                </div>
              </div>

              <div className={styles.mappingRowIndented}>
                <div className={styles.mappingContent}>
                  <div className={styles.sourceContainer}>
                    <HiArrowTurnDownRight className={globalStyles.closeIcon} style={{ marginRight: "20px" }} />
                    <div className={styles.dropdownWrapper}>
                      <Dropdown
                        value={["Sub-category- Emp. Onboarding"]}
                        options={filterNulls([
                          "Sub-category- Emp. Onboarding",
                          "Sub-category- Benefits",
                          "Sub-category- Payroll",
                        ])}
                        placeholder="Select Sub-category"
                      />
                    </div>
                  </div>

                  <div className={styles.arrowContainer}>
                    <HiArrowRight />
                  </div>

                  <div className={styles.targetContainer}>
                    <Dropdown
                      value={["Sub-category- Onboarding"]}
                      options={["Sub-category- Onboarding", "Sub-category- Benefits", "Sub-category- Compensation"]}
                      placeholder="Select Target Sub-category"
                    />
                  </div>
                </div>
              </div>
              <div className={styles.mappingRowDoubleIndented}>
                <div className={styles.mappingContent}>
                  <div className={styles.sourceContainer}>
                    <HiArrowTurnDownRight className={globalStyles.closeIcon} style={{ marginRight: "20px" }} />
                    <div className={styles.dropdownWrapper}>
                      <Dropdown
                        value={["Item- Laptop issue"]}
                        options={filterNulls(["Item- Laptop issue", "Item- Software access", "Item- Email setup"])}
                        placeholder="Select Item"
                      />
                    </div>
                  </div>

                  <div className={styles.arrowContainer}>
                    <HiArrowRight />
                  </div>

                  <div className={styles.targetContainer}>
                    <Dropdown
                      value={["Item- Device request"]}
                      options={["Item- Device request", "Item- Software license", "Item- System access"]}
                      placeholder="Select Target Item"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div style={{ padding: "20px 45px" }}>
          <div style={{ display: "flex", gap: "50%", alignItems: "center" }}>
            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <div className={globalStyles.poppinsHeaderStyle}>Data Type</div>
              <div className={globalStyles.interSummaryStyle}>{target?.name || "Tickets"}</div>
            </div>

            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <div className={globalStyles.poppinsHeaderStyle}>Source field selected</div>
              <div className={globalStyles.interSummaryStyle}>{attribute.sourcefield}</div>
            </div>
          </div>
        </div>

        <div className={styles.statusCardsContainer}>
          <div className={`${styles.section} ${styles.leftSection}`}>
            <div className={`${styles.statusCard} ${styles.leftCard}`}>
              <div className={styles.countNumber}>{getTotalSourceCount(attribute, target.name)}</div>
              <div className={styles.countDescription}>Source values</div>
            </div>
          </div>
          <div className={`${styles.section} ${styles.rightSection}`}>
            <div className={`${styles.statusCard} ${styles.rightCard}`}>
              <div className={styles.countNumber}>{getTotalMappedCount(mainMappings)}</div>
              <div className={styles.countDescription}>Mapped values</div>
            </div>
            <div className={`${styles.statusCard} ${styles.rightCard}`}>
              <div className={styles.countNumber}>
                {getTotalSourceCount(attribute, target.name) - getTotalMappedCount(mainMappings)}
              </div>
              <div className={styles.countDescription}>Unmapped values</div>
            </div>
          </div>
        </div>
        <div className={styles.dFlex} style={{ padding: "0 45px", alignItems: "center", gap: "10px" }}>
          {/* <button
            className={`${styles.dFlex} ${styles.buttonStyle}`}
            style={{ gap: "10px", alignItems: "center", justifyContent: "center" }}
          >
            <HiArrowPath className={styles.iconStyle} />
            <span className={globalStyles.interStyle} onClick={openRestoreDialog}>
              Restore defaults
            </span>
          </button> */}
          <Dialog open={openRestore} maxWidth="md" fullWidth>
            <DialogContent style={{ padding: "0", overflow: "auto" }}>
              <RestoreDetailsModal onRestore={onRestore} onClose={onClose} />
            </DialogContent>
          </Dialog>
          {/* <button
            className={`${styles.dFlex} ${styles.buttonStyle}`}
            style={{ gap: "10px", alignItems: "center", justifyContent: "center" }}
          >
            <HiArrowUpTray className={globalStyles.closeIcon} />
            <span className={globalStyles.interStyle}>Upload pre-filled CSV</span>
          </button> */}
          <div className={globalStyles.searchWrapper} style={{ marginTop: "10px", marginLeft: "auto" }}>
            <SearchIcon className={globalStyles.searchIcon} />
            <input
              type="text"
              placeholder="Search..."
              className={globalStyles.searchInput}
              onFocus={(e) => {
                e.target.style.width = "200px"
                e.target.placeholder = "Typing..."
              }}
              onBlur={(e) => {
                e.target.style.width = "80px"
                e.target.placeholder = "Search..."
              }}
            />
          </div>
        </div>

        <div style={{ padding: "0 45px" }}>
          <table className={globalStyles.table} style={{ marginBottom: "20px", width: "100%" }}>
            <thead className={globalStyles.tableHeader}>
              <tr className={globalStyles.rowStyles}>
                <th className={globalStyles.headerCell}>Status</th>
                <th className={globalStyles.headerCell} style={{ width: "300px" }}>
                  Source values
                </th>
                <th className={globalStyles.headerCell} style={{ width: "300px" }}>
                  Target values
                </th>
                <th className={globalStyles.headerCell}>Mapping actions</th>
              </tr>
            </thead>
            <tbody>
              {mainMappings.map((mapping, index) => (
                <React.Fragment key={mapping.id}>
                  <tr className={globalStyles.tableRow}>
                    <td className={globalStyles.cell} style={{ width: "80px", textAlign: "center" }}>
                      {getStatusIcon(mainMappings, index)}
                    </td>
                    <td className={globalStyles.cell}>
                      <Dropdown
                        value={mapping.sourcevalue}
                        options={getSourceFieldDropdownValues(attribute, target.name)}
                        onChange={(newValue) => handleSourceValueChange(index, newValue)}
                        placeholder="Select Source"
                        multiSelect={true}
                      />
                    </td>
                    <td className={globalStyles.cell}>
                      <Dropdown
                        value={getTargetMappingDisplayValue(mapping.targetvalue, target.name)}
                        options={getDropdownOptions(target, attribute)}
                        onChange={(newValue) => handleTargetValueChange(index, newValue)}
                        placeholder="Select Target"
                        displayKey="label"
                        valueKey="value"
                      />
                    </td>
                    <td className={globalStyles.cell}>
                      <div>
                        {index >= 0 && (
                          <button onClick={() => deleteMapping(index)}>
                            <span>-</span>
                          </button>
                        )}
                        {target?.name === "Tickets" && attribute?.targetfield === "category" && (
                          <HiLink
                            style={{ marginLeft: "8px", cursor: "pointer" }}
                            onClick={() => toggleMainMapping(index)}
                          />
                        )}
                      </div>
                    </td>
                  </tr>

                  {expandedMappings.main[index] && mapping.subCategories.length > 0 && (
                    <tr>
                      <td colSpan="4" style={{ padding: 0 }}>
                        <div style={{ marginLeft: "40px" }}>
                          {mapping.subCategories.map((subCategory, subIndex) => (
                            <React.Fragment key={`${index}-${subIndex}`}>
                              <table style={{ marginBottom: "10px", width: "100%" }}>
                                <thead>
                                  <tr className={globalStyles.rowStyles}>
                                    <th className={globalStyles.headerCell}>Source sub-category</th>
                                    <th className={globalStyles.headerCell}>Target sub-category</th>
                                    <th className={globalStyles.headerCell}>Mapping actions</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr className={globalStyles.tableRow}>
                                    <td className={globalStyles.cell}>
                                      <Dropdown
                                        value={subCategory.sourcevalue}
                                        options={getSubCategorySourceDropdownValues(attribute, index, "sub_category")}
                                        onChange={(newValue) =>
                                          handleSubCategorySourceValueChange(index, subIndex, newValue)
                                        }
                                        placeholder="Select Source sub-category"
                                        multiSelect={true}
                                      />
                                    </td>
                                    <td className={globalStyles.cell}>
                                      <Dropdown
                                        value={subCategory?.targetvalue?.value}
                                        options={getSubDropdownOptions(attribute, index)}
                                        onChange={(newValue) =>
                                          handleSubCategoryTargetValueChange(index, subIndex, newValue)
                                        }
                                        placeholder="Select Target sub-category"
                                        displayKey="label"
                                        valueKey="value"
                                      />
                                    </td>
                                    <td className={globalStyles.cell}>
                                      <div>
                                        {subIndex >= 0 && (
                                          <button onClick={() => deleteSubCategory(index, subIndex)}>
                                            <span>-</span>
                                          </button>
                                        )}
                                        <HiLink
                                          style={{
                                            marginLeft: "8px",
                                            cursor: "pointer",
                                          }}
                                          onClick={() => toggleSubCategory(index, subIndex)}
                                        />
                                      </div>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>

                              {expandedMappings.subCategory[index][subIndex] && subCategory.items.length > 0 && (
                                <div style={{ marginLeft: "40px" }}>
                                  {subCategory.items.map((item, itemIndex) => (
                                    <div key={itemIndex}>
                                      <table
                                        style={{
                                          marginBottom: "10px",
                                          width: "100%",
                                        }}
                                      >
                                        <thead>
                                          <tr className={globalStyles.rowStyles}>
                                            <th className={globalStyles.headerCell}>Source item</th>
                                            <th className={globalStyles.headerCell}>Target item</th>
                                            <th className={globalStyles.headerCell}>Mapping actions</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          <tr className={globalStyles.tableRow}>
                                            <td className={globalStyles.cell}>
                                              <Dropdown
                                                value={item.sourcevalue}
                                                options={getSubCategorySourceDropdownValues(
                                                  attribute,
                                                  index,
                                                  "item_category",
                                                )}
                                                onChange={(newValue) =>
                                                  handleItemSourceValueChange(index, subIndex, itemIndex, newValue)
                                                }
                                                placeholder="Select Source items"
                                                multiSelect={true}
                                              />
                                            </td>
                                            <td className={globalStyles.cell}>
                                              <Dropdown
                                                value={item?.targetvalue?.value}
                                                options={getItemDropdownOptions(attribute, index, subIndex)}
                                                onChange={(newValue) =>
                                                  handleItemTargetValueChange(index, subIndex, itemIndex, newValue)
                                                }
                                                placeholder="Select Target items"
                                                displayKey="label"
                                                valueKey="value"
                                              />
                                            </td>
                                            <td className={globalStyles.cell}>
                                              <div>
                                                {itemIndex >= 0 && (
                                                  <button onClick={() => deleteItem(index, subIndex, itemIndex)}>
                                                    <span>-</span>
                                                  </button>
                                                )}
                                              </div>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  ))}

                                  <div style={{ marginBottom: "20px", display: "flex" }}>
                                    <button
                                      onClick={() => addNewItem(index, subIndex)}
                                      className={styles.buttonStyle}
                                      style={{ marginLeft: "auto" }}
                                    >
                                      <span
                                        className={globalStyles.interStyle}
                                        style={{
                                          display: "flex",
                                          justifyContent: "center",
                                          alignItems: "center",
                                          gap: "20px",
                                        }}
                                      >
                                        <HiPlus style={{ fontSize: "18px" }} /> Add item for mapping{" "}
                                      </span>
                                    </button>
                                  </div>
                                </div>
                              )}
                            </React.Fragment>
                          ))}

                          <div style={{ marginBottom: "20px", display: "flex" }}>
                            <button
                              onClick={() => addNewSubCategory(index)}
                              className={styles.buttonStyle}
                              style={{ marginLeft: "auto" }}
                            >
                              <span
                                className={globalStyles.interStyle}
                                style={{
                                  display: "flex",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  gap: "20px",
                                }}
                              >
                                <HiPlus style={{ fontSize: "18px" }} /> Add sub-category for mapping{" "}
                              </span>
                            </button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>

          <div className={styles.dFlex} style={{ alignItems: "center", justifyContent: "center", marginTop: "20px" }}>
            <button className={styles.buttonStyle} onClick={addNewMapping} style={{ padding: "10px" }}>
              <span
                className={globalStyles.interStyle}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: "20px",
                }}
              >
                <HiPlus style={{ fontSize: "18px" }} />
                Add value for mapping
              </span>
            </button>
          </div>
        </div>
        <div className={styles.buttonContainer}>
          <button className={styles.combineButton} onClick={handleSave}>
            <img src="/assets/check list.png" alt="Checklist Icon" className={styles.buttonIcon} />
            Save Changes
          </button>
        </div>
      </div>
    </div>
  )
}
