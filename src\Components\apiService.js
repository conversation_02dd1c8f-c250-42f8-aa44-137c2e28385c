import axios from 'axios';
import { BlobServiceClient } from "@azure/storage-blob";
import config from "../Config/config.json"
import { parseJsonFields } from "../Helper/helper";

const API_BASE_URL = 'https://dev-saasgenie.gateway.apiplatform.io';
const UPLOAD_URL = 'https://services.apiplatform.io/v1/data/dev-saasgenie/dev-saasgenie/uploadfiletopartneraccount?folder=dev-saasgenie';
const TRANSFORM_URL = 'https://transformer.haiva.ai';
const CORE_URL = 'https://core.gateway.apiplatform.io';
const httpOptions = {
    headers: {
        'Content-Type': 'application/json',
        'pkey': '3fde0cf904f891223fd5131bb989b49a',
        'apikey': '4VMvMYQGuZvedBaxUTISvh4vhKyyAq3Z',
    },
};
const coreOptions = {
    headers: {
        'Content-Type': 'application/json',
        'pkey': '3feee3f13ffecbc23fdcb845d0b9359a',
        'apikey': 'WroK6tWEFqf6lOtO7J1qJPXCZUc8Ai24',
        'Access-Control-Allow-Origin': '*',
        'observe': 'response'
    }
};

const DB_WRAPPER_API_URL = config.DB_WRAPPER_API_URL
const BUSINESS_LOGIC_API_URL = config.BUSINESS_LOGIC_API_URL
const httpOptions2 = {
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Basic YWRtaW5zZzpTZ3VzZXJANTQz',
        'Access-Control-Allow-Origin': '*',
        "ngrok-skip-browser-warning": "true" // To be removed when switching to actual middleware
    }
};

export const getMigrationProviders = async () => {
    try {
        const response = await axios.get(`${DB_WRAPPER_API_URL}/mgv2/migration_providers`, httpOptions2);
        let response_data = parseJsonFields(response.data)
        return response_data;
    } catch (error) {
        throw error;
    }
};

export const getWorkflow = async (id) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/v1/get-workflow-by-id?id=${id}`, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }
};
export const addWorkflow = async (payload) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/v1/add-workflow`, payload, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }
};
export const updateWorkflow = async (payload) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/v1/update-workflow`, payload, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getTargetConnection = async (target) => {
    try {
        const response = await axios.get(`${BUSINESS_LOGIC_API_URL}/get-target-connection-fields?target=${target}`, httpOptions2);
        let response_data = parseJsonFields(response.data);
        return response_data
    } catch (error) {
        throw error;
    }
};

export const executeApi = async (executor) => {
    const payload = { executor };
    try {
        const response = await axios.post(`${API_BASE_URL}/v1/execute-api`, payload, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const transformData = async (payload) => {
    try {
        const response = await axios.post(`${TRANSFORM_URL}/v1/data-transformation`, payload, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getMigrationById = async (email, migrationId) => {
    try {
        const response = await axios.get(`${TRANSFORM_URL}/v1/get-migrations-by-user-migration?executedBy=${email}&migrationId=${migrationId}`, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}

export const getBatchesByMigration = async (migrationId) => {
    try {
        const response = await axios.get(`${TRANSFORM_URL}/v1/get-batches-by-migration?migrationId=${migrationId}&showPartial=true`, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}


export const getSchedulersDetailsBatches = async (payload) => {
    try {
        const response = await axios.post(`${CORE_URL}/v1/get-scheduler-details`, payload, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}

export const retryPlanMigration = async (payload) => {
    try {
        const response = await axios.post(`${CORE_URL}/v1/retry-data-transformation-plan`, payload, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}



export const retryBatchMigration = async (payload) => {
    try {
        const response = await axios.post(`${CORE_URL}/v1/retry-data-transformation-batch`, payload, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}

export const checkBatchMetrics = async (migrationId, executedBy) => {
    try {
        const response = await axios.get(`${CORE_URL}/v1/update-migration-metrics?migrationId=${migrationId}&executesBy=${executedBy}`, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}



export const getRecordsByMigrationId = async (migrationId) => {
    try {
        const response = await axios.get(`${CORE_URL}/v1/get-records-by-migration?migrationId=${migrationId}`, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}
export const getRecordsByBatchId = async (batchId) => {
    try {
        const response = await axios.get(`${CORE_URL}/v1/get-records-by-batch?batchId=${batchId}`, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}


export const getRecordByBatchConversations = async (batchId, mainTargetId) => {
    try {
        const response = await axios.get(`${CORE_URL}/v2/get-records-by-batch-conversation?batchId=${batchId}&mainTargetId=${mainTargetId}`, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}

export const validateSource = async (payload) => {
    try {
        const response = await axios.post(`${BUSINESS_LOGIC_API_URL}/validate-source`, payload, httpOptions2)
        let response_data = parseJsonFields(response.data)
        return response_data
    } catch (error) {
        throw error
    }
}

export const validateSourceData = async (source, target, entity, payload) => {
    try {
        const response = await axios.post(`${BUSINESS_LOGIC_API_URL}/validate-source-data?source=${source}&target=${target}&entity=${entity}`, payload,
            httpOptions2
        )
        let response_data = parseJsonFields(response.data)
        return response_data
    } catch (error) {
        throw error
    }
}

export const saveMigrationPlan = async (payload) => {
    try {
        const response = await axios.post(`${BUSINESS_LOGIC_API_URL}/update-migration-plan`, payload, httpOptions2);
        let response_data = parseJsonFields(response.data)
        return response_data;
    } catch (error) {
        throw error;
    }
};

export const filterOptions = async (name) => {
    try {
        const response = await axios.get(`${DB_WRAPPER_API_URL}/mgv2/source_filter_options?name=${name}`, httpOptions2);
        let response_data = { "response": parseJsonFields(response.data) }
        // console.log("filterOptions: ", response_data)
        return response_data;
    } catch (error) {
        throw error;
    }
};

export const signUpOrSignInUser = async (operation, payload) => {
    try {
        const response = await axios.post(`${BUSINESS_LOGIC_API_URL}/login?operation=${operation}`, payload, httpOptions);
        return response.data;
    } catch (error) {
        throw error;
    }
};


export const getMigrationEntities = async (source, target) => {
    try {
        const getParams = {
            source,
            target
        }
        const response = await axios.get(
            `${DB_WRAPPER_API_URL}/public/migration_entities`,
            { ...httpOptions2, params: getParams }
        );
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getMigrationObjects = async (source, target) => {
    try {
        const getParams = {
            source,
            provider: target
        }
        const response = await axios.get(
            `${DB_WRAPPER_API_URL}/public/migration_sources`,
            { ...httpOptions2, params: getParams }
        );
        return response.data
    } catch (error) {
        throw error
    }
}

export const getMigrationTargetById = async (id) => {
    try {
        const response = await axios.get(
            `${DB_WRAPPER_API_URL}/public/migration_targets/${id}`,
            { ...httpOptions2 }
        );
        return response.data
    } catch (error) {
        throw error
    }
}

export const deleteAccount = async (emailId) => {
    try {
        const response = await axios.delete(
            `${BUSINESS_LOGIC_API_URL}/delete-account?email=${emailId}`,
            httpOptions2,
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const deleteConn = async (email, isTarget, isSource, providerName, accountEmail, domain) => {
    try {
        const url = `${BUSINESS_LOGIC_API_URL}/delete-connection?email=${email}&isTarget=${(isTarget).toString()}&isSource=${(isSource).toString()}&providerName=${providerName}&accountEmail=${accountEmail}&domain=${domain}`;
        console.log("Delete Connection URL:", url); // Log the URL for debugging
        const response = await axios.delete(
            url,
            httpOptions,
        )
        return response
    } catch (error) {
        throw error
    }
}

export const uploadToS3 = async (file) => {
    const formData = new FormData()
    formData.append("multipartFile", file)

    try {
        const response = await axios.post(UPLOAD_URL, formData, {
            headers: {
                pkey: "3fde0cf904f891223fd5131bb989b49a",
            },
        })
        return response.data
    } catch (error) {
        throw error
    }
}

export const uploadToAzureBlob = async (file) => {
    if (!file) throw new Error("No file provided");

    const SAS_URL = "https://saasgeniestorage.blob.core.windows.net/?sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2025-12-31T13:47:32Z&st=2025-06-02T05:47:32Z&spr=https,http&sig=3lxpy88Cx8o0JfTZDtYk54ESpIZElAmlCTmeZKkwh6s%3D";

    const blobServiceClient = new BlobServiceClient(SAS_URL);
    const containerClient = blobServiceClient.getContainerClient("migrategenie-v2-storage");

    const blobName = `${Date.now()}-${file.name}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    const uploadBlobResponse = await blockBlobClient.uploadBrowserData(file);

    return {
        azureBlobURL: blockBlobClient.url,
        requestId: uploadBlobResponse.requestId,
    };
};

export const retryRecordMigration = async (payload) => {
    try {
        const response = await axios.post(`${CORE_URL}/v1/retry-data-transformation-record`, payload, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }

}


export const getMigrationsByUser = async (executedBy, showPartial = true) => {
    try {
        const response = await axios.get(
            `https://transformer.haiva.ai/v1/get-migrations-by-user?executedBy=${executedBy}&showPartial=true`,
            httpOptions,
        )
        return response.data
    } catch (error) {
        throw error
    }
}


export const getMigrationsByUserMigration = async (executedBy, migrationId) => {
    try {
        const response = await axios.get(
            `${TRANSFORM_URL}/v1/get-migrations-by-user-migration?executedBy=${executedBy}&migrationId=${migrationId}`,
            httpOptions,
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const getMigrationPlans = async (executedBy) => {
    try {
        const response = await axios.get(
            `${DB_WRAPPER_API_URL}/mgv2/migration_templates?email_id=${executedBy}`,
            httpOptions2
        );
        let response_data = parseJsonFields(response.data);

        if (!response_data) return null;

        // Helper to resolve provider by id, returns null if not found
        const resolveProvider = async (id) => {
            if (!id) return null;
            try {
                return await getMigrationProvidersById(id);
            } catch {
                return null;
            }
        };

        // Normalize to array for unified processing
        const plans = Array.isArray(response_data) ? response_data : [response_data];

        // Use Promise.all to parallelize lookups for better performance
        await Promise.all(
            plans.map(async (item) => {
                if (item.migration_source && item.migration_source.length > 0) {
                    item.migration_source = await resolveProvider(item.migration_source[0]);
                }
                if (item.migration_target && item.migration_target.length > 0) {
                    item.migration_target = await resolveProvider(item.migration_target[0]);
                }
            })
        );

        // Return in the same structure as received
        return Array.isArray(response_data) ? plans : plans[0];
    } catch (error) {
        throw error;
    }
}

export const getMigrationProvidersById = async (id) => {
    try {
        const response = await axios.get(`${DB_WRAPPER_API_URL}/mgv2/migration_providers/${id}`, httpOptions2);
        let response_data = parseJsonFields(response.data)
        return response_data;
    } catch (error) {
        throw error;
    }
};

export const getMigrationPlanById = async (executedBy, id) => {
    try {
        const response = await axios.get(`${DB_WRAPPER_API_URL}/mgv2/migration_templates?email_id=${executedBy}&id=${id}`, httpOptions2)
        let response_data = parseJsonFields(response.data)
        return { response: response_data }
    } catch (error) {
        throw error
    }
}

export const getUniqueValuesFromCSV = async (payload) => {
    try {
        const response = await axios.post(`${BUSINESS_LOGIC_API_URL}/get-unique-values-from-csv`, payload, httpOptions2)
        let response_data = parseJsonFields(response.data)
        return response_data
    } catch (error) {
        throw error
    }
}

export const updateConnDetails = async (payload) => {
    const account = payload.accountEmail || payload.account || payload.account_email;
    const providerName = payload.providerName;
    const isTarget = (payload.isTarget).toString();
    const isSource = (payload.isSource).toString();
    const userEmail = payload.userEmail;
    const domain = payload.domain;

    const getParams = {
        accountEmail: account,
        userEmail,
        providerName,
        isSource,
        isTarget,
        domain
    };

    try {
        const getResponse = await axios.get(
            `${DB_WRAPPER_API_URL}/mgv2/migration_connections`,
            { ...httpOptions2, params: getParams }
        );

        const getBody = getResponse.data;
        // const accountEmail = payload.accountEmail;

        // Check if URL already exists in getBody
        if (Array.isArray(getBody) && getBody.length > 0) {
            return {
                status_code: 200,
                body: { message: 'URL already exists in connection details' }
            };
        }
        else {
            const postPayload = {
                data: {
                    accountEmail: account,
                    userEmail,
                    providerName,
                    isSource,
                    isTarget,
                    templateCount: 1,
                    domain
                    // connectedAt: Math.floor(Date.now() / 1000)
                }
            };
            console.log(postPayload)
            const postResponse = await axios.post(
                `${DB_WRAPPER_API_URL}/mgv2/migration_connections`,
                postPayload,
                httpOptions2
            );

            if ([200, 201].includes(postResponse.status)) {
                return {
                    status_code: 201,
                    body: { message: 'Connection detail created successfully' }
                };
            } else {
                return {
                    error_code: postResponse.status,
                    body: postResponse.data || {}
                };
            }
        }
        // else {
        //     return {
        //         error_code: getResponse.status,
        //         body: getBody,
        //     };
        // }
    } catch (error) {
        return {
            error_code: error.response?.status || 500,
            body: error.response?.data || { error: 'Unknown error' },
        };
    }
}

export const getConnDetails = async (email, isSource, isTarget) => {
    try {
        const response = await axios.get(`${DB_WRAPPER_API_URL}/mgv2/migration_connections?userEmail=${email}&isSource=${(isSource).toString()}&isTarget=${(isTarget).toString()}`, httpOptions2)
        return response.data
    } catch (error) {
        throw error
    }
}

export const deleteplan = async (planId) => {
    try {
        const response = await axios.delete(
            `${DB_WRAPPER_API_URL}/mgv2/migration_templates/${planId}`,
            httpOptions2
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const getUser = async (email) => {
    try {
        const response = await axios.get(
            `${BUSINESS_LOGIC_API_URL}/get-user-details?email_id=${email}`,
            httpOptions2
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const changePassword = async (email, method, password) => {
    try {
        const response = await axios.get(
            `${BUSINESS_LOGIC_API_URL}/change-password?email=${email}&method=${method}&password=${password}`,
            httpOptions2
        )
        return response.data
    } catch (error) {
        throw error
    }
}



export const getWorkflowErrorLog = async (errorId) => {
    try {
        const response = await axios.get(`${CORE_URL}/v1/get-workflow-error-log?errorId=${errorId}`, coreOptions);
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getBatchesById = async (batchId, showPartial = true) => {
    try {
        const response = await axios.get(
            `${TRANSFORM_URL}/v1/get-batches-by-id?batchId=${batchId}&showPartial=${showPartial}`,
            httpOptions,
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const getRecordsByBatch = async (batchId) => {
    try {
        const response = await axios.get(
            `${CORE_URL}/v1/get-records-by-batch?batchId=${batchId}`,
            coreOptions
        )
        return response.data
    } catch (error) {
        console.error("Error fetching records by batch:", error)
        throw error
    }
}



export const postDemo = async (payload) => {
    try {
        const response = await axios.post(
            `${BUSINESS_LOGIC_API_URL}/book-a-demo`, payload,
            httpOptions2
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const updateUser = async (email, payload) => {
    try {
        const response = await axios.patch(
            `${BUSINESS_LOGIC_API_URL}/update-user-detail?email_id=${email}`, { "data": payload },
            httpOptions2
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const postActivity = async (payload) => {
    try {
        const response = await axios.post(
            `${DB_WRAPPER_API_URL}/mgv2/activity_logs`,
            { data: payload },
            httpOptions2
        )
        return response.data
    } catch (error) {
        throw error
    }
}

export const getActivityLog = async (email) => {
    try {
        const response = await axios.get(
            `${DB_WRAPPER_API_URL}/mgv2/activity_logs?email=${email}`,
            httpOptions2
        );
        return response.data;
    } catch (error) {
        throw error;
    }
};

export const getTemplateLog = async (email) => {
    try {
        const response = await axios.get(
            `${BUSINESS_LOGIC_API_URL}/dashboard-templates?email_id=${email}`,
            httpOptions2
        );
        let response_data = parseJsonFields(response.data)
        return response_data;
    } catch (error) {
        throw error;
    }
};

export const migrationTemplate = {
    create: async (payload) => {
        try {
            const response = await axios.post(
                `${DB_WRAPPER_API_URL}/mgv2/migration_templates`,
                { data: payload },
                httpOptions2
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    },
    update: async (id, payload) => {
        try {
            const response = await axios.put(
                `${DB_WRAPPER_API_URL}/mgv2/migration_templates/${id}`,
                { data: payload },
                httpOptions2
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    },
    get: async (payload) => {
        try {
            const response = await axios.get(
                `${DB_WRAPPER_API_URL}/mgv2/migration_templates/${payload.id}`,
                httpOptions2
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

}