.dFlex {
  display: flex;
}

.section {
  /* flex: 1;
  width: 50%; */
  padding: 20px;
}

.sourceTargetGraphic {
  height: 400px;

  position: relative;
}

.entityRow {
  display: flex;
  align-items: center;
  justify-content: stretch;
}

.dependentContainer {
  display: flex;
  gap: 10px;
}

.entityBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  width: 150px;
  height: 150px;
  border: 1px solid #efeeed;
  padding: 10px;
  text-align: center;
  background-color: #fff;
  position: relative;
  margin: 15px 5px;
  cursor: pointer;
}

.entityBox:hover {
  background-color: #efeeed;
}

.entityBox:hover .entityName {
  background-color: #b9b5b3;
}

.entityImage {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.selectedEntityImage {
  width: 120px;
  height: 120px;
  object-fit: contain;
}

.selectedEntityBox {
  border: 2px solid #514742;
  background-color: #f8f8f7;
}

.entityBox:hover {
  background-color: #efeeed;
}

.entityBox:hover .entityName {
  background-color: #b9b5b3;
}

.selectedEntityContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 300px;
  height: 290px;
  border: 1px solid #efeeed;
  padding: 10px;
  text-align: center;
  background-color: #170903;
  position: relative;
  margin: auto;
  margin-top: 20px;
}

.selectedEntityContent {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: stretch;
}

.selecetdEntityName {
  width: 100%;
  background-color: #514742;
  color: #f8f8f7;
  height: 30px;
  text-align: center;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  bottom: 0;
  left: 0;
  font-family: Poppins;
  font-weight: 600;
  font-size: 14px;
}

.closeIcon {
  font-size: 20px;
  color: #170903;
  cursor: pointer;
}

.entityContent {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: stretch;
}

.dependentBox {
  width: 75px;
  height: 75px;
  background-color: #efeeed;
  border-radius: 5px;
}

.entityName {
  width: 100%;
  background-color: #efeeed;
  color: #514742;
  height: 30px;
  text-align: center;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  bottom: 0;
  left: 0;
  font-family: Poppins;
  font-weight: 600;
  font-size: 14px;
}

.selectedDep {
  font-size: 16px;
  font-weight: 400;
  font-family: Inter;
  margin-top: 5px;
  text-align: center;
  color: #170903;
}

.floatingButton {
  position: fixed;
  bottom: 20px;
  right: 20px;
  transition: all 0.3s ease-in-out;
}

.sourceTargetGraphic {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}


.sourceTargetContainer {
  position: relative;
  width: 100%;
}


.backgroundImage {
  width: 100%;
  height: auto;
  display: block;
}


.logoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45%;
  height: 30%;
  margin: auto;
  pointer-events: none;
}


.sourceLogoContainer,
.targetLogoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(252, 239, 234, 0.8);
  border-radius: 50%;
  width: 22%;
  aspect-ratio: 1/1;
  flex-shrink: 0;
  padding: 1%;
  box-sizing: border-box;
  pointer-events: auto;
}

/* Connection logos */
.connectionLogo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}


.connectionArrow {
  flex: 1;
  max-width: 40%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* padding: 0 5%; */
  pointer-events: auto;
}

@media screen and (max-width: 768px) {
  .logoOverlay {
    width: 70%;
    height: 35%;
  }

  .sourceLogoContainer,
  .targetLogoContainer {
    width: 25%;
  }
}

@media screen and (max-width: 480px) {
  .logoOverlay {
    width: 80%;
    height: 40%;
  }

  .sourceLogoContainer,
  .targetLogoContainer {
    width: 30%;
  }

  .connectionArrow {
    padding: 0 3%;
  }
}

@media screen and (max-width: 320px) {
  .logoOverlay {
    width: 90%;
  }

  .sourceLogoContainer,
  .targetLogoContainer {
    width: 35%;
  }

  /* .connectionArrow {
    padding: 0 0.5%;
  } */
}

.connectionArrow {
  position: relative;
  width: fit-content;
  /* margin: 0 auto; */
}

.arrowImage {
  width: 35px;
  height: auto;
  /* animation: pulseMove 3s ease-in-out infinite; */
}

@keyframes pulseMove {
  0% {
    transform: translateX(0) scale(1);
    opacity: 0.7;
  }

  50% {
    transform: translateX(5px) scale(1);
    opacity: 1;
  }

  100% {
    transform: translateX(20px) scale(1);
    opacity: 0.7;
  }
}

.selectedEntityCard {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  font-family: Poppins;
}

.selectedEntityImage {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.selectedEntityName {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  text-align: center;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
}

.modalBody {
  padding: 16px;
  font-family: Inter;
  color: #170903;
  font-size: 16px;
}

.modalFooter {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 16px;
}

.okButton {
  background-color: #EF8963;
  color: #000000;
  border: none;
  border-radius: 20px;
  padding: 10px 30px;
  font-weight: 500;
  cursor: pointer;
  font-family: Inter;
}

.cancelButton {
  background-color: #E6EDF8;
  color: #2B5282;
  border: none;
  border-radius: 20px;
  padding: 10px 30px;
  font-weight: 500;
  cursor: pointer;
  font-family: Inter;
}
.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #efeeed;
}

.modalHeader h3 {
  margin: 0;
  font-family: Poppins;
  font-weight: 600;
  color: #170903;
}

.modalBody {
  padding: 16px;
  font-family: Inter;
  color: #514742;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #efeeed;
}

.deleteIcon {
  color: #514742;
  cursor: pointer;
  font-size: 18px;
  transition: color 0.2s;
}

.deleteIcon:hover {
  color: #e53e3e;
}
