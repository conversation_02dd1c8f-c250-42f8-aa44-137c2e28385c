.sidebar {
    position: fixed;
    width: 215px;
    height: 100vh;
    background-color: #170903;
    color: #DCDAD9;
    padding: 15px;
    z-index: 1000;
    /*flex-direction: column; !* Stack items vertically *!*/
    /*justify-content: space-between; !* Space between the content and footer *!*/
    /*transition: width 0.3s ease, padding 0.3s ease;*/
    /*z-index: 1000; !* Ensure i*/
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
}

.logo {
    width: 28px;
    height: 28px;
    object-fit: contain;
}

.collapse-btn {
    background: none;
    border: none;
    color: #97908E;
    padding-bottom: 10px;
    cursor: pointer;
    /*border-radius: 8px;*/
}

/*.collapse-btn:hover {*/
/*    background-color: #374151;*/
/*}*/

.icon {
    width: 24px;
    height: 24px;
    color: #97908E;
    margin-right: 14px;
}

.icon-top {
    width: 24px;
    height: 24px;
    color: #97908E;
    margin-left: 10px;
    margin-bottom: 5px;
}

/*.sidebar-content {*/
/*    padding-left: 20px;*/
/*}*/

.menu-section {
    margin-bottom: 32px;
}

.section-title {
    color: #97908E;
    font-size: 11px;
    font-weight: 600;
    margin-bottom: 16px;
    /*padding: 0 12px;*/
    font-family: "poppins";
    line-height: 24px;
}

.menu-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 15px;
    margin-bottom: 4px;
    background: none;
    border: none;
    /*color: #9ca3af;*/
    cursor: pointer;
    /*justify-content: center;*/

}



.menu-text {
    color: #DCDAD9;
    font-family: "Inter";
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 16px 16px 40px 16px;

}

.user-profile {
    display: flex;
    align-items: center;
}


.user-info {
    margin-left: 12px;
}

.username {
    color: #EA5822;
    font-size: 16px;
    font-family: poppins;
    font-weight: 600;
    line-height: 24px;
}

.profile {
    width: 40px;
    height: 40px
}

.organization {
    color: #DCDAD9;
    font-size: 14px;
    font-family: Inter;
    font-weight: 400;
    line-height: 24px;
}

.hrline {
    border: 1px solid #4E443F;
    margin-bottom: 20px;
}

.hrsubline {
    border: 1px solid #4E443F;
}

.logo-migrategenie {
    width: 160px;
    height: 40px;
    margin-bottom: 15px;
    margin-left: 10px;
}

.submenu {
    position: absolute;
    background-color: #170903;
    border: 1px solid #4E443F;
    padding: 10px 15px;
    width: 177px;
    z-index: 1000;
}

.submenu-item {
    width: 100%;
    padding: 8px 16px;
    background: none;
    border: none;
    color: #DCDAD9;
    display: flex;
    justify-content: center;
    cursor: pointer;
}

.submenu-item:hover {
    background-color: #4E443F;
}

.hover-zoom:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease-in-out;
}

.menu-item-container {
    position: relative;
}

.tooltip {
    position: absolute;
    background-color: #170903;
    border: 1px solid #4E443F;
    padding: 8px 12px;
    color: #DCDAD9;
    font-family: "Inter";
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    border-radius: 4px;
    z-index: 1000;
    left: 65px;
    top: 0;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    pointer-events: none;
}