.container {
  display: flex;
  height: 100vh;
  background-color: #fff;
  overflow: hidden; /* Ensure container has no scroll */
  position: fixed; /* Use fixed positioning */
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.mainSection {
  margin-left: 240px;
  padding: 20px 45px 20px 58px;
  flex: 1;
  transition: margin-left 0.3s ease;
  background-color: #fff;
  overflow-y: auto;  /* Only allow vertical scrolling here */
  overflow-x: hidden;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}

.mainSection.expanded {
  margin-left: 110px;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.headerControls {
  display: flex;
  gap: 10px;
}

.statsContainer {
  display: flex;
  gap: 15px;
  width: 100%;
  margin-bottom: 20px;
}

.statItem {
  flex: 1;
  height: 78px;
  border-radius: 5px;
  border: 1px solid #ddd;
  padding: 15px 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.statNumber {
  font-size: 24px;
  font-weight: bold;
  color: #746b68;
  font-family: Poppins;
}

.statContent {
  display: flex;
  align-items: center;
  gap: 10px;
}

.statLabel {
  font-size: 14px;
  color: #170903;
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  white-space: nowrap;
}

.listOfTemplates {
  display: flex;
  padding: 7px 15px;
  justify-content: space-between;
  align-items: center;
  background: #170903;
  color: #ea5822;
  margin-bottom: 15px;
  font-family: Poppins;
}

.listOfTemplates h2 {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.unableToFind {
  display: flex;
  align-items: center;
  gap: 3px;
  background-color: #170903;
  color: #dcdad9;
  padding: 5px 10px;
  border-radius: 5px;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}

.tableControls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
}

.filterContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #514742;
  height: 38px;
  margin-bottom: 10px;
  white-space: nowrap;
}

.searchContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.searchInput {
  padding: 8px 12px;
  padding-right: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.tableWrapper {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

.templatesTable {
  width: 100%;
  border-collapse: collapse;
  font-family: "Poppins";
}

.templatesTable th {
  background-color: #f5f5f5;
  padding: 12px 16px;
  text-align: left;
  color: #746b68;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.templatesTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.rowSelect:hover {
  background-color: #dcdad9;
}

.sourceTarget {
  display: flex;
  align-items: center;
  gap: 8px;
}

.arrow {
  color: #746b68;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
}

.timelineDropdownContainer {
  position: relative;
}

.timelineArrow {
  width: 16px;
  height: 16px;
  cursor: pointer;
  transition: transform 0.2s ease;
  color: #746b68;
}

.timelineArrow.expanded {
  transform: rotate(180deg);
}

.timelineDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  width: 100%;
  min-width: 200px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timelineItem {
  padding: 4px 0;
  font-size: 14px;
  color: #333;
}

.timelineHeader {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.deleteButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
}

.deleteIcon {
  width: 18px;
  height: 18px;
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0 40px 0;
  gap: 60px;
}

.pageInfo,
.rowsPerPageContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.pageButtons,
.rowsButtons {
  display: flex;
  gap: 5px;
}

.pageButton,
.rowsButton {
  border: 1px solid #ddd;
  background-color: white;
  padding: 5px 10px;
  cursor: pointer;
  min-width: 30px;
  text-align: center;
  font-size: 14px;
}

.active {
  background-color: #333;
  color: white;
}

.editButton {
  margin-right: 15px;
}

.paginationText {
  color: #170903;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

.filterDropdownContainer {
  position: relative;
  display: inline-block;
  margin-right: 10px;
}

.filterDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 20;
  min-width: 200px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filterOption {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #170903;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
}

.filterOptionWithArrow {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #170903;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filterOption:hover,
.filterOptionWithArrow:hover {
  background-color: #f5f5f5;
}

.nestedDropdown {
  margin-left: 15px;
  border-left: 1px solid #e0e0e0;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-family: Inter;
  color: #746b68;
}

.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-family: Inter;
  color: #c30010;
}

.noDataCell {
  text-align: center;
  padding: 30px;
  color: #746b68;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.noDataMessage {
  color: #666;
  font-size: 14px;
  padding: 10px;
  text-align: center;
}

/* Delete Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.deleteModal {
  background-color: #170903;
  width: 100%;
  max-width: 600px;
  border-radius: 8px;
  overflow: hidden;
  color: white;
}

.deleteModalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.deleteModalHeader h3 {
  margin: 0;
  color: #ea5822;
  font-family: Poppins;
  font-size: 18px;
  font-weight: 600;
}

.closeModalButton {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
}

.deleteModalContent {
  padding: 0 20px 20px;
}

.deleteModalContent p {
  margin-bottom: 15px;
  font-family: Inter;
  font-size: 14px;
  line-height: 1.5;
  color: #f8f8f7;
}

.deleteConfirmButton {
  width: 100%;
  padding: 12px;
  background-color: #ef8963;
  color: #170903;
  border: none;
  border-radius: 4px;
  font-family: Inter;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 20px;
}

.dropdownArrow {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.platformDropdownContainer {
  position: relative;
  margin-right: 10px;
}

.platformButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  min-width: 150px;
}

.dropdownIcon {
  width: 16px;
  height: 16px;
  margin-left: 10px;
  transition: transform 0.3s ease;
}

.dropdownIcon.rotated {
  transform: rotate(180deg);
}

.platformDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 20;
  min-width: 200px;
  max-height: 250px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.platformOption {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.platformOption:hover {
  background-color: #f0f0f0;
}

.platformSelectorText {
  margin-left: 8px;
  font-weight: normal;
  color: #666;
}

.pageNavButton {
  border: 1px solid #ddd;
  background-color: white;
  padding: 5px 10px;
  cursor: pointer;
  min-width: 30px;
  text-align: center;
  font-size: 14px;
}

.pageNavButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageEllipsis {
  display: inline-block;
  padding: 5px;
  min-width: 30px;
  text-align: center;
}

.templateLink {
  color: #0096ff;
  text-decoration: none;
}

.templateLink:hover {
  text-decoration: underline;
}

.platformSelectorButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #514742;
  height: 38px;
  margin-bottom: 10px;
  white-space: nowrap;
}

.platformSelectorButton:hover {
  background-color: #f9f9f9;
}

/* Style for the chevron to keep it at the edge */
.chevron {
  display: inline-flex;
  transition: transform 0.2s ease;
}

.chevronUp {
  transform: rotate(180deg);
}

.chevronIcon {
  width: 16px;
  height: 16px;
}
