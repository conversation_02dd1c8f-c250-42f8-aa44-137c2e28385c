import { Client, Stomp } from '@stomp/stompjs';
import SockJ<PERSON> from 'sockjs-client';

class WebSocketService {
    constructor() {
        this.client = null;
        this.state = 'NOT CONNECTED';
        this.previouslyConnected = false;
        this.notificationCount = null;
        this.msg = [];
    }

    initializeKingerConnection(query) {
        this.disconnect();
        return new Promise((resolve, reject) => {
            const WEBSOCKET_URL = `wss://${process.env.REACT_APP_KINGER_HOST}/apiplatform-ws?${query}`;

            this.client = new Client({
                brokerURL: WEBSOCKET_URL,
                reconnectDelay: 5000,
                onConnect: () => {
                    this.previouslyConnected = true;
                    this.state = 'CONNECTED';
                    resolve('success');
                },
                onDisconnect: () => {
                    this.state = 'DISCONNECTED';
                },
                onStompError: () => {
                    reject('Connection error');
                }
            });
            this.client.activate();
        });
    }

    initializeWebSocketConnection() {
        this.disconnect();
        return new Promise((resolve, reject) => {
            const WEBSOCKET_URL = `wss://kinger-services.apiplatform.io/apiplatform-ws?environment=hari&emailId=<EMAIL>&password=gLK/FM4cUGeIJgUY/pCIgw==&shash=1f131b068dcda656593ff60b798aae3d&ralam=1bee0250f29652b7957547676d85f0b1`;

            this.client = new Client({
                brokerURL: WEBSOCKET_URL,
                reconnectDelay: 5000,
                onConnect: () => {
                    this.previouslyConnected = true;
                    this.state = 'CONNECTED';
                    resolve('success');
                },
                onDisconnect: () => {
                    this.state = 'DISCONNECTED';
                    reject('Failed to connect');
                }
            });
            this.client.activate();
        });
    }

    disconnect() {
        if (this.client) {
            this.client.deactivate();
        }
    }

    isOpen() {
        return this.client && this.client.connected;
    }

    listenAPICreation(stepId) {
        if (this.isOpen()) {
            return new Promise((resolve) => {
                this.client.subscribe(`/topic/${stepId}`, (message) => {
                    resolve(JSON.parse(message.body));
                });
            });
        }
        return null;
    }

    listenToTopic(queueName) {
        if (this.isOpen()) {
            return new Promise((resolve) => {
                this.client.subscribe(`/topic/${queueName}.response`, (message) => {
                    resolve(JSON.parse(message.body));
                });
            });
        }
        return null;
    }

    unsubscribeTopic(streamUrl) {
        this.client.unsubscribe(streamUrl);
    }

    unsubscribeAllTopics() {
        this.client.unsubscribeAll();
    }
}

export default new WebSocketService();
