.container {
  display: flex;
  height: 100vh;
  overflow: hidden;
  
  background: #f8f8f7;
}

.mainSection {
  margin-left: 220px;
  padding: 20px 45px 20px 72px;
  flex: 1;
  transition: margin-left 0.3s ease;
  background-color: #fff;
  height: 100vh;
  overflow-y: auto;
  scrollbar-width: none;  /* Firefox */
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  &::-webkit-scrollbar {  /* WebKit */
    display: none;
  }
}

.mainSection.expanded {
  margin-left: 85px;
}

/* .contentSection {
 padding-left: 70px;
 padding-right: 45px;
} */

.heroSection {
  display: flex;
  flex-wrap: wrap;
  background-color: #f8f8f8;
  padding: 40px 0 ;
  border-radius: 8px;
  margin-bottom: 30px;
  /* border: 3px solid #ccc; */
}

.heroHeader {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.headerControls {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}



.heroContent {
  flex: 1;
  max-width: 60%;
  padding: 40px;
}

.heroImage {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroImage img {
  height: 293.539px;
  flex: 1 0 0;
  aspect-ratio: 522.50/293.54;
  object-fit: contain;
}

.broughtBySection {
  margin-bottom: 15px;
  font-size: 14px;
  color: #514742;
  font-family: "Inter", sans-serif;
}

.saasGenieLink {
  color: #514742;
  text-decoration: none;
  transition: color 0.2s;
}

.saasGenieLink:hover {
  color: #ea5822;
  text-decoration: underline;
}

.mainTitle {
  font-size: 28px;
  font-weight: 600;
  color: #ea5822;
  margin-bottom: 15px;
  font-family: "Poppins", sans-serif;
}

.mainDescription {
  color: #170903;
  text-align: justify;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 20px;
  max-width: 600px;
}

.startButton,
.demoButton {
  background-color: #ef8963;
  color: #170903;
  border: none;
  padding: 10px 45px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  font-family: "Inter", sans-serif;
  transition: background-color 0.2s;
}

.featuresLayout {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  align-self: stretch;
  margin-bottom: 40px;
}

.iconsSection {
  width: 45%;
  padding: 20px;
}

.iconWrapper {
  position: relative;
  width: 100%;
  height: 400px;
}

.nanoIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background-color: #f8f8f8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.analyticsIcon {
  position: absolute;
  top: 10%;
  left: 15%;
  width: 100px;
  height: 100px;
  background-color: #f8f8f8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.lockIcon {
  position: absolute;
  bottom: 10%;
  left: 15%;
  width: 100px;
  height: 100px;
  background-color: #f8f8f8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.checkIcon {
  position: absolute;
  top: 40%;
  right: 10%;
  width: 100px;
  height: 100px;
  background-color: #f8f8f8;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.nanoIcon img,
.analyticsIcon img,
.lockIcon img,
.checkIcon img {
  width: 50%;
  height: 50%;
  object-fit: contain;
}

.keyFeaturesSection {
  width: 55%;
  padding: 0 0 0 20px;
}

.keyFeaturesTitle {
  color: #ea5822;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  font-family: "Poppins", sans-serif;
}

.featureItem {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.featureItem:last-child {
  border-bottom: none;
}

.featureItem h4 {
  font-size: 16px;
  font-weight: 500;
  color: #514742;
  margin-bottom: 5px;
  font-family: "Poppins", sans-serif;
}

.featureItem p {
  font-size: 14px;
  color: #514742;
  font-family: "Inter", sans-serif;
  line-height: 1.5;
}

.pricingSection {
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #eee;
  margin-bottom: 30px;
}

.pricingTitle {
  font-size: 24px;
  font-weight: 600;
  color: #ea5822;
  margin-bottom: 15px;
  font-family: "Poppins", sans-serif;
}

.pricingDescription {
  font-size: 16px;
  color: #170903;
  margin-bottom: 20px;
  line-height: 1.5;
  font-family: "Inter", sans-serif;
}

/* New Pricing Tables Container */
.pricingTablesContainer {
  display: flex;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 30px;
  /* padding: 20px;  */
}

/* Individual Pricing Table Container */
.pricingTableContainer {
  flex: 1;
  min-width: 250px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.pricingTableContainer:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

/* Selected Plan Styling */
.selectedPlan {
  background-color: #dcdad9;
  z-index: 2;
}

.selectedPlan .pricingTableHeader h3 {
  color: #000;
}

/* Pricing Table Header */
.pricingTableHeader {
  background-color: #f8f8f8;
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid #eee;
  transition: all 0.3s ease;
}

.pricingTableHeader h3 {
  margin: 0;
  color: #ea5822;
  font-size: 16px;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  transition: color 0.3s ease;
}

/* Pricing Table Content */
.pricingTableContent {
  padding: 15px;
}

.pricingTableRow {
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.pricingTableRow:last-child {
  border-bottom: none;
}

.pricingTableRow p {
  margin: 0;
  font-size: 14px;
  color: #514742;
  font-family: "Inter", sans-serif;
  line-height: 1.5;
}

/* Trusted Section Styles */
.trustedSection {
  padding: 40px 0;
  background-color: #f8f8f8;
  margin-top: 40px;
  border-radius: 8px;
}

.trustedHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.trustedLogo {
  width: 40px;
  height: 40px;
  margin-right: 15px;
}

.trustedTitle {
  font-size: 24px;
  font-weight: 500;
  color: #ea5822;
  font-family: "Poppins", sans-serif;
  margin: 0;
}

.trustedLogos {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.clientLogo {
  width: 120px;
  height: 80px;
  background-color: #eee;
  border-radius: 4px;
}

@media (max-width: 768px) {
  .heroSection {
    flex-direction: column;
  }

  .heroHeader {
    flex-direction: column;
    align-items: flex-start;
  }

  .headerControls {
    margin-top: 15px;
  }

  .heroContent {
    max-width: 100%;
    margin-bottom: 20px;
  }

  .featuresLayout {
    flex-direction: column;
  }

  .iconsSection,
  .keyFeaturesSection {
    width: 100%;
  }

  .iconWrapper {
    height: 300px;
  }

  .nanoIcon {
    width: 150px;
    height: 150px;
  }

  .analyticsIcon,
  .lockIcon,
  .checkIcon {
    width: 80px;
    height: 80px;
  }

  .pricingTablesContainer {
    flex-direction: column;
    gap: 20px;
  }

  .pricingTableContainer {
    width: 100%;
    border-radius: 8px;
  }

  .trustedLogos {
    gap: 15px;
  }

  .clientLogo {
    width: 100px;
    height: 60px;
  }
}

.calculatorContainer {
  margin-top: 40px;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.08);
  font-family: 'Poppins', sans-serif;
}

.calculatorSection {
  margin-bottom: 25px;
}

.calculatorLabel {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.calculatorInput {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  font-size: 14px;
  border-radius: 4px;
  border: 1px solid #ccc;
  margin-bottom: 20px;
}

.calculatorSubtext {
  font-size: 13px;
  color: #777;
  margin-top: -15px;
  margin-bottom: 15px;
}

.calculateButton {
  background-color: #f26a2e;
  color: white;
  padding: 10px 24px;
  font-weight: bold;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.resultsBox {
  margin-top: 30px;
  font-size: 16px;
  font-weight: 500;
}

.calculatorContainer {
  display: flex;
  flex-direction: column;
  /* adjust height as needed, or use max-height */
  height: 600px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.calculatorHeader {
  flex-shrink: 0;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 16px;
}

.calculatorBody {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}


.resultsBox {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.totalRecords,
.estimatedPrice {
  margin: 0;
}

