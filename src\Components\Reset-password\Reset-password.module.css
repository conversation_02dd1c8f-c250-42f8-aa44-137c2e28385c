.mainContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
}

.form-group-new {
  display: grid;
  grid-template-columns: 1fr;
  padding: 0px !important;
}

.signInContainer {
  display: flex;
  flex-direction: row;
  width: 100%;
  min-height: 600px;
  padding: 20px 0;
}

.leftContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px 40px;
}

.loginImage {
  height: 356.343px;
  align-self: stretch;
  aspect-ratio: 533.00/356.34;
  margin-top: 20px;
  object-fit: contain;
}

.rightSide {
  flex: 0.8;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
}

.container {
  width: 367px;
  background: #170903;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  padding: 35px 25px;
  text-align: center;
}

.buttonContainer {
  display: flex;
  justify-content: flex-end;
  /* margin-bottom: 15px; */
}

.description {
  color: #f8f8f7;
  font-family: "poppins", sans-serif;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
}

.passwordContainer {
  position: relative;
  width: 100%;
  padding-top: 10px;
}

.passwordInput {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
  font-family: "Inter";
  font-weight: 400;
  font-size: 16px;
}

.passwordToggle {
  position: absolute;
  right: -20px;
  top: 55%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #6c757d;
  font-size: 18px;
}

.passwordToggle:hover {
  color: #495057;
}

.passwordToggle:focus {
  outline: none;
}

.passwordRequirements {
  color: #b9b5b3;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  font-family: "Inter", sans-serif;
  text-align: left;
  margin-top: 10px;
  margin-bottom: 15px;
}

.successContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}

.successIcon {
  background-color: rgba(0, 128, 0, 0.1);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.checkIcon {
  color: green;
  width: 30px;
  height: 30px;
}

.successMessage {
  color: #f8f8f7;
  font-family: "Inter", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

/* Features Section Styles */
.featuresSection {
  width: 100%;
  padding: 0 20px;
  margin-top: 20px;
}

.featureItem {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.trustedBySection {
  width: 100%;
  margin-top: 30px;
  margin-bottom: 100px;
}

.trustedByContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.trustedByTitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  font-family: "Poppins", sans-serif;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .signInContainer {
    padding: 10px;
  }

  .leftContent {
    padding: 10px 20px;
  }

  .loginImage {
    height: 300px;
    aspect-ratio: 533.00/356.34;
  }
}

@media (max-width: 992px) {
  .container {
    width: 320px;
    padding: 25px 20px;
  }

  .loginImage {
    height: 250px;
    aspect-ratio: 533.00/356.34;
  }
}

@media (max-width: 768px) {
  .signInContainer {
    flex-direction: column;
    min-height: auto;
  }

  .leftContent {
    padding: 20px;
    align-items: center;
    text-align: center;
  }

  .rightSide {
    width: 100%;
    padding: 20px 10px 40px;
    align-items: center;
  }

  .container {
    width: 90%;
    max-width: 367px;
  }

  .featuresSection {
    padding: 0 10px;
  }
}

@media (max-width: 480px) {
  .container {
    width: 95%;
    padding: 25px 15px;
  }

  .loginImage {
    height: 200px;
    aspect-ratio: 533.00/356.34;
  }
}
