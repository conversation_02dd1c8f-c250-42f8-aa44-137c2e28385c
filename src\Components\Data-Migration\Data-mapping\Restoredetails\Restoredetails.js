import React from "react";
import { IoMdClose } from "react-icons/io";
import { IoMdRefresh } from "react-icons/io";
import "./Restoredetail.modal.css";

const RestoreDetailsModal = ({ onClose, onRestore }) => {
    return (
        <div className="modalOverlay">
            <div className="modalContainer">
                <IoMdClose className="closeIcon" onClick={onClose} />
                <div className="modalHeader" style={{marginBottom: "20px"}}>

                  <span className="modalTitle">
                    Do you want to restore to default mapping settings?
                  </span>

                </div>
                {/* <button className="restoreButton" onClick={onRestore}>
                    <IoMdRefresh className="refreshIcon" />
                    <span className="restoreText">
                    Restore defaults
                  </span>
                </button> */}
            </div>
        </div>
    );
};

export default RestoreDetailsModal;
