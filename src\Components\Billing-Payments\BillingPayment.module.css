.main-section {
    margin-left: 220px;
    padding: 20px 45px 20px 72px;
    flex: 1;
    transition: margin-left 0.3s ease;
    background-color: #FFFF;
    height: 100vh;
}

.main-section.expanded {
    margin-left: 85px;
}

.dFlex {
    display: flex;
    align-items: center;
}
.centerAlignedCell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}
.buttonStyle {
    border: 1px solid #DCDAD9;
    padding: 5px 15px;
    border-radius: 3px;
    background-color: white;
    cursor: pointer;
}

.container {
    height: 48px;
    width: 100%;
    border: 1px solid #DCDAD9;
    padding: 10px 20px;
    border-radius: 5px;
    margin-top: 15px;
    display: flex;
    align-items: center;
    gap: 25px;
    justify-content: center;
}

.container:hover {
    background-color: #DCDAD9;
}

.iconStyle {
    height: 24px;
    width: 24px;
    cursor: pointer;
}

.boxName {
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #170903;
    line-height: 24px;
}

.tableContainer {
    overflow-x: auto;
    /*margin-bottom: 150px;*/
    padding: 0;
    margin-top: 20px;
}

.statusStyle {
    width: 50px;
    height: 28px;
    border-radius: 3px;
    padding-top: 2px;
    padding-right: 5px;
    padding-bottom: 2px;
    padding-left: 5px;
    gap: 10px;
}

/* Filter dropdown styles */
.filterDropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 180px;
    background-color: #FFF;
    border: 1px solid #EFEEED;
    border-radius: 3px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.filterOption {
    display: flex;
    padding: 5px 15px;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    background: #FFF;
    cursor: pointer;
    font-family: Inter;
    font-size: 14px;
    line-height: 24px;
    justify-content: space-between;
}

.filterOption:hover {
    background-color: #F5F5F5;
}

.activeFilter {
    background-color: #F9F9F9;
    font-weight: 500;
}

.checkIcon {
    width: 16px;
    height: 16px;
    color: #4A4746;
}

/* Left alignment for specific table columns */
.leftAlignedHeader {
    text-align: left !important;
}

.leftAlignedCell {
    text-align: left !important;
}

/* Remove left padding from header to align with 45px gap */
.headerNoLeftPadding {
    padding-left: 0 !important;
}