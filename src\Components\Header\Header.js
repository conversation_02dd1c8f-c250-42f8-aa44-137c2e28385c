import React, {useState} from "react";
import "./Header.css"
import '@fortawesome/fontawesome-free/css/all.min.css';
import {Dialog, DialogContent} from "@mui/material";
import BookDemo from "../Book-Demo/BookDemo";


export default function Header(){
    const [openDemo, setOpenDemo] = useState(false);
    const bookDemo = () => {
        setOpenDemo(true);
    }
    const closeDemo = () => {
        setOpenDemo(false);
    }
    return (
        <div className="header">
            <img src="/assets/migrategenie.png" alt="Migrate Genie Logo" className="header-logo"/>
            <button className="demo-button" style={{marginLeft: 'auto'}} onClick={bookDemo}>
                {/*<img src="/assets/help.png" className="demo-icon"/>*/}
                <i className="fa fa-headset demo-icon"></i>
                Book a Demo
            </button>
            <Dialog open={openDemo}  maxWidth="md" PaperProps={{
                style: {
                    width: '700px',
                },
            }}>
                <DialogContent style={{padding: "0", overflow: "auto", backgroundColor: "#170903"}}>

                    <BookDemo close={closeDemo}/>
                </DialogContent>
            </Dialog>

        </div>



    )
}
