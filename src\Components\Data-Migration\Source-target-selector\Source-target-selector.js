import React, {useState} from "react";
import styles from "./Source-target-selector.module.css"
import {HiXMark} from "react-icons/hi2";
import { SearchIcon} from "@heroicons/react/solid";
import globalStyle from "../../globalStyles.module.css"

export default function SourceTargetSelector({ showModel, setShowModel, data, name, onSelect }) {
    const [selectedSource, setSelectedSource] = useState(null);
    const [searchText, setSearchText] = useState("");
    if (!showModel) return null;

    const handleSelect = (source) => {
        setSelectedSource(source);
    };

    const handleConfirm = () => {
        if (selectedSource) {
            onSelect(selectedSource.value);
            closeModal();
        }
    };

    const closeModal = () => setShowModel(false);


    return (
        <div className={styles.modalOverlay}>
            <div className={styles.modalContent}>
                <div style={{marginBottom: "20px"}}>
                    <button className={styles.closeBtn} onClick={closeModal}><HiXMark/></button>
                </div>
                <div className={styles.modalHeader}>
                    <h3 className={styles.modalTitle}>SELECT A {name}</h3>
                    <div className={globalStyle.searchBarContainer}>
                        <div>
                            <div className={globalStyle.searchWrapper}>
                                <SearchIcon className={globalStyle.searchIcon}/>
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    onChange={(event) => {setSearchText(event.target.value)}}
                                    className={globalStyle.searchInput}
                                    onFocus={(e) => {
                                        e.target.style.width = '150px';
                                        e.target.placeholder = 'Typing...';
                                    }}
                                    onBlur={(e) => {
                                        e.target.style.width = '80px';
                                        e.target.placeholder = 'Search...';
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className={styles.sourceIconsContainer}>
                    <div className={styles.sourceIcons}>
                        {data.filter(source => source.name.toLowerCase().includes(searchText.toLowerCase())).map((source, index) => (
                            <div
                                key={index}
                                className={`${styles.source} ${selectedSource === source ? styles.selected : ""}`}
                                onClick={() => handleSelect(source)}
                            >
                                <img src={source.src} alt={source.name}/>
                                <p className={`${styles.sourceName} ${selectedSource === source ? styles.selectedSourceName: ""}`}>{source.name}</p>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Button at bottom of dialog */}
                {selectedSource && (
                    <div className={styles.buttonContainer}>
                        <button style={{width: "100%"}}
                            className={globalStyle.mainButton}
                            onClick={handleConfirm}
                        >
                            Confirm
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
}
