{"name": "migrategenie-v2", "version": "0.1.0", "private": true, "dependencies": {"@azure/storage-blob": "^12.27.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@heroicons/react": "^1.0.6", "@intercom/messenger-js-sdk": "^0.0.14", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^6.4.11", "@mui/lab": "^6.0.0-beta.28", "@mui/material": "^6.4.11", "@mui/system": "^6.4.8", "@react-oauth/google": "^0.12.1", "@stomp/stompjs": "^7.0.1", "axios": "^1.8.1", "bootstrap": "^5.3.6", "buffer": "^6.0.3", "cra-template": "1.2.0", "crypto-js": "^4.2.0", "date-fns": "^2.29.3", "dotenv": "^16.5.0", "pdfmake": "^0.2.18", "react": "^18.2.0", "react-avatar-editor": "^13.0.2", "react-bootstrap": "^2.10.9", "react-datepicker": "^8.1.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^7.1.5", "react-scripts": "^5.0.1", "react-select": "^5.10.1", "react-select-async-paginate": "^0.7.9", "react-toastify": "^11.0.5", "realm-web": "^2.0.1", "recharts": "^2.15.1", "rxjs": "^7.8.2", "sockjs-client": "^1.6.1", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}