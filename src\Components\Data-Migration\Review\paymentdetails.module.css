.dFlex{
  display: flex;
  align-items: center;
}

.section {
  /* width: 50%;
  flex: 1; */
  margin: 20px 0;
  padding: 20px;
}

.targetGraphic{
  height: 400px;
  background-color: #F8F8F7;
  display: flex;
  justify-content: center;
  align-items: center;
}

.paymentImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.greyContainer {
  width: 450px;
  height: 250px;
  border-radius: 5px;
  background: #F8F8F7;
  padding: 50px;
  border: 1px solid #DCDAD9;
}

.paymentHeaderContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 12px;
}
 
.infoRow {
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 30px;
  margin: 24px 0px;
  justify-content: space-between;
}

.label {
  color: #746B68;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 600;
  line-height: 24px;
}

.value {
  color: #170903;
  font-family: Inter;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.downloadInvoice {
  height: 24px;
  padding: 0px 15px;
  border-radius: 3px;
  border: 1px solid #DCDAD9;
  background: #FFF;
  box-shadow: 2px 2px 5px 0px rgba(0, 0, 0, 0.10);
  cursor: pointer;
  color: #514742;
  text-align: center;
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
}