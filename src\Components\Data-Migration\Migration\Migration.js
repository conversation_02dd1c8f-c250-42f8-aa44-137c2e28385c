"use client"

import { useContext, useEffect, useRef, useState } from "react"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from "recharts"
import styles from "./Migration.module.css"
import Sidebar from "../../Sidebar/Sidebar"
import globalStyles from "../../globalStyles.module.css"
import { GlobeIcon, SearchIcon } from "@heroicons/react/solid"
import { HiOutlineDocumentDuplicate } from "react-icons/hi2"
import { Dialog, DialogContent } from "@mui/material"
import Feedback from "./Feedback/Feedback"
import { useLocation, useNavigate } from "react-router-dom"
import {
  addWorkflow,
  checkBatchMetrics,
  getBatchesByMigration,
  getMigrationById,
  getSchedulersDetailsBatches,
  postActivity,
  retryPlanMigration,
  transformData,
  updateWorkflow,
  migrationTemplate,
} from "../../apiService"
import { useDataService } from "../../dataService"
import websocketService from "../../websocketService"
import { switchMap, interval } from "rxjs"
import { useMongodb } from "../../mongodbService"
import LoaderSpinner from "../../loaderspinner"
import BatchResult from "./Batch-Result/Batch-Result"
import APITester from "../../ApiTester/ApiTester"
import { MigrationContext } from "../Data-Migration"
import { displayArticle } from "../../../Helper/helper"
import { toast, ToastContainer } from "react-toastify"
import "react-toastify/dist/ReactToastify.css"
import { create } from "@mui/material/styles/createTransitions"

export default function Migration() {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  var createdWorkflowId = null
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const { watchMigrations, migrationDataMongo, batchDataMongo, closeConnection } = useMongodb()
  const location = useLocation()
  const navigate = useNavigate()

  const { sample, sampleBatch, samplePayload, liveMigrationPayload } = location.state || {}
  const [isSample, setIsSample] = useState(sample)
  const [sampleBatchSize, setSampleBatchSize] = useState(sampleBatch)
  const [sampleTransformationPayload, setSampleTransformationPayload] = useState(samplePayload)

  const [migrationProgress, setMigrationProgress] = useState(25)
  const [timeElapsed, setTimeElapsed] = useState("")
  const [timeRemaining, setTimeRemaining] = useState("1 hour 04 minutes")
  const [currentPage, setCurrentPage] = useState(1)
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    return localStorage.getItem("isSidebarCollapsed") === "true"
  })
  const [isPaused, setIsPaused] = useState(false)
  const [isStopped, setIsStopped] = useState(false)
  const [sampleMigrationCompleted, setSampleMigrationCompleted] = useState(false)
  const [liveMigrationCompleted, setLiveMigrationCompleted] = useState(false)
  const [openBatchResult, setOpenBatchResult] = useState(false)
  const [openFeedback, setOpenFeedback] = useState(false)
  const [sampleMigrationRecordsData, setSampleMigrationRecordsData] = useState([])
  const timerRef = useRef(null)
  const apiCalledRef = useRef(false)
  const [batchData, setBatchData] = useState([])
  const [selectedBatchId, setSelectedBatchId] = useState(null)
  const [hasError, setHasError] = useState(false)
  const [schedulerIds, setSchedulerIds] = useState([])
  const apiSubscriptionRef = useRef(null)
  const [migrationData, setMigrationData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [openApiResult, setOpenApiResult] = useState(false)
  const chartContainerRef = useRef(null)
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  // New state for filtering
  const [activeFilter, setActiveFilter] = useState("all")
  const [filteredBatchData, setFilteredBatchData] = useState([])

  const [migrationSummary, setMigrationSummary] = useState({
    name: "",
    id: "",
    createdOn: "",
    startTime: "",
    endTime: "",
    description: "Your migration from Zendesk to Freshservice is in progress, transferring changes without notes.",
  })
  const [migrationStats, setMigrationStats] = useState({
    errors: 0,
    totalRecords: 0,
    migrated: 0,
    scheduled: 0,
  })
  const [executor, setExecutor] = useState({})
  const [tag, setTag] = useState("")

  // Define pieData inside the component
  const pieData =
    migrationStats.totalRecords === 0
      ? [{ name: "Empty", value: 1, color: "#f5f5f5" }]
      : [
          {
            name: "Migrated",
            value: migrationStats.migrated,
            color: "#EF8963",
          },
          { name: "Errors", value: migrationStats.errors, color: "#A43E18" },
          {
            name: "Skipped",
            value: migrationStats.totalRecords - migrationStats.migrated - migrationStats.errors,
            color: "#f5f5f5",
          },
        ]
  const filterOptions = [
    { id: "all", label: "See all" },
    { id: "noErrors", label: "Batches with no errors" },
    { id: "withErrors", label: "Batches with errors" },
  ]

  const handleFilterChange = (filterId) => {
    setActiveFilter(filterId)
    setCurrentPage(1)
  }

  const filterBatchData = () => {
    try {
      if (!batchData || !Array.isArray(batchData) || batchData.length === 0) {
        setFilteredBatchData([])
        return
      }

      let filtered = []
      switch (activeFilter) {
        case "noErrors":
          filtered = batchData.filter((batch) => batch.failed === 0)
          break
        case "withErrors":
          filtered = batchData.filter((batch) => batch.failed > 0)
          break
        case "all":
        default:
          filtered = [...batchData]
          break
      }

      setFilteredBatchData(filtered)
    } catch (error) {
      console.error("Error filtering batch data:", error)
      setFilteredBatchData([])
    }
  }

  useEffect(() => {
    filterBatchData()
  }, [activeFilter, batchData])

  const handleOpenDialog = (batch) => {
    setOpenBatchResult(true)
    setSelectedBatchId(batch.batchId)
    setHasError(batch.failed > 0 || batch.skipped > 0)
  }

  const handleCloseDialog = () => {
    setOpenBatchResult(false)
  }

  const handlePauseClick = () => {
    setIsPaused(!isPaused)
  }

  const handleStopClick = () => {
    setIsStopped(true)
  }

  const handleBackClick = () => {
    // Only store migration state if migration is not completed
    // This prevents completed migration state from interfering with new migrations
    if (!sampleMigrationCompleted && !liveMigrationCompleted) {
      localStorage.setItem("migrationProgress", String(migrationProgress))
      localStorage.setItem("migrationId", migrationSummary.id || "")
    } else {
      // Clear any stored migration state when coming back from completed migration
      localStorage.removeItem("migrationProgress")
      localStorage.removeItem("migrationId")
    }

    // Get the plan ID from the migration state or search params
    const planId = migrationState.planId || new URLSearchParams(location.search).get("plan_id") || new URLSearchParams(location.search).get("planId")

    // Create a new URL with the plan_id parameter, ensuring no duplicate parameters
    let newUrl = `/data-migration`
    if (planId) {
      newUrl += `?plan_id=${planId}`
    }

    navigate(newUrl, {
      state: {
        tab: isSample ? "3" : "5",
        fromMigration: true,
        templateName: migrationState.templateName,
        migrationData: {
          sampleBatchSize: sampleBatchSize,
          tag: tag || "",
          migrationSummary: migrationSummary,
          migrationProgress: sampleMigrationCompleted || liveMigrationCompleted ? 0 : migrationProgress, // Reset progress if completed
        },
      },
    })
  }

  const bottomRef = useRef(null)

  const scrollToBottom = () => {
    bottomRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  const validateMigration = [
    {
      step: 1,
      title: "Verify Migrated Data",
      tasks: [
        "Log in to Freshservice using your admin account.",
        "Navigate to Tickets > List.",
        "Filter by Migration Tag - Use the tag you assigned during migration to filter.",
        "Review Tickets - Ensure that all tickets and associated data have been migrated correctly.",
      ],
    },
    {
      step: 2,
      title: "Re-enable Notifications and Automations",
      tasks: [
        "Turn On Email Notifications - Navigate to Freshservice settings. Re-enable email notifications to agents and requesters. Refer to Configuring Email Notifications.",
        "Contact Freshworks support to re-enable SMTP.",
        "Contact Freshworks support to turn-on Freshworks org notifications.",
        "Turn-on any automations that were disabled prior to migration.",
      ],
    },
  ]

  const showPages = [5, 10, 15]
  const [itemsPerPage, setItemsPerPage] = useState(showPages[0])

  const totalPages = Math.ceil(filteredBatchData.length / itemsPerPage)
  const paginatedData = filteredBatchData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page)
    }
  }

  const handleShowPage = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }

  const getBatchNumber = (name) => {
    const match = name.match(/Batch (\d+)/)
    return match ? match[1] : ""
  }

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString()
  }

  useEffect(() => {
    const fetchData = async () => {
      if (
        sampleTransformationPayload &&
        !apiCalledRef.current &&
        isSample &&
        localStorage.getItem("sampleMigrationButtonClicked") === "true"
      ) {
        apiCalledRef.current = true
        const workflowPayload = {
          plan_name: migrationState.templateName,
          payload: sampleTransformationPayload,
          createdBy: email,
        }
        try {
          const workflowResp = await addWorkflow(workflowPayload)

          if (workflowResp && workflowResp.upsertedId) {
            createdWorkflowId = workflowResp.upsertedId
            setMigrationState((prevState) => ({
              ...prevState,
              workflowDetails: {
                workflowId: workflowResp.upsertedId,
              },
            }))

            const res = await transformData(sampleTransformationPayload)
            const item =
              res?.transformationPlanResults?.transformationResults?.[0]?.transformationResult?.migrationResults?.[0]
            setMigrationState((prevState) => ({
              ...prevState,
              workflowDetails: {
                workflowId: workflowResp.upsertedId,
                sampleTransRes: res,
                sampleRan: true,
              },
            }))

            localStorage.setItem("sampleMigrationButtonClicked", "false")
            if (item?.migrationId && res.statusCode === 200) {
              const updateWorkflowPayload = {
                id: createdWorkflowId,
                data: {
                  workflowRunId: item.migrationId,
                },
              }

              await updateWorkflow(updateWorkflowPayload)
              setLoading(false)
              getBatchDetails(item.migrationId)
            } else if (res.statusCode !== 200) {
              setLoading(false)
              setError(true)
              failedData(item?.migrationId)
              setExecutor(item.migrationResult.batchResults[0].sourceModel.sourceExecutor)
            }
          }
        } catch (error) {
          toast.error("Migration failed due to unexpected error", {
            position: "top-right",
          })
        }
      }
    }

    fetchData()
  }, [sampleTransformationPayload, isSample])

  const failedData = async (migrationID) => {
    const response = await getMigrationById(email, migrationID)
    setMigrationSummary((prevState) => ({
      ...prevState,
      name: response.name,
      id: response.migrationId,
      createdOn: formatTimestamp(response.startTime),
      startTime: formatTimestamp(response.startTime),
      endTime: "-",
    }))

    setMigrationProgress(100)
  }

  const updateMigrationDetails = async (migrationID) => {
    const response = await getMigrationById(email, migrationID)
    setMigrationSummary((prevState) => ({
      ...prevState,
      name: response.name,
      id: response.migrationId,
      createdOn: formatTimestamp(response.startTime),
      startTime: formatTimestamp(response.startTime),
      endTime: formatTimestamp(response.endTime),
    }))
  }

  useEffect(() => {
    const fetchData = async () => {
      if (
        !liveMigrationPayload ||
        apiCalledRef.current ||
        isSample ||
        localStorage.getItem("liveMigrationButtonClicked") !== "true"
      ) {
        return
      }

      apiCalledRef.current = true

      try {
        if (migrationState.workflowDetails.sampleRan && migrationState.workflowDetails.sampleTransRes) {
          const sampleTransRes = migrationState.workflowDetails.sampleTransRes

          liveMigrationPayload.transformationPlanId = sampleTransRes.transformationPlanId

          const transformResults = sampleTransRes.transformationPlanResults?.transformationResults
          if (transformResults && transformResults.length > 0) {
            liveMigrationPayload.transformationId = transformResults[0]?.transformationId

            const migrationResults = transformResults[0]?.transformationResult?.migrationResults
            if (migrationResults && migrationResults.length > 0) {
              liveMigrationPayload.migrationId = migrationResults[0].migrationId
            }
          }
        }

        const workflowPayload = {
          plan_name: migrationState.templateName,
          payload: liveMigrationPayload,
          createdBy: email,
        }

        const workflowResp = await addWorkflow(workflowPayload)
        if (!workflowResp || !workflowResp.upsertedId) {
          console.error("Failed to create workflow")
        }

        const workflowId = workflowResp.upsertedId
        createdWorkflowId = workflowId
        setMigrationState((prevState) => ({
          ...prevState,
          workflowDetails: {
            ...prevState.workflowDetails,
            workflowId,
          },
        }))

        const res = await transformData(liveMigrationPayload)
        const item =
          res?.transformationPlanResults?.transformationResults?.[0]?.transformationResult?.migrationResults?.[0]

        localStorage.setItem("liveMigrationButtonClicked", "false")

        if (item?.migrationId && res.statusCode === 200) {
          updateMigrationDetails(item?.migrationId)          // Add migration count tracking for live migration
          try {
            const planId = migrationState?.planId
            if (planId) {
              console.log("Plan ID for live migration:", planId)
              const oldPlanData = await migrationTemplate.get({ id: planId })
              const inJson = JSON.parse(oldPlanData.metadata)

              const countUpdatedMetadata = {
                ...inJson,
                totalMigrationCount: (inJson?.totalMigrationCount || 0) + 1,
              }
              await migrationTemplate.update(planId, {
                metadata: JSON.stringify(countUpdatedMetadata),
              })
            }
          } catch (error) {
            console.log("Error updating live migration count:", error)
          }

          const updateWorkflowPayload = {
            id: createdWorkflowId,
            data: {
              workflowRunId: item.migrationId,
            },
          }
          await updateWorkflow(updateWorkflowPayload)

          const activityPayload = {
            email: email,
            activity: "Live Migration started",
            // timestamp: Date.now(),
          }
          await postActivity(activityPayload)

          setLoading(false)
          await watchMigrations()
          await getWorkflowData(item.migrationId)
        } else if (res.statusCode !== 200) {
          setLoading(false)
          setError(true)
          failedData(item?.migrationId)
          setExecutor(item.migrationResult?.batchResults?.[0]?.sourceModel?.sourceExecutor)
        }
      } catch (error) {
        setLoading(false)
        setError(true)
        console.error("API Error:", error)
      }
    }

    fetchData()
  }, [liveMigrationPayload])

  useEffect(() => {
    if (batchDataMongo) {
      updateBatchData(batchDataMongo)
    }
  }, [batchDataMongo, liveMigrationPayload])

  useEffect(() => {
    if (migrationDataMongo) {
      updateMigrationData(migrationDataMongo)
    }
  }, [migrationDataMongo, liveMigrationPayload])

  const connectWithMongoDb = async () => {
    try {
      await watchMigrations()
    } catch (error) {
      throw error
    }
  }

  const updateBatchData = (data) => {
    if (batchData.length > 0 && data.fullDocument && data.fullDocument.batchId) {
      const index = batchData.findIndex((obj) => obj.batchId === data.fullDocument.batchId)

      if (index !== -1) {
        const updatedBatchData = [...batchData]
        updatedBatchData[index] = {
          ...updatedBatchData[index],
          scheduled: data.fullDocument.scheduled,
          passed: data.fullDocument.passed,
          skipped: data.fullDocument.skipped,
          failed: data.fullDocument.failed,
          status: data.fullDocument.status,
        }

        setBatchData(updatedBatchData)
      }
    }
  }

  const updateMigrationData = (data) => {
    if (migrationData && data.fullDocument && data.fullDocument.migrationId === migrationData.migrationId) {
      setMigrationStats((prevState) => ({
        ...prevState,
        migrated: data.fullDocument.passed,
        totalRecords: migrationData.totalRecord,
        errors: data.fullDocument.failed,
        scheduled: data.fullDocument.scheduled,
      }))
      if (data.fullDocument && data.fullDocument.status !== "completed") {
        setMigrationProgress((prev) => Math.min(prev + 2, 95))
      } else {
        setMigrationProgress(100)
        setLiveMigrationCompleted(true)
        const activityPayload = {
          email: email,
          activity: "Live Migration Completed",
          // timestamp: Date.now(),
        }
        postActivity(activityPayload)
        const timeDiff = data.fullDocument.endTime - data.fullDocument.startTime

        const totalSeconds = Math.floor(timeDiff / 1000)
        const hours = Math.floor(totalSeconds / 3600)
        const minutes = Math.floor((totalSeconds % 3600) / 60)
        const seconds = totalSeconds % 60

        let elapsedTime = ""
        if (hours > 0) elapsedTime += `${hours} hour${hours > 1 ? "s" : ""} `
        if (minutes > 0) elapsedTime += `${minutes} minute${minutes > 1 ? "s" : ""} `
        if (seconds > 0) elapsedTime += `${seconds} second${seconds > 1 ? "s" : ""}`
        setTimeElapsed(elapsedTime.trim())
      }
      setMigrationSummary((prevState) => ({
        ...prevState,
        name: migrationData.name,
        id: migrationData.migrationId,
        createdOn: formatTimestamp(migrationData.startTime),
        startTime: formatTimestamp(migrationData.startTime),
        endTime: formatTimestamp(data.fullDocument.endTime),
      }))
    }
  }

  const getMigrationHistory = (force = true) => {
    const promises = [useDataService.getMigrationHistoryV1(email, force)]

    Promise.all(promises)
      .then((resp) => {
        const temp = useDataService.migrationHistory

        if (temp) {
          const sortedData = [...temp].sort((a, b) => b.startTime - a.startTime)
        }
      })
      .catch((err) => {
        console.error("Error while fetching migration plans", err)
      })
  }

  const getBatches = async (migrationId) => {
    try {
      const res = await getBatchesByMigration(migrationId)

      setBatchData(res)
    } catch (err) {
      throw err
    }
  }

  const listenToTopic = (migrationId) => {
    websocketService.listenToTopic(migrationId).subscribe((data) => {
      if (data) {
        const result = JSON.parse(data.body)
        if (result) {
          getWorkflowBatchDetails(migrationSummary.id)
        }
      }
    })
  }

  const compareRecordsDetails = () => {
    if (migrationData) {
      let passedCount = 0
      let failedCount = 0
      let skippedCount = 0
      for (const batch of migrationData) {
        passedCount = batch["passed"] + passedCount
        failedCount = failedCount + batch["failed"]
        skippedCount = skippedCount + batch["skipped"]
      }
      setMigrationStats((prevState) => ({
        ...prevState,
        migrated: passedCount,
        totalRecords: passedCount + failedCount + skippedCount,
        errors: failedCount,
      }))
    }
  }

  const getWorkflowBatchDetails = (id) => {
    getBatchesByMigration(id)
      .then((res) => {
        const temp = res

        if (temp) {
          const newSchedulerIds = []
          const processedBatchData = temp.map((obj, index) => {
            if (obj.schedulerId && obj.schedulerId !== "") {
              if (!newSchedulerIds.includes(obj.schedulerId)) {
                newSchedulerIds.push(obj.schedulerId)
              }
            }
            return { ...obj, index: index + 1 }
          })

          setBatchData(processedBatchData)

          if (newSchedulerIds.length > 0) {
            getSchedulerDetailsforBatches()
          }
        }
      })
      .catch((err) => {
        console.error("Error fetching batch details:", err)
      })
  }

  const getSchedulerDetailsforBatches = async () => {
    if (schedulerIds && schedulerIds.length > 0) {
      const payload = { scheduler_ids: this.schedulerIds }

      try {
        const res = await getSchedulersDetailsBatches(payload).toPromise()
        if (res && res.length > 0) {
          setSchedulerIds(res)
        }
      } catch (error) {
        console.error("Error fetching scheduler details:", error)
      }
    }
  }

  const getWorkflowData = async (id) => {
    try {
      const res = await getMigrationById(email, id)
      const temp = res
      if (temp) {
        setMigrationData(temp)
        getWorkflowBatchDetails(id)

        if (temp.status === "started") {
          if (websocketService.isOpen()) {
            listenToTopic("migration:" + temp.migrationId)
          } else {
            try {
              await websocketService.initializeWebSocketConnection()
              if (websocketService.isOpen()) {
                listenToTopic("migration:" + temp.migrationId)
              }
            } catch (error) {
              console.error("WebSocket connection failed:", error)
            }
          }

          if (apiSubscriptionRef.current) {
            apiSubscriptionRef.current.unsubscribe()
          }

          apiSubscriptionRef.current = interval(180000)
            .pipe(switchMap(() => checkBatchMetrics(temp.migrationId, temp.executedBy)))
            .subscribe({
              next: (data) => {},
              error: (error) => {},
            })
        }
      }
    } catch (err) {
      console.error("Error while fetching migration plan", err)
    }
  }

  const handleClose = (model) => {
    setOpenApiResult(false)
  }

  useEffect(() => {
    const { sample, sampleBatch, samplePayload, liveMigrationPayload, tag: stateTag } = location.state || {}
    setIsSample(sample)
    setSampleBatchSize(sampleBatch)
    setSampleTransformationPayload(samplePayload)

    // Handle tag setting
    if (stateTag) {
      setTag(stateTag)
    } else if (location.state?.tag) {
      setTag(location.state.tag)
    } else {
      const savedTag = localStorage.getItem("migrationTag")
      if (savedTag) setTag(savedTag)
    }

    // Reset sample migration state when starting a new sample migration
    if (sample && samplePayload) {
      console.log("Starting new sample migration - resetting previous state")
      setSampleMigrationCompleted(false)
      setLiveMigrationCompleted(false)
      setMigrationProgress(25) // Reset to initial progress
      setError(false)
      setLoading(true)
      // Reset API call flag to allow new migration
      apiCalledRef.current = false

      // Clear any previous migration data
      setBatchData([])
      setMigrationStats({
        errors: 0,
        totalRecords: 0,
        migrated: 0,
        scheduled: 0,
      })
      setMigrationSummary({
        name: "",
        id: "",
        createdOn: "",
        startTime: "",
        endTime: "",
        description: "Your migration from Zendesk to Freshservice is in progress, transferring changes without notes.",
      })
    }
  }, [location.state])

  useEffect(() => {
    if (location.state && location.state.continueMigration) {
      const savedProgress = localStorage.getItem("migrationProgress")
      const savedMigrationId = localStorage.getItem("migrationId")

      if (savedProgress) {
        setMigrationProgress(Number(savedProgress))
      }

      if (savedMigrationId && savedMigrationId !== "") {
        getBatchDetails(savedMigrationId)
      }
    }
  }, [location.state])



  const getBatchDetails = async (migrationID) => {
    try {
      const response = await getMigrationById(email, migrationID)
      getBatches(response.executionDetails.migrationId)

      if (response && response.status !== "completed") {
        setMigrationSummary((prevState) => ({
          ...prevState,
          name: response.name,
          id: response.migrationId,
          createdOn: formatTimestamp(response.startTime),
          startTime: formatTimestamp(response.startTime),
          endTime: formatTimestamp(response.endTime),
        }))

        setMigrationStats((prevState) => ({
          ...prevState,
          migrated: response.passed,
          totalRecords: response.totalRecords,
          errors: response.failed,
          scheduled: response.scheduled,
        }))
        setMigrationProgress((prev) => Math.min(prev + 10, 95))

        setTimeout(() => getBatchDetails(migrationID), 2000)
      } else {
        setMigrationProgress(100)
        setSampleMigrationCompleted(true)

        const planId = new URLSearchParams(location.search).get("plan_id")

        const templateKey = `completedSampleMigration_${planId || "default"}`
        localStorage.setItem(templateKey, "true")

        const countKey = `sampleMigrationCount_${planId || "default"}`
        const currentCount = parseInt(localStorage.getItem(countKey) || "0")
        localStorage.setItem(countKey, (currentCount + 1).toString())

        const activityPayload = {
          email: email,
          activity: "Sample Migration completed",
          // timestamp: Date.now(),
        }
        postActivity(activityPayload)

        getBatches(response.executionDetails.migrationId)
        const timeDiff = response.endTime - response.startTime

        const totalSeconds = Math.floor(timeDiff / 1000)
        const hours = Math.floor(totalSeconds / 3600)
        const minutes = Math.floor((totalSeconds % 3600) / 60)
        const seconds = totalSeconds % 60

        let elapsedTime = ""
        if (hours > 0) elapsedTime += `${hours} hour${hours > 1 ? "s" : ""} `
        if (minutes > 0) elapsedTime += `${minutes} minute${minutes > 1 ? "s" : ""} `
        if (seconds > 0) elapsedTime += `${seconds} second${seconds > 1 ? "s" : ""}`
        setMigrationSummary((prevState) => ({
          ...prevState,
          name: response.name,
          id: response.migrationId,
          createdOn: formatTimestamp(response.startTime),
          startTime: formatTimestamp(response.startTime),
          endTime: formatTimestamp(response.endTime),
        }))

        setTimeElapsed(elapsedTime.trim())
        setMigrationStats((prevState) => ({
          ...prevState,
          migrated: response.passed,
          totalRecords: response.totalRecords,
          errors: response.failed,
          scheduled: response.scheduled,
        }))
      }
    } catch (error) {
      throw error
    }
  }

  const closeBatch = async (updateBatch) => {
    setOpenBatchResult(!openBatchResult)
    if (updateBatch) {
      if (migrationSummary.id) {
        try {
          const response = await getMigrationById(email, migrationSummary.id)
          if (response) {
            getWorkflowBatchDetails(migrationSummary.id)
            setMigrationStats((prevState) => ({
              ...prevState,
              migrated: response.passed,
              totalRecords: response.totalRecords,
              errors: response.failed,
              scheduled: response.scheduled,
            }))
          }
        } catch (error) {
          console.error("Error fetching migration data:", error)
        }
      }
    }
  }

  const getFailedBatchIds = () => {
    const temp = []
    if (batchData) {
      for (const batch of batchData) {
        if (batch.failed > 0) {
          temp.push(batch.batchId)
        }
      }
    }
    return temp
  }

  const retryAllBatch = async () => {
    const payload = {
      batchIds: getFailedBatchIds(),
    }

    try {
      await retryPlanMigration(payload)
      getWorkflowBatchDetails(migrationSummary.id)
    } catch (error) {
      getWorkflowBatchDetails(migrationSummary.id)
    }
  }

  const openApiResultDialaog = () => {
    setOpenApiResult(true)
  }

  useEffect(() => {
    if (migrationStats.totalRecords > 0) {
      const chartContainer = chartContainerRef.current
      if (chartContainer) {
        chartContainer.style.opacity = "0.99"
        setTimeout(() => {
          chartContainer.style.opacity = "1"
        }, 10)
      }
    }
  }, [migrationStats])

  const handlePlayVideo = () => {
    setIsVideoPlaying(true)
  }

  return (
    <div >
      <Sidebar isCollapsed={isSidebarCollapsed} setIsCollapsed={setIsSidebarCollapsed} />
      <div className={`${styles.mainSection} ${isSidebarCollapsed ? styles.expanded : ""}`}>
        <div className={`${styles.dFlex} ${styles.contentContainer}`} style={{ justifyContent: "space-between" }}>
          {isSample ? (
            <div className={globalStyles.headerStyle} style={{ paddingLeft: "0" }}>
              Sample Migration
            </div>
          ) : (
            <div className={globalStyles.headerStyle} style={{ paddingLeft: "0" }}>
              Live Migration
            </div>
          )}
          <div className={globalStyles.searchBarContainer}>
            <div className={globalStyles.searchBar}>
              {/* <div className={globalStyles.searchWrapper}>
                <SearchIcon className={globalStyles.searchIcon} />
                <input
                  type="text"
                  placeholder="Search..."
                  className={globalStyles.searchInput}
                  onFocus={(e) => {
                    e.target.style.width = "200px"
                    e.target.placeholder = "Typing..."
                  }}
                  onBlur={(e) => {
                    e.target.style.width = "80px"
                    e.target.placeholder = "Search..."
                  }}
                />
              </div> */}

              <div className={globalStyles.searchWrapper}>
                <GlobeIcon className={globalStyles.searchIcon} />
                <input
                  type="text"
                  placeholder="Eng"
                  className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                  readOnly
                />
              </div>
            </div>
          </div>
        </div>
        <div className={styles.progressBarContainer}>
          <div className={styles.progressBar}>
            <div
              className={styles.progressFill}
              style={{ width: `${liveMigrationCompleted || sampleMigrationCompleted ? 100 : migrationProgress}%` }}
            >
              {liveMigrationCompleted || sampleMigrationCompleted ? (
                <>100% Migration completed</>
              ) : (
                <>{migrationProgress}% Migration in process</>
              )}
            </div>
          </div>
        </div>

        {sampleMigrationCompleted || liveMigrationCompleted ? (
          <div className={styles.contentContainer}>
            <div className={styles.dFlex} style={{ justifyContent: "center", marginBottom: "15px", fontSize: "12px" }}>
              <div className={globalStyles.interStyle} style={{ fontSize: "12px" }}>
                Completed in {timeElapsed}
              </div>
            </div>
            <div
              className={styles.dFlex}
              style={{ justifyContent: "space-between", gap: "20px", marginBottom: "20px" }}
            >
              <div className={styles.dFlex} style={{ gap: "20px" }}>
                <button className={globalStyles.mainButton} style={{ margin: "0" }} onClick={handleBackClick}>
                  Back
                </button>
                <button className={styles.validateButton} onClick={scrollToBottom}>
                  Validate your migration in simple steps
                </button>
              </div>
              {/* Start Live Migration button is only shown after migration completion (sample or live) */}
              <button
                className={globalStyles.mainButton}
                style={{ margin: "0" }}
                onClick={() => {
                  // Get the plan ID from the migration state or search params
                  const planId = migrationState.planId || new URLSearchParams(location.search).get("plan_id") || new URLSearchParams(location.search).get("planId")

                  // Create a new URL with the plan_id parameter, ensuring no duplicate parameters
                  let newUrl = `/data-migration`
                  if (planId) {
                    newUrl += `?plan_id=${planId}`
                  }

                  navigate(newUrl, {
                    state: {
                      tab: "5",
                      isSample: false,
                      forceStep5: true
                    },
                  })
                }}
              >
                Start Live Migration
              </button>
            </div>
          </div>
        ) : (
          <div className={styles.contentContainer}>
            <div className={styles.dFlex} style={{ justifyContent: "flex-end", gap: "20px", marginBottom: "20px" }}>
              <button
                className={globalStyles.mainButton}
                style={{ margin: "0", gap: "10px" }}
                onClick={handleBackClick}
              >
                Back
              </button>
              <div style={{ marginLeft: "auto", display: "flex", gap: "20px" }}>
                {/* Start Live Migration button is hidden during sample migration and only shown after completion */}
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <div style={{ height: "50vh", display: "flex", justifyContent: "center", alignItems: "center" }}>
            <LoaderSpinner fullpage={false} />
          </div>
        ) : (
          <div>
            <hr className={styles.hr} />

            <div className={`${styles.dFlex} ${styles.contentContainer}`} style={{ gap: "20px", marginBottom: "30px" }}>
              <div className={styles.statsCard}>
                <div className={globalStyles.selectionName}>MIGRATION STATS</div>
                <div className={styles.statsContent}>
                  <div className={styles.metrics}>
                    <div
                      className={styles.statusBadge}
                      style={{
                        backgroundColor:
                          migrationStats.scheduled - migrationStats.migrated > 0 ? "#F3B8A3" : "#8BDF5559",
                      }}
                    >
                      {migrationStats.scheduled - migrationStats.migrated > 0 ? "Errors!" : "Success!"}
                    </div>
                    <div className={styles.metricItem}>
                      <div className={styles.metricNumber}>
                        {String(Number(migrationStats.errors || 0)).padStart(2, "0")}
                      </div>
                      <div className={styles.metricLabel}>
                        <span className={`${styles.dot} ${styles.templateDot}`}></span>
                        <span className={styles.dotName}>Records Failed</span>
                      </div>
                    </div>

                    <div className={styles.metricItem}>
                      <div className={styles.metricNumber}>{String(migrationStats.migrated).padStart(2, "0")}</div>
                      <div className={styles.metricLabel}>
                        <span className={`${styles.dot} ${styles.migrationDot}`}></span>
                        <span className={styles.dotName}>Total records being migrated</span>
                      </div>
                    </div>
                  </div>

                  <div className={styles.chartContainer}>
                    <ResponsiveContainer width="100%">
                      <PieChart>
                        <Pie
                          data={pieData}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={80}
                          paddingAngle={0}
                          dataKey="value"
                          startAngle={90}
                          endAngle={-270}
                        >
                          {pieData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>

              <div className={styles.summaryCard}>
                <div className={styles.dFlex} style={{ justifyContent: "space-between", marginBottom: "20px" }}>
                  <div className={globalStyles.selectionName}>MIGRATION SUMMARY</div>
                  <div style={{ display: "flex", gap: "20px", marginLeft: "auto" }}>
                    <div className={styles.dFlex}>
                      <img
                        src="/assets/help.png"
                        alt="Help"
                        className={globalStyles.helpIcon}
                        onClick={() => {
                          displayArticle("How to fix migration errors?")
                        }}
                      />
                      <span className={globalStyles.guideName}>How to fix migration errors?</span>
                    </div>
                  </div>
                </div>
                <div className={styles.summaryContent}>
                  <div className={styles.summaryRow}>
                    <span className={globalStyles.poppinsHeaderStyle}>Migration name</span>
                    <span className={globalStyles.interSummaryStyle}>{migrationSummary.name}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span className={globalStyles.poppinsHeaderStyle}>Migration ID</span>
                    <span className={globalStyles.interSummaryStyle}>{migrationSummary.id}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span className={globalStyles.poppinsHeaderStyle}>Migration created on</span>
                    <span className={globalStyles.interSummaryStyle}>{migrationSummary.createdOn}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span className={globalStyles.poppinsHeaderStyle}>Start time</span>
                    <span className={globalStyles.interSummaryStyle}>{migrationSummary.startTime}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span className={globalStyles.poppinsHeaderStyle}>End time</span>
                    <span className={globalStyles.interSummaryStyle}>{migrationSummary.endTime}</span>
                  </div>
                </div>
                {/*<div style={{marginTop: "15px"}}>*/}
                {/*    <div className={globalStyles.poppinsHeaderStyle}>Migration description</div>*/}
                {/*    <div className={globalStyles.interSummaryStyle}>{migrationSummary.description}</div>*/}
                {/*</div>*/}
              </div>
            </div>

            <hr className={styles.hr} />

            <div className={styles.contentContainer}>
              <div className={styles.dFlex} style={{ justifyContent: "space-between", marginBottom: "20px" }}>
                <div className={globalStyles.selectionName}>MIGRATION RESULT LOG</div>
                <div className={styles.dFlex}>
                  <img
                    src="/assets/help.png"
                    alt="Help"
                    className={globalStyles.helpIcon}
                    onClick={() => {
                      displayArticle("How to retry failed records without redoing the migration?")
                    }}
                  />
                  <span className={globalStyles.guideName}>
                    How to retry failed records without redoing the migration?
                  </span>
                </div>
              </div>

              <div className={styles.dFlex} style={{ gap: "10px", marginBottom: "20px" }}>
                <span className={globalStyles.interStyle} style={{ fontSize: "12px" }}>
                  Filter:
                </span>
                {filterOptions.map((option) => (
                  <button
                    key={option.id}
                    className={`${styles.filterButton} ${activeFilter === option.id ? styles.activeFilter : ""}`}
                    onClick={() => handleFilterChange(option.id)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>

              <div>
                <table className={globalStyles.table}>
                  <thead className={globalStyles.tableHeader}>
                    <tr className={globalStyles.rowStyles}>
                      <th className={globalStyles.headerCell}>Status</th>
                      <th className={globalStyles.headerCell}>Batch details</th>
                      <th className={globalStyles.headerCell}>Start time</th>
                      <th className={globalStyles.headerCell}>End time</th>
                      <th className={globalStyles.headerCell}>Total</th>
                      <th className={globalStyles.headerCell}>Migrated</th>
                      <th className={globalStyles.headerCell}>Failed</th>
                      <th className={globalStyles.headerCell}>See more</th>
                    </tr>
                  </thead>

                  {!error ? (
                    <tbody style={{ width: "100%" }}>
                      {paginatedData.map((batch) => {
                        // Determine status icon
                        let statusIcon = "⏳";
                        let statusClass = styles.inProgressIcon;
                        if (batch.status === "completed") {
                          statusIcon = "✓";
                          statusClass = styles.successIcon;
                        } else if (batch.status === "failed" || batch.failed > 0) {
                          statusIcon = "✗";
                          statusClass = styles.failedIcon;
                        }
                        // Calculate migrated and failed counts
                        const migrated = batch.passed || 0;
                        const failed = batch.failed || 0;
                        return (
                          <tr key={batch.batchId} className={globalStyles.tableRow}>
                            <td className={globalStyles.cell}>
                              <span className={statusClass}>{statusIcon}</span>
                            </td>
                            <td className={globalStyles.cell}>Batch #{getBatchNumber(batch.name)}</td>
                            <td className={globalStyles.cell}>{formatTimestamp(batch.startTime)}</td>
                            <td className={globalStyles.cell}>{formatTimestamp(batch.endTime)}</td>
                            <td className={globalStyles.cell}>{batch.totalRecords}</td>
                            <td className={globalStyles.cell}>{migrated}</td>
                            <td className={globalStyles.cell}>{failed}</td>
                            <td className={globalStyles.cell}>
                              {(batch?.additionalDetails?.statusCode || !batch?.additionalDetails) < 300 ? (
                                <HiOutlineDocumentDuplicate
                                  className={globalStyles.closeIcon}
                                  style={{ cursor: "pointer" }}
                                  onClick={() => handleOpenDialog(batch)}
                                />
                              ) : (
                                <HiOutlineDocumentDuplicate
                                  className={globalStyles.closeIcon}
                                  style={{ cursor: "pointer" }}
                                  onClick={() => {}}
                                />
                              )}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  ) : (
                    <tbody>
                      <tr>
                        <td colSpan={8}>
                          <div
                            className={globalStyles.interSummaryStyle}
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              justifyContent: "center",
                              alignItems: "center",
                              height: "100%",
                              textAlign: "center",
                              gap: "10px",
                              marginTop: "10px",
                            }}
                          >
                            No records fetched from Source.
                            <button className={globalStyles.mainButton} onClick={openApiResultDialaog}>
                              See details
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  )}
                </table>
              </div>

              <Dialog open={openBatchResult} maxWidth="md" fullWidth PaperProps={{ style: { width: "80%" } }}>
                <DialogContent>
                  <BatchResult close={closeBatch} batchId={selectedBatchId} hasError={hasError} />
                </DialogContent>
              </Dialog>
              <Dialog open={openApiResult} maxWidth="md" fullWidth PaperProps={{ style: { width: "80%" } }}>
                <DialogContent>
                  <APITester close={handleClose} apiModelRes={executor} />
                </DialogContent>
              </Dialog>

              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "10px",
                  marginTop: "30px",
                  marginBottom: "50px",
                }}
              >
                <span className={globalStyles.interStyle} style={{ color: "#170903", width: "80px" }}>
                  Page no
                </span>
                {[...Array(totalPages)].map((_, index) => (
                  <button
                    key={index + 1}
                    className={` ${currentPage === index + 1 ? globalStyles.activePage : globalStyles.pageButton}`}
                    onClick={() => handlePageChange(index + 1)}
                  >
                    {String(index + 1).padStart(2, "0")}
                  </button>
                ))}
                <span className={globalStyles.interStyle} style={{ color: "#170903", marginLeft: "30px" }}>
                  Show
                </span>
                {showPages.map((pageLimit, index) => (
                  <button
                    key={index}
                    className={`${itemsPerPage === pageLimit ? globalStyles.activePage : globalStyles.pageButton}`}
                    onClick={() => handleShowPage(pageLimit)}
                  >
                    {String(pageLimit).padStart(2, "0")}
                  </button>
                ))}

                {migrationStats.errors > 0 && (
                  <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
                    <button
                      className={globalStyles.mainButton}
                      style={{ marginLeft: "auto", margin: "0" }}
                      onClick={retryAllBatch}
                    >
                      Retry all failed records
                    </button>
                  </div>
                )}
              </div>
            </div>
            <hr className={styles.hr} />

            <div className={styles.dFlex} ref={bottomRef}>
              <div className={styles.section}>
                <div className={styles.videoContent} onClick={handlePlayVideo} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', width: '80%', height: '100%', flex: 1, marginLeft: '30px' }}>
                  {!isVideoPlaying ? (
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', width: '80%', height: '100%', marginLeft: '30px', flex: 1 }}>
                      <h2 className={styles.textVideo} style={{ marginBottom: 24, textAlign: 'center' }}>Watch the product demo</h2>
                      <button className={styles.playButton} style={{ fontSize: 32, width: 90, height: 90, borderRadius: '50%', background: '#EA5822', color: '#fff', border: 'none', boxShadow: '0 2px 8px rgba(234,88,34,0.15)', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto' }}>▶</button>
                    </div>
                  ) : (
                    <iframe
                      style={{ borderRadius: 12, boxShadow: '0 2px 12px rgba(0,0,0,0.08)', maxWidth: 600, width: '100%', aspectRatio: '16/9', border: 0, minHeight: 200, background: '#fff', display: 'block', margin: '0 auto' }}
                      src="https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Ffast.wistia.net%2Fembed%2Fiframe%2Fwrauox425u&display_name=Wistia%2C+Inc.&url=https%3A%2F%2Fsaasgenie.wistia.com%2Fmedias%2Fwrauox425u&image=https%3A%2F%2Fembed-ssl.wistia.com%2Fdeliveries%2F86ed1423ea8a73ca90bc752a2f2a1a7d.jpg%3Fimage_crop_resized%3D960x540&type=text%2Fhtml&schema=wistia"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    ></iframe>
                  )}
                </div>
              </div>
              <div className={styles.section}>
                <div className={styles.dFlex}>
                  <div className={globalStyles.selectionName} style={{ marginBottom: "10px" }}>
                    VALIDATE YOUR MIGRATION
                  </div>
                  <div style={{ display: "flex", gap: "20px", marginLeft: "auto" }}>
                    <div className={styles.dFlex}>
                      <img
                        src="/assets/help.png"
                        alt="Help"
                        className={globalStyles.helpIcon}
                        onClick={() => {
                          displayArticle("Where to find detailed reports of migration?")
                        }}
                      />
                      <span className={globalStyles.guideName}>Where to find detailed reports of migration?</span>
                    </div>
                  </div>
                </div>
                <div>
                  {validateMigration.map((section) => (
                    <div key={section.step}>
                      <div className={globalStyles.poppinsHeaderStyle} style={{ marginBottom: "10px" }}>
                        Step {section.step} - {section.title}
                      </div>
                      <ul>
                        {section.tasks.map((task, index) => (
                          <li key={index} className={globalStyles.interSummaryStyle} style={{ lineHeight: "1.9rem" }}>
                            {task}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
                <button
                  className={globalStyles.mainButton}
                  style={{ width: "90%", marginBottom: "30px" }}
                  onClick={(event) => setOpenFeedback(!openFeedback)}
                >
                  Validate migration and leave feedback
                </button>
              </div>
            </div>
            <Dialog open={openFeedback} maxWidth="md" fullWidth PaperProps={{ style: { width: "70%" } }}>
              <DialogContent sx={{ backgroundColor: "#170903", padding: "0" }}>
                <Feedback close={(event) => setOpenFeedback(!openFeedback)} />
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
        style={{
          fontFamily: "Inter",
        }}
        toastStyle={{
          fontFamily: "Inter",
          fontWeight: "bold",
        }}
      />
    </div>
  )
}
