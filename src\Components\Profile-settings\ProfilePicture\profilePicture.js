import React, {useState, useRef, useEffect} from 'react';
import ReactAvatarEditor from 'react-avatar-editor';
import styles from './profilePicture.module.css';
import globalStyles from '../../globalStyles.module.css';
import {Hi<PERSON>rrowPath, HiArrowUpTray} from "react-icons/hi2";
import {uploadToAzureBlob} from "../../apiService"; // Import Azure Blob upload function

const ProfilePictureUpdate = ({close, save, imageUrl}) => {
    console.log(imageUrl, 'd');
    const [image, setImage] = useState(imageUrl || null);
    const [preview, setPreview] = useState(null);
    const [scale, setScale] = useState(1.2);
    const editorRef = useRef(null);
    const fileInputRef = useRef(null);

    // Use a direct path to the image in the public folder
    const defaultPicture = `${process.env.PUBLIC_URL}/assets/profile-orange.png`;

    const handleImageChange = (e) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setImage(file);
        }
    };

    const handleSave = async () => {
        if (editorRef.current) {
            try {
                const canvas = editorRef.current.getImageScaledToCanvas();
                const blob = await new Promise(resolve => {
                    canvas.toBlob(resolve, 'image/jpeg', 0.95);
                });

                const file = new File([blob], image ? image.name : 'profile-picture.jpg', { type: 'image/jpeg' });
                const response = await uploadToAzureBlob(file); // Use Azure Blob upload function
                if (response) {
                    const azureBlobUrl = response.azureBlobURL; // Get the Azure Blob URL
                    save(azureBlobUrl);
                }
                setPreview(canvas.toDataURL());
                close();
            } catch (error) {
                console.error('Error uploading image:', error);
            }
        }
    };

    useEffect(() => {
        console.log('preview', preview)
    }, [preview]);

    const handleReset = () => {
        // Reset to default image
        setPreview(null);
        setImage(null);
        // Don't save changes here, just reset the local state
    };

    const triggerFileInput = () => {
        // Use the ref to click the file input
        if (fileInputRef.current) {
            fileInputRef.current.click();
        }
    };

    return (
        <div className={styles.modalOverlay}>
            <div className={styles.modalContent}>
                <div className={styles.modalHeader}>
                    <h2 className={globalStyles.selectionName}>UPDATE PROFILE PICTURE</h2>
                    <button className={styles.closeButton} onClick={close}>×</button>
                </div>

                <div className={styles.modalBody}>
                    <div className={styles.editorContainer}>
                        <ReactAvatarEditor
                            ref={editorRef}
                            image={image || preview || defaultPicture}
                            width={150}
                            height={150}
                            border={50}
                            borderRadius={105}
                            color={[255, 255, 255, 0.6]}
                            scale={scale}
                            rotate={0}
                            className={styles.avatarEditor}
                        />

                        <div className={styles.controls}>
                            <div className={styles.uploadControls}>
                                <div className={styles.dFlex}>
                                    <button
                                        type="button"
                                        className={styles.uploadButton}
                                        onClick={triggerFileInput}
                                    >
                                        Upload image
                                        <span className={styles.uploadIcon}>
                                        <HiArrowUpTray className={globalStyles.closeIcon}/>
                                    </span>
                                    </button>
                                </div>

                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/jpeg, image/png"
                                    onChange={handleImageChange}
                                    className={styles.fileInput}
                                    style={{display: "none"}}
                                />

                                <button
                                    type="button"
                                    className={styles.resetButton}
                                    onClick={handleReset}
                                >
                                    Reset to default <span className={styles.uploadIcon}><HiArrowPath className={globalStyles.closeIcon}/></span>
                                </button>
                            </div>

                            <p className={globalStyles.guideName} style={{fontSize: "16px"}}>
                                Supported formats are JPEG, PNG; max. file size of 5 MB
                            </p>

                            <button
                                style={{width: "100%"}}
                                className={globalStyles.mainButton}
                                onClick={handleSave}
                            >
                                Save changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProfilePictureUpdate;
