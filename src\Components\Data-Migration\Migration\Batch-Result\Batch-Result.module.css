.container{
    width: 100%;
}
.tableContainer {
    width: 100%;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dFlex{
    display: flex;
    align-items: center;
}
.filterButton {
    padding: 6px 12px;
    border: 1px solid #DCDAD9;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    font-size: 12px;
    color: #514742;
}

.activeFilter {
    background-color: #170903;
    color: #F8F8F7;
    border-color: #333;
}


.statusSuccess {
    color: #4caf50;
    font-size: 20px;
}

.statusFailed {
    color: #f44336;
    font-size: 20px;
}

.payloadButton {
    background: transparent;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 18px;
}


.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modalContent {
    background: white;
    padding: 24px;
    border-radius: 4px;
    width: 80%;
    max-width: 700px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.modalTitle {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.closeButton {
    background: transparent;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.detailRow {
    margin-bottom: 12px;
}

.detailLabel {
    font-weight: 500;
    color: #666;
    margin-right: 8px;
}

.detailValue {
    color: #333;
}

.responseContainer {
    margin-top: 16px;
}

.responseTitle {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.jsonContainer {
    background-color: #f5f5f5;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
}
/* Dialog styles */
.dialogOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialogContent {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.dialogHeader {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dialogHeader h3 {
    margin: 0;
    font-weight: 500;
    color: #333;
}

.closeDialogIcon {
    cursor: pointer;
    font-size: 20px;
    color: #666;
}

.dialogTabs {
    display: flex;
    border-bottom: 1px solid #eee;
}

.tabButton {
    padding: 12px 24px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.activeTab {
    border-bottom-color: #EF8963;
    color: #EF8963;
}

.dialogBody {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.statusLine {
    font-weight: 500;
    margin-bottom: 16px;
    font-size: 16px;
}

.successCode {
    color: #4CAF50;
    font-weight: bold;
}

.errorCode {
    color: #F44336;
    font-weight: bold;
}

.codeDisplay {
    background-color: #272822;
    color: #f8f8f2;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
}

.dialogFooter {
    padding: 16px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

.closeButton {
    padding: 8px 16px;
    background-color:#EF8963;
    color: #170903;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    line-height: 24px; /* 150% */
}

.expandedRow {
    background-color: #f9f9f9;
}

.expandedRow td {
    padding: 0;
}

/* Custom cell style for migration result log with smaller font */
.migrationCell {
    padding: 0.75rem 1rem;
    color: #170903;
    text-align: center;
    border-bottom: 3px solid #EFEEED;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
}
.expandedContent{
    margin-left: 60px;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
    display: inline-block;
}

.tabbedContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.tabsHeader {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0;
    overflow-x: auto;
    scrollbar-width: none;
    font-family: Poppins;
}

.tabButton {
    padding: 12px 20px;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    color: #666;
    transition: all 0.2s;
    white-space: nowrap;
    font-family: Poppins;
    font-weight: 600;
    font-size: 16px;
}



.activeTab {
    color: #EF8963;
    border-bottom: 2px solid #EF8963;
}

.tabBadge {
    background-color: #e0e0e0;
    border-radius: 9999px;
    padding: 2px 6px;
    font-size: 0.75rem;
    margin-left: 6px;
}

.tabContent {
    padding: 10px 0;
}

.tabPanel {
    width: 100%;
}

.activePanel {
    display: block;
}

.hiddenPanel {
    display: none;
}

.noData {
  font-family: "Inter";
  font-size: 12px;
}

