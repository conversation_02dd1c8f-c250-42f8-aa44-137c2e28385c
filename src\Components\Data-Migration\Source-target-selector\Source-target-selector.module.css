.helpIcon {
    width: 14px;
    height: 14px;
}

.modalOverlay {
    background: rgba(0, 0, 0, 0.6);

}

.modalContent {
    background: #170903;
    padding: 8px 12px;
    border-radius: 10px;
    height: 300px;
    position: relative;
    display: flex;
    flex-direction: column;
    padding-bottom: 0;
}

@keyframes scaleIn {
    from { transform: scale(0.8); }
    to { transform: scale(1); }
}

.closeBtn {
    position: absolute;
    top: 8px;
    right: 12px;
    background: none;
    border: none;
    font-size: 18px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
    z-index: 1;
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
    position: relative;
}

.modalTitle {
    color: #EA5822;
    font-family: Poppins;
    font-size: 15px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    margin: 0;
    padding-left: 15px;
    flex: 1;
}

.searchInput {
    width: 200px;
    padding: 6px 12px;
    margin: 0 15px;
    border-radius: 5px;
    border: 1px solid gray;
    background-color: #252525;
    color: white;
    font-family: "Inter";
    font-weight: 400;
    font-size: 14px;
}

.searchInput::placeholder {
    color: gray;
}

.sourceIconsContainer {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 10px;
}

.sourceIcons {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 2px;
    padding: 4px;
}

.source:hover{
    background-color: #F8F8F7;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.source:hover .sourceName{
    color: #170903 !important;
}

.source {
    text-align: center;
    margin: 5px;
    cursor: pointer;
    padding: 8px;
    transition: background-color 0.3s ease;
    border-radius: 5px;
}

.source img {
    width: 35px;
    height: 35px;
    padding: 8px;
    transition: 0.3s ease-in-out;
}

.selectedSourceName{
    color: #170903 !important;
}

.source:hover img {
    transform: scale(1.1);
}

.uploadOptions {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.option {
    text-align: center;
    cursor: pointer;
}

.option img {
    width: 50px;
    height: 50px;
    background-color: #252525;
    border-radius: 10px;
    padding: 10px;
    transition: 0.3s ease-in-out;
}

.option:hover img {
    transform: scale(1.1);
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 15px;
}

.modalTitle {
    color: #EA5822;
    font-family: Poppins;
    font-size: 15px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    margin: 0;
    padding-left: 15px;
}

.sourceName{
    color: #F8F8F7;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin: 0;
}

.selected {
    background-color: #F8F8F7;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.buttonContainer {
    display: flex;
    justify-content: center;
    padding: 10px 0;
    margin-top: auto;
    background: linear-gradient(to top, rgba(23, 9, 3, 1) 0%, rgba(23, 9, 3, 0.9) 100%);
    border-radius: 0 0 10px 10px;
}
