import React, {useState, useRef, useEffect} from 'react';
import { ChevronDoubleRightIcon, ChevronDoubleLeftIcon } from '@heroicons/react/solid';
import './Sidebar.css';
import {useNavigate} from "react-router-dom";
import {Dialog, DialogContent} from "@mui/material";
import ProfilePictureUpdate from "../Profile-settings/ProfilePicture/profilePicture";
import ChangePassword from "../Profile-settings/ChangePaassword/ChangePassword";
import BookDemo from "../Book-Demo/BookDemo";
import {getUser} from "../apiService";
// import { shutdown } from "@intercom/messenger-js-sdk";

export default function Sidebar({ isCollapsed, setIsCollapsed }) {
    const [email, setEmail] = useState(localStorage.getItem('email'));
    const [openDropdown, setOpenDropdown] = useState(null);
    const [showProfileMenu, setShowProfileMenu] = useState(false);
    const [showTooltip, setShowTooltip] = useState(null);
    const dropdownRef = useRef(null);
    const submenuRef = useRef(null);
    const profileMenuRef = useRef(null);
    const hoverTimeoutRef = useRef(null);
    const tooltipTimeoutRef = useRef(null);
    const navigate = useNavigate();
    const [openDemo, setOpenDemo] = useState(false);
    const apiCalledRef = useRef(null);
    const [data, setData] = useState({
        name: "",
        profile_image: "/assets/profile-orange.png",
        company_name: "",
    })

    const bookDemo = () => {
        setOpenDemo(true);
    }
    const closeDemo = () => {
        setOpenDemo(false);
    }

    useEffect(() => {

        const fetchUser = async () => {
            if(!apiCalledRef.current){
                apiCalledRef.current = true;
                try {
                    const res = await getUser(email);

                    setData((prevState) => ({
                        ...prevState,
                        name: res.name,
                        profile_image: res.profile_image || "/assets/profile-orange.png",
                        company_name: res.company_name || "",
                    }));
                    // const defaultPicture = 'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460_1280.png';
                    // setImageUrl(res.profile_image || defaultPicture);



                } catch (error) {

                    console.log(error)
                }
            }

        };

        fetchUser();
    }, []);
    const menuItems = [
        {
            title: 'ACTIVITY',
            items: [
                { name: 'Home', icon: "/assets/home.png", hasDropDown: false, routeLink: '/home' },
                { name: 'Data Migration', icon: "/assets/data-migration.png", hasDropDown: false , routeLink: '/data-migration', state: {newTemplate: true}},
                { name: 'Data & Security', icon: "/assets/security.png", hasDropDown: false , routeLink: '/data-security' },
                { name: 'Migration History', icon: "/assets/history.png", hasDropDown: true,
                    subItems: [
                        {name: 'Saved Templates', routeLink: '/saved-templates'},
                        {name: 'Completed Migrations', routeLink: '/migration-history'},
                    ]
                },
            ],
        },
        {
            title: 'PRODUCT',
            items: [
                { name: 'Product & Pricing', icon: "/assets/about.png", hasDropDown: false, routeLink: '/product-pricing' },
                // { name: 'Pricing & Packages', icon: "/assets/pricing.png", hasDropDown: false },
                { name: 'Book a Demo', icon: "/assets/demo.png", hasDropDown: false, fun: bookDemo },
            ],
        },
    ];
    const handleLogout = () => {
        localStorage.removeItem('email');
        localStorage.removeItem('sessionExpiry');
        // shutdown();
        window.location.reload();
    };
    const moveToProfile =() => {
        navigate('/profile-settings')

    }
    const moveToBill =() => {
        navigate('/billing-payments')

    }

    const moveToActivity = () => {
        navigate('/activity-and-analytics');
    }


    const profileMenuItems = [
        {title: 'Profile & Settings', fun: moveToProfile},
        {title: 'Billing & Payments', fun: moveToBill},
        {title: 'Activity & Analytics', fun: moveToActivity},
        {title: 'Logout', fun: handleLogout},
    ];


    const handleMouseEnter = (index) => {
        if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
        }
        setOpenDropdown(index);
    };

    const handleMouseLeave = () => {
        hoverTimeoutRef.current = setTimeout(() => {
            setOpenDropdown(null);
        }, 100);
    };

    const handleSubmenuMouseEnter = () => {
        if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
        }
    };

    const handleSubmenuMouseLeave = () => {
        setOpenDropdown(null);
    };

    const handleProfileMouseEnter = () => {
        if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
        }
        setShowProfileMenu(true);
    };

    const handleProfileMouseLeave = () => {
        hoverTimeoutRef.current = setTimeout(() => {
            setShowProfileMenu(false);
        }, 100);
    };

    const handleTooltipMouseEnter = (itemName) => {
        if (tooltipTimeoutRef.current) {
            clearTimeout(tooltipTimeoutRef.current);
        }
        if (isCollapsed) {
            setShowTooltip(itemName);
        }
    };

    const handleTooltipMouseLeave = () => {
        tooltipTimeoutRef.current = setTimeout(() => {
            setShowTooltip(null);
        }, 100);
    };

    const toggleSidebar = () => {
        setIsCollapsed(prev => {
            localStorage.setItem('isSidebarCollapsed', !prev);
            return !prev;
        });
    };

    return (
        <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
            <div className="sidebar-header">
                {!isCollapsed && (
                    <img src="/assets/migrategenie.png" alt="logo" className="logo-migrategenie"/>
                )}
                <button
                    className="collapse-btn"
                    onClick={toggleSidebar}
                >
                    {isCollapsed ? (
                        <div className="d-flex align-items-center">
                            <img src="/assets/saasgenie_logo.png" alt="logo" className="logo"/>
                            <ChevronDoubleRightIcon className="icon-top"/>
                        </div>
                    ) : (
                        <ChevronDoubleLeftIcon className="icon-top"/>
                    )}
                </button>
            </div>
            <hr className="hrline"/>

            <div className="sidebar-content">
                {menuItems.map((section, index) => (
                    <div key={index} className="menu-section">
                        {!isCollapsed && (
                            <h2 className="section-title">{section.title}</h2>
                        )}
                        {section.items.map((item, itemIndex) => (
                            <div
                                key={itemIndex}
                                ref={dropdownRef}
                                className="menu-item-container"
                                onMouseEnter={() => {
                                    if (item.hasDropDown) {
                                        handleMouseEnter(itemIndex);
                                    } else if (isCollapsed) {
                                        handleTooltipMouseEnter(item.name);
                                    }
                                }}
                                onMouseLeave={() => {
                                    if (item.hasDropDown) {
                                        handleMouseLeave();
                                    } else if (isCollapsed) {
                                        handleTooltipMouseLeave();
                                    }
                                }}
                            >
                                <button className="menu-item hover-zoom" onClick={() => {
                                    if (item.fun) {
                                        item.fun();
                                    } else if (item.routeLink) {
                                        if (item.state) {
                                            navigate(item.routeLink, {state: item.state});
                                        } else {
                                            navigate(item.routeLink);
                                        }
                                    }
                                }}>

                                    <img src={item.icon} alt="icon" className="icon" style={{paddingLeft: '16px'}}/>
                                    {!isCollapsed ? (
                                        <span className="menu-text">{item.name}</span>
                                    ) : <span className="menu-text"> </span>}
                                </button>
                                {/* Tooltip for collapsed sidebar (except Migration History) */}
                                {isCollapsed && !item.hasDropDown && showTooltip === item.name && (
                                    <div className="tooltip">
                                        {item.name}
                                    </div>
                                )}
                                {item.hasDropDown && openDropdown === itemIndex && (
                                    <div
                                        className="submenu"
                                        style={{
                                            top: isCollapsed ? '0px' : '0px',
                                            left: isCollapsed ? '65px': '215px'
                                        }}
                                        ref={submenuRef}
                                        onMouseEnter={handleSubmenuMouseEnter}
                                        onMouseLeave={handleSubmenuMouseLeave}
                                    >
                                        {item.subItems.map((subItem, subIndex) => (
                                            <div>
                                            <button onClick={() => {
                                                // Force navigation to the route, even if we're already on a related page
                                                if (subItem.name === 'Completed Migrations') {
                                                    // Navigate with a timestamp to force refresh and replace the current entry in history
                                                    // This ensures we navigate to the migration history page even from batch execution summary
                                                    navigate(subItem.routeLink, {
                                                        state: { refresh: Date.now() },
                                                        replace: true  // Replace current history entry to avoid navigation issues
                                                    });
                                                } else {
                                                    navigate(subItem.routeLink);
                                                }
                                            }}
                                                key={subIndex}
                                                className="submenu-item"
                                            >
                                                {subItem.name}
                                            </button>
                                                {subIndex < item.subItems.length - 1 && <hr className="hrsubline"/>}
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                        {index < menuItems.length - 1 && <hr className="hrline" style={{paddingLeft: "0"}}/>}
                    </div>
                ))}
            </div>
            <Dialog open={openDemo}  fullWidth
                    maxWidth="md" PaperProps={{
                style: {
                    width: '700px',
                },
            }}>
                <DialogContent style={{padding: "0", overflow: "auto", backgroundColor: "#170903"}}>

                    <BookDemo close={closeDemo}/>
                </DialogContent>
            </Dialog>

            <div
                className="sidebar-footer"
                onMouseEnter={() => {
                    handleProfileMouseEnter();
                    if (isCollapsed) {
                        handleTooltipMouseEnter(data.name || "USER NAME");
                    }
                }}
                onMouseLeave={() => {
                    handleProfileMouseLeave();
                    if (isCollapsed) {
                        handleTooltipMouseLeave();
                    }
                }}
            >
                <div className="user-profile hover-zoom menu-item-container">
                    <div>
                        <img
                            src={data.profile_image}
                            alt="profile"
                            className="profile"
                            style={{
                                width: "40px",
                                height: "40px",
                                borderRadius: "50%",
                                objectFit: "cover"
                            }}
                        />
                    </div>
                    {!isCollapsed && (
                        <div className="user-info">
                            <div className="username">{data.name || "USER NAME"}</div>
                            {data.company_name && <div className="organization">{data.company_name}</div> }
                        </div>
                    )}
                    {/* Tooltip for user profile when collapsed */}
                    {isCollapsed && showTooltip === (data.name || "USER NAME") && (
                        <div className="tooltip" style={{ left: '65px', top: '0px' }}>
                            {data.name || "USER NAME"}
                        </div>
                    )}
                </div>
                {showProfileMenu && (
                    <div
                        className="submenu"
                        style={{
                            bottom: '40px',
                            left: isCollapsed ? '80px' : '200px',
                        }}
                        ref={profileMenuRef}
                    >
                        {profileMenuItems.map((item, index) => (
                            <div>
                            <button
                                key={index} onClick={item.fun}
                                className="submenu-item"
                            >
                                {item.title}
                            </button>
                             {index < profileMenuItems.length - 1 && <hr className="hrsubline"/>}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
