{"version": 3, "file": "static/js/488.0c77328e.chunk.js", "mappings": "qUAAA,IAAIA,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,EAAEC,EAAE,SAASN,GAAGO,iBAAiB,YAAY,SAASN,GAAGA,EAAEO,YAAYH,EAAEJ,EAAEQ,UAAUT,EAAEC,GAAG,IAAG,EAAG,EAAES,EAAE,WAAW,IAAIV,EAAEW,KAAKC,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,GAAG,GAAGb,GAAGA,EAAEc,cAAc,GAAGd,EAAEc,cAAcF,YAAYG,MAAM,OAAOf,CAAC,EAAEgB,EAAE,WAAW,IAAIhB,EAAEU,IAAI,OAAOV,GAAGA,EAAEiB,iBAAiB,CAAC,EAAEC,EAAE,SAASlB,EAAEC,GAAG,IAAIC,EAAEQ,IAAIP,EAAE,WAA8J,OAAnJE,GAAG,EAAEF,EAAE,qBAAqBD,IAAIiB,SAASC,cAAcJ,IAAI,EAAEb,EAAE,YAAYgB,SAASE,aAAalB,EAAE,UAAUD,EAAEoB,OAAOnB,EAAED,EAAEoB,KAAKC,QAAQ,KAAK,OAAa,CAACC,KAAKxB,EAAEyB,WAAM,IAASxB,GAAG,EAAEA,EAAEyB,OAAO,OAAOC,MAAM,EAAEC,QAAQ,GAAGC,GAAG,MAAMC,OAAOC,KAAKhB,MAAM,KAAKe,OAAOE,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAMC,eAAehC,EAAE,EAAEiC,EAAE,SAASpC,EAAEC,EAAEC,GAAG,IAAI,GAAGmC,oBAAoBC,oBAAoBC,SAASvC,GAAG,CAAC,IAAIG,EAAE,IAAIkC,qBAAqB,SAASrC,GAAGwC,QAAQC,UAAUC,MAAM,WAAWzC,EAAED,EAAE2C,aAAa,GAAG,IAAI,OAAOxC,EAAEyC,QAAQC,OAAOC,OAAO,CAACxB,KAAKtB,EAAE+C,UAAS,GAAI7C,GAAG,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMH,GAAG,CAAC,EAAEgD,EAAE,SAAShD,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAE,OAAO,SAASC,GAAGL,EAAEwB,OAAO,IAAInB,GAAGH,MAAME,EAAEJ,EAAEwB,OAAOrB,GAAG,UAAK,IAASA,KAAKA,EAAEH,EAAEwB,MAAMxB,EAAE0B,MAAMtB,EAAEJ,EAAEyB,OAAO,SAAS1B,EAAEC,GAAG,OAAOD,EAAEC,EAAE,GAAG,OAAOD,EAAEC,EAAE,GAAG,oBAAoB,MAAM,CAApE,CAAsEA,EAAEwB,MAAMvB,GAAGF,EAAEC,GAAG,CAAC,EAAEgD,EAAE,SAASjD,GAAGkD,uBAAuB,WAAW,OAAOA,uBAAuB,WAAW,OAAOlD,GAAG,GAAG,GAAG,EAAEmD,EAAE,SAASnD,GAAGmB,SAASZ,iBAAiB,oBAAoB,WAAW,WAAWY,SAASiC,iBAAiBpD,GAAG,GAAG,EAAEqD,EAAE,SAASrD,GAAG,IAAIC,GAAE,EAAG,OAAO,WAAWA,IAAID,IAAIC,GAAE,EAAG,CAAC,EAAEqD,GAAG,EAAEC,EAAE,WAAW,MAAM,WAAWpC,SAASiC,iBAAiBjC,SAASC,aAAa,IAAI,CAAC,EAAEoC,EAAE,SAASxD,GAAG,WAAWmB,SAASiC,iBAAiBE,GAAG,IAAIA,EAAE,qBAAqBtD,EAAEsB,KAAKtB,EAAES,UAAU,EAAEgD,IAAI,EAAEC,EAAE,WAAWnD,iBAAiB,mBAAmBiD,GAAE,GAAIjD,iBAAiB,qBAAqBiD,GAAE,EAAG,EAAEC,EAAE,WAAWE,oBAAoB,mBAAmBH,GAAE,GAAIG,oBAAoB,qBAAqBH,GAAE,EAAG,EAAEI,EAAE,WAAW,OAAON,EAAE,IAAIA,EAAEC,IAAIG,IAAIpD,GAAG,WAAWuD,YAAY,WAAWP,EAAEC,IAAIG,GAAG,GAAG,EAAE,KAAK,CAAC,mBAAII,GAAkB,OAAOR,CAAC,EAAE,EAAES,EAAE,SAAS/D,GAAGmB,SAASC,aAAab,iBAAiB,sBAAsB,WAAW,OAAOP,GAAG,IAAG,GAAIA,GAAG,EAAEgE,EAAE,CAAC,KAAK,KAAKC,EAAE,SAASjE,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE8D,GAAG,WAAW,IAAI7D,EAAEC,EAAEyD,IAAIxD,EAAEc,EAAE,OAAOb,EAAE+B,EAAE,SAAS,SAASpC,GAAGA,EAAEkE,SAAS,SAASlE,GAAG,2BAA2BA,EAAEwB,OAAOnB,EAAE8D,aAAanE,EAAEoE,UAAUjE,EAAE2D,kBAAkB1D,EAAEqB,MAAMO,KAAKqC,IAAIrE,EAAEoE,UAAUpD,IAAI,GAAGZ,EAAEwB,QAAQ0C,KAAKtE,GAAGE,GAAE,IAAK,GAAG,IAAIG,IAAIH,EAAE8C,EAAEhD,EAAEI,EAAE4D,EAAE/D,EAAEsE,kBAAkBjE,GAAG,SAASH,GAAGC,EAAEc,EAAE,OAAOhB,EAAE8C,EAAEhD,EAAEI,EAAE4D,EAAE/D,EAAEsE,kBAAkBtB,GAAG,WAAW7C,EAAEqB,MAAMb,YAAYG,MAAMZ,EAAEM,UAAUP,GAAE,EAAG,GAAG,IAAI,GAAG,EAAEsE,EAAE,CAAC,GAAG,KAAKC,EAAE,SAASzE,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAEgE,EAAEZ,GAAG,WAAW,IAAInD,EAAEC,EAAEe,EAAE,MAAM,GAAGd,EAAE,EAAEC,EAAE,GAAGK,EAAE,SAASV,GAAGA,EAAEkE,SAAS,SAASlE,GAAG,IAAIA,EAAE0E,eAAe,CAAC,IAAIzE,EAAEI,EAAE,GAAGH,EAAEG,EAAEA,EAAEsE,OAAO,GAAGvE,GAAGJ,EAAEoE,UAAUlE,EAAEkE,UAAU,KAAKpE,EAAEoE,UAAUnE,EAAEmE,UAAU,KAAKhE,GAAGJ,EAAEyB,MAAMpB,EAAEiE,KAAKtE,KAAKI,EAAEJ,EAAEyB,MAAMpB,EAAE,CAACL,GAAG,CAAC,IAAII,EAAED,EAAEsB,QAAQtB,EAAEsB,MAAMrB,EAAED,EAAEyB,QAAQvB,EAAEH,IAAI,EAAEc,EAAEoB,EAAE,eAAe1B,GAAGM,IAAId,EAAE8C,EAAEhD,EAAEG,EAAEqE,EAAEvE,EAAEsE,kBAAkBpB,GAAG,WAAWzC,EAAEM,EAAE4D,eAAe1E,GAAE,EAAG,IAAII,GAAG,WAAWF,EAAE,EAAED,EAAEe,EAAE,MAAM,GAAGhB,EAAE8C,EAAEhD,EAAEG,EAAEqE,EAAEvE,EAAEsE,kBAAkBtB,GAAG,WAAW,OAAO/C,GAAG,GAAG,IAAI2D,WAAW3D,EAAE,GAAG,IAAI,EAAE2E,EAAE,EAAEC,EAAE,IAAIC,EAAE,EAAEC,EAAE,SAAShF,GAAGA,EAAEkE,SAAS,SAASlE,GAAGA,EAAEiF,gBAAgBH,EAAE9C,KAAKkD,IAAIJ,EAAE9E,EAAEiF,eAAeF,EAAE/C,KAAKqC,IAAIU,EAAE/E,EAAEiF,eAAeJ,EAAEE,GAAGA,EAAED,GAAG,EAAE,EAAE,EAAE,GAAG,EAAEK,EAAE,WAAW,OAAOnF,EAAE6E,EAAEjE,YAAYwE,kBAAkB,CAAC,EAAEC,EAAE,WAAW,qBAAqBzE,aAAaZ,IAAIA,EAAEoC,EAAE,QAAQ4C,EAAE,CAAC1D,KAAK,QAAQyB,UAAS,EAAGuC,kBAAkB,IAAI,EAAEC,EAAE,GAAGC,EAAE,IAAIC,IAAIC,EAAE,EAA8EC,EAAE,GAAGC,EAAE,SAAS5F,GAAG,GAAG2F,EAAEzB,SAAS,SAASjE,GAAG,OAAOA,EAAED,EAAE,IAAIA,EAAEiF,eAAe,gBAAgBjF,EAAE6F,UAAU,CAAC,IAAI5F,EAAEsF,EAAEA,EAAEZ,OAAO,GAAGzE,EAAEsF,EAAEM,IAAI9F,EAAEiF,eAAe,GAAG/E,GAAGqF,EAAEZ,OAAO,IAAI3E,EAAE+F,SAAS9F,EAAE+F,QAAQ,CAAC,GAAG9F,EAAEF,EAAE+F,SAAS7F,EAAE8F,SAAS9F,EAAE0B,QAAQ,CAAC5B,GAAGE,EAAE8F,QAAQhG,EAAE+F,UAAU/F,EAAE+F,WAAW7F,EAAE8F,SAAShG,EAAEoE,YAAYlE,EAAE0B,QAAQ,GAAGwC,WAAWlE,EAAE0B,QAAQ0C,KAAKtE,OAAO,CAAC,IAAIG,EAAE,CAAC0B,GAAG7B,EAAEiF,cAAce,QAAQhG,EAAE+F,SAASnE,QAAQ,CAAC5B,IAAIwF,EAAES,IAAI9F,EAAE0B,GAAG1B,GAAGoF,EAAEjB,KAAKnE,EAAE,CAACoF,EAAEW,MAAM,SAASlG,EAAEC,GAAG,OAAOA,EAAE+F,QAAQhG,EAAEgG,OAAO,IAAIT,EAAEZ,OAAO,IAAIY,EAAEY,OAAO,IAAIjC,SAAS,SAASlE,GAAG,OAAOwF,EAAEY,OAAOpG,EAAE6B,GAAG,GAAG,CAAC,CAAC,EAAEwE,EAAE,SAASrG,GAAG,IAAIC,EAAEU,KAAK2F,qBAAqB3F,KAAKkD,WAAW3D,GAAG,EAAE,OAAOF,EAAEqD,EAAErD,GAAG,WAAWmB,SAASiC,gBAAgBpD,KAAKE,EAAED,EAAED,GAAGmD,EAAEnD,IAAIE,CAAC,EAAEqG,EAAE,CAAC,IAAI,KAAKC,EAAE,SAASxG,EAAEC,GAAG,2BAA2BU,MAAM,kBAAkB8F,uBAAuBC,YAAYzG,EAAEA,GAAG,CAAC,EAAE8D,GAAG,WAAW,IAAI7D,EAAEmF,IAAI,IAAIlF,EAAEC,EAAEc,EAAE,OAAOb,EAAE,SAASL,GAAGqG,GAAG,WAAWrG,EAAEkE,QAAQ0B,GAAG,IAAI3F,EAAz8B,WAAW,IAAID,EAAEgC,KAAKkD,IAAIK,EAAEZ,OAAO,EAAE3C,KAAKC,OAAOkD,IAAIO,GAAG,KAAK,OAAOH,EAAEvF,EAAE,CAAm4B2G,GAAI1G,GAAGA,EAAE+F,UAAU5F,EAAEqB,QAAQrB,EAAEqB,MAAMxB,EAAE+F,QAAQ5F,EAAEwB,QAAQ3B,EAAE2B,QAAQzB,IAAI,GAAG,EAAEO,EAAE0B,EAAE,QAAQ/B,EAAE,CAACiF,kBAAkB,QAAQpF,EAAED,EAAEqF,yBAAoB,IAASpF,EAAEA,EAAE,KAAKC,EAAE6C,EAAEhD,EAAEI,EAAEmG,EAAEtG,EAAEsE,kBAAkB7D,IAAIA,EAAEkC,QAAQ,CAACtB,KAAK,cAAcyB,UAAS,IAAKI,GAAG,WAAW9C,EAAEK,EAAEkE,eAAezE,GAAE,EAAG,IAAIG,GAAG,WAAWoF,EAAEP,IAAII,EAAEZ,OAAO,EAAEa,EAAEoB,QAAQxG,EAAEc,EAAE,OAAOf,EAAE6C,EAAEhD,EAAEI,EAAEmG,EAAEtG,EAAEsE,iBAAiB,IAAI,IAAI,EAAEsC,EAAE,CAAC,KAAK,KAAKC,EAAE,CAAC,EAAEC,EAAE,SAAS/G,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE8D,GAAG,WAAW,IAAI7D,EAAEC,EAAEyD,IAAIxD,EAAEc,EAAE,OAAOb,EAAE,SAASL,GAAGC,EAAEsE,mBAAmBvE,EAAEA,EAAEgH,OAAO,IAAIhH,EAAEkE,SAAS,SAASlE,GAAGA,EAAEoE,UAAUjE,EAAE2D,kBAAkB1D,EAAEqB,MAAMO,KAAKqC,IAAIrE,EAAEoE,UAAUpD,IAAI,GAAGZ,EAAEwB,QAAQ,CAAC5B,GAAGE,IAAI,GAAG,EAAEQ,EAAE0B,EAAE,2BAA2B/B,GAAG,GAAGK,EAAE,CAACR,EAAE8C,EAAEhD,EAAEI,EAAEyG,EAAE5G,EAAEsE,kBAAkB,IAAIjB,EAAED,GAAG,WAAWyD,EAAE1G,EAAEyB,MAAMxB,EAAEK,EAAEkE,eAAelE,EAAEyD,aAAa2C,EAAE1G,EAAEyB,KAAI,EAAG3B,GAAE,GAAI,IAAI,CAAC,UAAU,SAASgE,SAAS,SAASlE,GAAGO,iBAAiBP,GAAG,WAAW,OAAOqG,EAAE/C,EAAE,GAAG,CAAC2D,MAAK,EAAGC,SAAQ,GAAI,IAAI/D,EAAEG,GAAGhD,GAAG,SAASH,GAAGC,EAAEc,EAAE,OAAOhB,EAAE8C,EAAEhD,EAAEI,EAAEyG,EAAE5G,EAAEsE,kBAAkBtB,GAAG,WAAW7C,EAAEqB,MAAMb,YAAYG,MAAMZ,EAAEM,UAAUqG,EAAE1G,EAAEyB,KAAI,EAAG3B,GAAE,EAAG,GAAG,GAAG,CAAC,GAAG,EAAEiH,EAAE,CAAC,IAAI,MAAMC,EAAE,SAASpH,EAAEC,GAAGkB,SAASC,aAAa2C,GAAG,WAAW,OAAO/D,EAAEC,EAAE,IAAI,aAAakB,SAASkG,WAAW9G,iBAAiB,QAAQ,WAAW,OAAOP,EAAEC,EAAE,IAAG,GAAI4D,WAAW5D,EAAE,EAAE,EAAEqH,EAAE,SAAStH,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAIC,EAAEgB,EAAE,QAAQf,EAAE6C,EAAEhD,EAAEE,EAAEiH,EAAElH,EAAEsE,kBAAkB6C,GAAG,WAAW,IAAIhH,EAAEM,IAAIN,IAAIF,EAAEuB,MAAMO,KAAKqC,IAAIjE,EAAEU,cAAcE,IAAI,GAAGd,EAAE0B,QAAQ,CAACxB,GAAGD,GAAE,GAAIG,GAAG,WAAWJ,EAAEgB,EAAE,OAAO,IAAIf,EAAE6C,EAAEhD,EAAEE,EAAEiH,EAAElH,EAAEsE,oBAAmB,EAAG,IAAI,GAAG,EAAEgD,EAAE,CAACC,SAAQ,EAAGN,SAAQ,GAAIO,EAAE,IAAI1F,KAAK2F,EAAE,SAAS1H,EAAEI,GAAGH,IAAIA,EAAEG,EAAEF,EAAEF,EAAEG,EAAE,IAAI4B,KAAK4F,GAAEhE,qBAAqBiE,IAAI,EAAEA,EAAE,WAAW,GAAG1H,GAAG,GAAGA,EAAEC,EAAEsH,EAAE,CAAC,IAAIzH,EAAE,CAAC6F,UAAU,cAAcrE,KAAKvB,EAAEqB,KAAKuG,OAAO5H,EAAE4H,OAAOC,WAAW7H,EAAE6H,WAAW1D,UAAUnE,EAAEQ,UAAUsH,gBAAgB9H,EAAEQ,UAAUP,GAAGE,EAAE8D,SAAS,SAASjE,GAAGA,EAAED,EAAE,IAAII,EAAE,EAAE,CAAC,EAAE4H,EAAE,SAAShI,GAAG,GAAGA,EAAE8H,WAAW,CAAC,IAAI7H,GAAGD,EAAES,UAAU,KAAK,IAAIsB,KAAKnB,YAAYG,OAAOf,EAAES,UAAU,eAAeT,EAAEsB,KAAK,SAAStB,EAAEC,GAAG,IAAIC,EAAE,WAAWwH,EAAE1H,EAAEC,GAAGG,GAAG,EAAED,EAAE,WAAWC,GAAG,EAAEA,EAAE,WAAWuD,oBAAoB,YAAYzD,EAAEqH,GAAG5D,oBAAoB,gBAAgBxD,EAAEoH,EAAE,EAAEhH,iBAAiB,YAAYL,EAAEqH,GAAGhH,iBAAiB,gBAAgBJ,EAAEoH,EAAE,CAAhO,CAAkOtH,EAAED,GAAG0H,EAAEzH,EAAED,EAAE,CAAC,EAAE2H,GAAE,SAAS3H,GAAG,CAAC,YAAY,UAAU,aAAa,eAAekE,SAAS,SAASjE,GAAG,OAAOD,EAAEC,EAAE+H,EAAET,EAAE,GAAG,EAAEU,GAAE,CAAC,IAAI,KAAKC,GAAG,SAASlI,EAAEG,GAAGA,EAAEA,GAAG,CAAC,EAAE4D,GAAG,WAAW,IAAI1D,EAAEK,EAAEkD,IAAI5C,EAAEE,EAAE,OAAO+B,EAAE,SAASjD,GAAGA,EAAEoE,UAAU1D,EAAEoD,kBAAkB9C,EAAES,MAAMzB,EAAE+H,gBAAgB/H,EAAEoE,UAAUpD,EAAEY,QAAQ0C,KAAKtE,GAAGK,GAAE,GAAI,EAAEiD,EAAE,SAAStD,GAAGA,EAAEkE,QAAQjB,EAAE,EAAEM,EAAEnB,EAAE,cAAckB,GAAGjD,EAAE2C,EAAEhD,EAAEgB,EAAEiH,GAAE9H,EAAEoE,kBAAkBhB,IAAIJ,EAAEE,GAAG,WAAWC,EAAEC,EAAEqB,eAAerB,EAAEY,YAAY,KAAK7D,GAAG,WAAW,IAAIA,EAAEU,EAAEE,EAAE,OAAOb,EAAE2C,EAAEhD,EAAEgB,EAAEiH,GAAE9H,EAAEoE,kBAAkBnE,EAAE,GAAGF,GAAG,EAAED,EAAE,KAAK0H,GAAEpH,kBAAkBD,EAAE2C,EAAE7C,EAAEkE,KAAKhE,GAAGsH,GAAG,IAAI,GAAG,C", "sources": ["../node_modules/web-vitals/dist/web-vitals.js"], "sourcesContent": ["var e,n,t,r,i,o=-1,a=function(e){addEventListener(\"pageshow\",(function(n){n.persisted&&(o=n.timeStamp,e(n))}),!0)},c=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},u=function(){var e=c();return e&&e.activationStart||0},f=function(e,n){var t=c(),r=\"navigate\";o>=0?r=\"back-forward-cache\":t&&(document.prerendering||u()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":t.type&&(r=t.type.replace(/_/g,\"-\")));return{name:e,value:void 0===n?-1:n,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},s=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},t||{})),r}}catch(e){}},d=function(e,n,t,r){var i,o;return function(a){n.value>=0&&(a||r)&&((o=n.value-(i||0))||void 0===i)&&(i=n.value,n.delta=o,n.rating=function(e,n){return e>n[1]?\"poor\":e>n[0]?\"needs-improvement\":\"good\"}(n.value,t),e(n))}},l=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&e()}))},v=function(e){var n=!1;return function(){n||(e(),n=!0)}},m=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){\"hidden\"===document.visibilityState&&m>-1&&(m=\"visibilitychange\"===e.type?e.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return m<0&&(m=h(),y(),a((function(){setTimeout((function(){m=h(),y()}),0)}))),{get firstHiddenTime(){return m}}},C=function(e){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return e()}),!0):e()},b=[1800,3e3],S=function(e,n){n=n||{},C((function(){var t,r=E(),i=f(\"FCP\"),o=s(\"paint\",(function(e){e.forEach((function(e){\"first-contentful-paint\"===e.name&&(o.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-u(),0),i.entries.push(e),t(!0)))}))}));o&&(t=d(e,i,b,n.reportAllChanges),a((function(r){i=f(\"FCP\"),t=d(e,i,b,n.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,t(!0)}))})))}))},L=[.1,.25],w=function(e,n){n=n||{},S(v((function(){var t,r=f(\"CLS\",0),i=0,o=[],c=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=o[0],t=o[o.length-1];i&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(i+=e.value,o.push(e)):(i=e.value,o=[e])}})),i>r.value&&(r.value=i,r.entries=o,t())},u=s(\"layout-shift\",c);u&&(t=d(e,r,L,n.reportAllChanges),p((function(){c(u.takeRecords()),t(!0)})),a((function(){i=0,r=f(\"CLS\",0),t=d(e,r,L,n.reportAllChanges),l((function(){return t()}))})),setTimeout(t,0))})))},A=0,I=1/0,P=0,M=function(e){e.forEach((function(e){e.interactionId&&(I=Math.min(I,e.interactionId),P=Math.max(P,e.interactionId),A=P?(P-I)/7+1:0)}))},k=function(){return e?A:performance.interactionCount||0},F=function(){\"interactionCount\"in performance||e||(e=s(\"event\",M,{type:\"event\",buffered:!0,durationThreshold:0}))},D=[],x=new Map,R=0,B=function(){var e=Math.min(D.length-1,Math.floor((k()-R)/50));return D[e]},H=[],q=function(e){if(H.forEach((function(n){return n(e)})),e.interactionId||\"first-input\"===e.entryType){var n=D[D.length-1],t=x.get(e.interactionId);if(t||D.length<10||e.duration>n.latency){if(t)e.duration>t.latency?(t.entries=[e],t.latency=e.duration):e.duration===t.latency&&e.startTime===t.entries[0].startTime&&t.entries.push(e);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};x.set(r.id,r),D.push(r)}D.sort((function(e,n){return n.latency-e.latency})),D.length>10&&D.splice(10).forEach((function(e){return x.delete(e.id)}))}}},O=function(e){var n=self.requestIdleCallback||self.setTimeout,t=-1;return e=v(e),\"hidden\"===document.visibilityState?e():(t=n(e),p(e)),t},N=[200,500],j=function(e,n){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(n=n||{},C((function(){var t;F();var r,i=f(\"INP\"),o=function(e){O((function(){e.forEach(q);var n=B();n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())}))},c=s(\"event\",o,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});r=d(e,i,N,n.reportAllChanges),c&&(c.observe({type:\"first-input\",buffered:!0}),p((function(){o(c.takeRecords()),r(!0)})),a((function(){R=k(),D.length=0,x.clear(),i=f(\"INP\"),r=d(e,i,N,n.reportAllChanges)})))})))},_=[2500,4e3],z={},G=function(e,n){n=n||{},C((function(){var t,r=E(),i=f(\"LCP\"),o=function(e){n.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-u(),0),i.entries=[e],t())}))},c=s(\"largest-contentful-paint\",o);if(c){t=d(e,i,_,n.reportAllChanges);var m=v((function(){z[i.id]||(o(c.takeRecords()),c.disconnect(),z[i.id]=!0,t(!0))}));[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,(function(){return O(m)}),{once:!0,capture:!0})})),p(m),a((function(r){i=f(\"LCP\"),t=d(e,i,_,n.reportAllChanges),l((function(){i.value=performance.now()-r.timeStamp,z[i.id]=!0,t(!0)}))}))}}))},J=[800,1800],K=function e(n){document.prerendering?C((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},Q=function(e,n){n=n||{};var t=f(\"TTFB\"),r=d(e,t,J,n.reportAllChanges);K((function(){var i=c();i&&(t.value=Math.max(i.responseStart-u(),0),t.entries=[i],r(!0),a((function(){t=f(\"TTFB\",0),(r=d(e,t,J,n.reportAllChanges))(!0)})))}))},U={passive:!0,capture:!0},V=new Date,W=function(e,i){n||(n=i,t=e,r=new Date,Z(removeEventListener),X())},X=function(){if(t>=0&&t<r-V){var e={entryType:\"first-input\",name:n.type,target:n.target,cancelable:n.cancelable,startTime:n.timeStamp,processingStart:n.timeStamp+t};i.forEach((function(n){n(e)})),i=[]}},Y=function(e){if(e.cancelable){var n=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,n){var t=function(){W(e,n),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",t,U),removeEventListener(\"pointercancel\",r,U)};addEventListener(\"pointerup\",t,U),addEventListener(\"pointercancel\",r,U)}(n,e):W(n,e)}},Z=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(n){return e(n,Y,U)}))},$=[100,300],ee=function(e,r){r=r||{},C((function(){var o,c=E(),u=f(\"FID\"),l=function(e){e.startTime<c.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),o(!0))},m=function(e){e.forEach(l)},h=s(\"first-input\",m);o=d(e,u,$,r.reportAllChanges),h&&(p(v((function(){m(h.takeRecords()),h.disconnect()}))),a((function(){var a;u=f(\"FID\"),o=d(e,u,$,r.reportAllChanges),i=[],t=-1,n=null,Z(addEventListener),a=l,i.push(a),X()})))}))};export{L as CLSThresholds,b as FCPThresholds,$ as FIDThresholds,N as INPThresholds,_ as LCPThresholds,J as TTFBThresholds,w as onCLS,S as onFCP,ee as onFID,j as onINP,G as onLCP,Q as onTTFB};\n"], "names": ["e", "n", "t", "r", "i", "o", "a", "addEventListener", "persisted", "timeStamp", "c", "self", "performance", "getEntriesByType", "responseStart", "now", "u", "activationStart", "f", "document", "prerendering", "wasDiscarded", "type", "replace", "name", "value", "rating", "delta", "entries", "id", "concat", "Date", "Math", "floor", "random", "navigationType", "s", "PerformanceObserver", "supportedEntryTypes", "includes", "Promise", "resolve", "then", "getEntries", "observe", "Object", "assign", "buffered", "d", "l", "requestAnimationFrame", "p", "visibilityState", "v", "m", "h", "g", "T", "y", "removeEventListener", "E", "setTimeout", "firstHiddenTime", "C", "b", "S", "for<PERSON>ach", "disconnect", "startTime", "max", "push", "reportAllChanges", "L", "w", "hadRecentInput", "length", "takeRecords", "A", "I", "P", "M", "interactionId", "min", "k", "interactionCount", "F", "durationThreshold", "D", "x", "Map", "R", "H", "q", "entryType", "get", "duration", "latency", "set", "sort", "splice", "delete", "O", "requestIdleCallback", "N", "j", "PerformanceEventTiming", "prototype", "B", "clear", "_", "z", "G", "slice", "once", "capture", "J", "K", "readyState", "Q", "U", "passive", "V", "W", "Z", "X", "target", "cancelable", "processingStart", "Y", "$", "ee"], "sourceRoot": ""}