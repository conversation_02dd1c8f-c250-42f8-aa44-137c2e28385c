import { useContext, useEffect, useState } from "react"
import styles from "./Target-connection.module.css"
import globalStyles from "../../globalStyles.module.css"
import SourceTargetSelector from "../Source-target-selector/Source-target-selector"
import { CheckIcon, EyeIcon, EyeOffIcon } from "@heroicons/react/solid"
import { FormControl, MenuItem, Select } from "@mui/material"
import { Dialog, DialogContent } from "@mui/material"
import {
  getMigrationProviders,
  getTargetConnection,
  executeApi,
  saveMigrationPlan,
  postActivity,
  updateConnDetails,
} from "../../apiService"
import { MigrationContext } from "../Data-Migration"
import { ToastContainer, toast } from "react-toastify"
import { displayArticle } from "../../../Helper/helper"

export default function TargetConnection({
  setSelectedTab,
  planId,
  setPlanId,
  templateName,
  migrationTarget,
  setMigrationTarget,
}) {
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const { migrationState, setMigrationState } = useContext(MigrationContext)

  const sourceObjData = migrationState.sourceObjData
  const targetData = migrationState.targetData

  const [data, setData] = useState([])
  const [dropDownData, setDropDownData] = useState([])
  const [dropdownOptions, setDropdownOptions] = useState([])
  const [selectedSource, setSelectedSource] = useState(null)
  const [showModel, setShowModel] = useState(false)
  const [formData, setFormData] = useState({})
  const [isConnected, setIsConnected] = useState(false)
  const [openDialog, setOpenDialog] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [connectionError, setConnectionError] = useState(false)
  const [visibleFields, setVisibleFields] = useState({})
  const [formValid, setFormValid] = useState(false)
  const [credentialsModified, setCredentialsModified] = useState(false)

  const toggleVisibility = (fieldName) => {
    setVisibleFields((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }))
  }

  useEffect(() => {
    setFormData(targetData.formData || {})
    setSelectedSource(targetData.target)
    setIsConnected(targetData.isConnected || false)
    setCredentialsModified(false)

    const fetchProviders = async () => {
      try {
        const data = await getMigrationProviders()
        setData(data)
      } catch (error) {
        console.error("Failed to load migration providers:", error)
      }
    }

    fetchProviders()
  }, [targetData])

  useEffect(() => {
    if (selectedSource?.name) {
      getTargetConnectionFields(selectedSource?.name)
    }
  }, [selectedSource])

  useEffect(() => {
    if (isConnected && dropDownData.length > 0) {
      fetchOptions()
    }
  }, [isConnected, dropDownData])

  useEffect(() => {
    validateForm()
  }, [formData, selectedSource])

  const validateForm = () => {
    if (!selectedSource) {
      setFormValid(false)
      return
    }

    const requiredFields = selectedSource.fields?.filter((field) => !field.optional) || []
    const allRequiredFieldsFilled = requiredFields.every(
      (field) => formData[field.name] && formData[field.name].trim() !== "",
    )
    let dropdownsValid = true
    if (isConnected && dropDownData.length > 0) {
      dropDownData[0]?.fields.forEach((dropdown) => {
        if (!dropdown.optional && (!formData[dropdown.key] || formData[dropdown.key] === "")) {
          dropdownsValid = false
        }
      })
    }

    setFormValid(allRequiredFieldsFilled && (!isConnected || dropdownsValid))
  }

  const openSelector = () => {
    setShowModel(true)
  }

  const handleSelect = (selectedValue) => {
    const selectedData = data.find((item) => item.name === selectedValue)
    setMigrationTarget([selectedData.id])
    setSelectedSource(selectedData)

    localStorage.setItem("targetPlatform", selectedData.displayName || selectedData.name)

    getTargetConnectionFields(selectedData.name)

    setFormData({})
    setIsConnected(false)
    setConnectionError(false)
    setCredentialsModified(true)
  }

  const getTargetConnectionFields = async (target) => {
    try {
      const data = await getTargetConnection(target)
      setDropDownData([data])
    } catch (error) {
      console.error("Failed to get data:", error)
    }
  }

  const extractSubdomain = (url) => {
    if (!url) return null
    const subdomainMatch = url.match(/^(?:https?:\/\/)?([^./]+)\./)
    return subdomainMatch ? subdomainMatch[1] : null
  }

  const extractSourceDomain = (url) => {
    if (!url) return null
    try {
      const trimmedUrl = url.trim()
      if (!/^https?:\/\/[a-zA-Z0-9]+([-.]{1}[a-zA-Z0-9]+)*\.[a-zA-Z]{2,}(:[0-9]{1,5})?(\/.*)?$/.test(trimmedUrl)) {
        return null
      }
      const parsedUrl = new URL(trimmedUrl)
      return parsedUrl.hostname
    } catch (error) {
      console.error("Invalid URL:", error)
      return null
    }
  }

  const isValidUrl = (url) => {
    if (!url) return false
    try {
      const urlPattern = /^https?:\/\/[a-zA-Z0-9]+([-.]{1}[a-zA-Z0-9]+)*\.[a-zA-Z]{2,}(:[0-9]{1,5})?(\/.*)?$/
      return urlPattern.test(url)
    } catch (error) {
      return false
    }
  }

  const fetchOptions = async () => {
    if (formData) {
      for (const [index, field] of dropDownData[0].fields.entries()) {
        if (!field?.dependent?.includes(index + 1)) {
          const queryParams = field.fieldExecutor.queryParams.map((param) => {
            if (param.key.startsWith("domain")) {
              if (dropDownData[0].domainType === "subDomain") {
                return { ...param, value: extractSubdomain(formData.instance_url) }
              } else if (dropDownData[0].domainType === "sourceDomain") {
                return { ...param, value: extractSourceDomain(formData.instance_url) }
              }
            } else if (param.key === "apikey") {
              return { ...param, value: formData.apikey }
            }
            return param
          })

          const updatedExecutor = {
            ...field.fieldExecutor,
            queryParams,
          }

          try {
            const res = await executeApi(updatedExecutor)
            handleDropdownData(res, index)
          } catch (err) {
            throw err
          }
        }
      }
    }
  }

  const handleDropdownData = (data, index) => {
    setDropdownOptions((prevState) => {
      const updatedState = [...prevState]

      if (Array.isArray(data)) {
        updatedState[index] = data
      } else if (typeof data === "object" && data !== null) {
        for (const key in data) {
          if (Array.isArray(data[key])) {
            updatedState[index] = data[key]
            break
          }
        }
      } else {
        updatedState[index] = []
      }

      return updatedState
    })
  }

  const connectToTarget = async (event) => {
    event.stopPropagation()
    event.preventDefault()

    const requiredFields = selectedSource.fields?.filter((field) => !field.optional) || []
    const missingFields = requiredFields.filter((field) => !formData[field.name] || formData[field.name].trim() === "")

    if (missingFields.length > 0) {
      toast.error("Please fill all required fields", {
        position: "top-right",
        style: {
          "--toastify-icon-color-error": "red",
          "--toastify-color-progress-error": "red",
        },
      })
      return
    }

    if (formData.instance_url) {
      if (!isValidUrl(formData.instance_url)) {
        toast.error("Please enter a valid URL with domain extension (e.g. .com)", {
          position: "top-right",
          style: {
            "--toastify-icon-color-error": "red",
            "--toastify-color-progress-error": "red",
          },
        })
        return
      }
    }

    setIsLoading(true)
    try {

      const domain = extractSourceDomain(formData.instance_url)
      if (!domain) {
        throw new Error("Invalid URL format")
      }

      await fetchOptions()
      const connPayload = {
        domain: domain,
        userEmail: email,
        providerName: selectedSource.name,
        isSource: false,
        isTarget: true,
      }

      if (formData.accountEmail || formData.username) {
        connPayload["accountEmail"] = formData.accountEmail || formData.username
      } else {
        connPayload["accountEmail"] = ""
      }

      await updateConnDetails(connPayload)
      setIsConnected(true)
      setConnectionError(false)
      setCredentialsModified(false)
      toast.success("Connected successfully!", {
        position: "top-right",
        style: {
          "--toastify-icon-color-success": "green",
          "--toastify-color-progress-success": "green",
        },
      })
    } catch (error) {
      toast.error("Connection Failed. Please check your credentials and try again.", {
        position: "top-right",
        style: {
          "--toastify-icon-color-error": "red",
          "--toastify-color-progress-error": "red",
        },
      })
      setIsConnected(false)
      setConnectionError(true)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (e, fieldName) => {
    const newValue = e.target.value
    const oldValue = formData[fieldName] || ""
    if (["username", "password", "apikey", "instance_url"].includes(fieldName) && newValue !== oldValue) {
      setCredentialsModified(true)
      setIsConnected(false)
    }

    setFormData((prevData) => ({
      ...prevData,
      [fieldName]: newValue,
    }))
  }

  const handleReset = () => {
    setFormData({})
    setDropdownOptions([])
    setIsConnected(false)
    setConnectionError(false)
    setCredentialsModified(true)
    toast.info("All form fields have been reset", {
      position: "top-right",
      style: {
        "--toastify-icon-color-info": "green",
        "--toastify-color-progress-info": "green",
      },
    })
  }

  const moveToNextStep = async (event) => {
    event.preventDefault()
    setIsLoading(true)
    if (credentialsModified || !isConnected) {
      toast.error("Please validate your credentials before proceeding", {
        position: "top-right",
        style: {
          "--toastify-icon-color-error": "red",
          "--toastify-color-progress-error": "red",
        },
      })
      return
    }
    if (formData.instance_url) {
      if (!isValidUrl(formData.instance_url)) {
        toast.error("Please enter a valid URL with domain extension (e.g. .com)", {
          position: "top-right",
          style: {
            "--toastify-icon-color-error": "red",
            "--toastify-color-progress-error": "red",
          },
        })
        setIsConnected(false)
        setCredentialsModified(true)
        return
      }
    }
    if (dropDownData.length > 0) {
      const requiredDropdowns = dropDownData[0].fields.filter((field) => !field.optional)
      const missingDropdowns = requiredDropdowns.filter(
        (dropdown) => !formData[dropdown.key] || formData[dropdown.key] === "",
      )

      if (missingDropdowns.length > 0) {
        toast.error("Please select all required dropdown fields", {
          position: "top-right",
          style: {
            "--toastify-icon-color-error": "red",
            "--toastify-color-progress-error": "red",
          },
        })
        return
      }
    } setOpenDialog(true)

    // Show success toast notification
    toast.success(`Connected to ${selectedSource.name} profile!`, {
      position: "top-right",
      autoClose: 3000,
    })

    const obj = {
      target: selectedSource,
      formData: formData,
      isConnected: isConnected,
      domainType: dropDownData[0]?.domainType,
    }

    setMigrationState((prevState) => ({
      ...prevState,
      targetData: obj,
    }))

    const activityPayload = {
      email: email,
      activity: "Source and Target connected",
      // timestamp: Date.now(),
    }

    postActivity(activityPayload)
    await saveMigration(obj)
    setIsLoading(false)
    setTimeout(() => {
      setSelectedTab("3")
    }, 2000)
  }

  const saveMigration = async (obj) => {
    try {
      const payload = {
        plan_name: templateName,
        migration_target: migrationTarget,
        migration_objects: [],
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: sourceObjData,
          targetData: obj,
          dataTypeData: migrationState.dataTypeData,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
        },
      }

      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }

      const res = await saveMigrationPlan(payload)

      if (res?.message === "plan created successfully" && res?.response?.id) {
        setPlanId(res.response.id)
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Something went wrong!", {
        position: "top-right",
        style: {
          "--toastify-icon-color-error": "red",
          "--toastify-color-progress-error": "red",
        },
      })
    }
  }

  const handleDependentData = async (index, selectedValue, dependent) => {
    for (let j = 0; j < dependent.length; j++) {
      const dependentIndex = dependent[j] - 1

      const fieldExecutor = dropDownData[0].fields[dependentIndex].fieldExecutor
      const queryParams = fieldExecutor.queryParams.map((param) => {
        if (param.key.startsWith("domain")) {
          if (dropDownData[0].domainType === "subDomain") {
            return { ...param, value: extractSubdomain(formData.instance_url) }
          } else if (dropDownData[0].domainType === "sourceDomain") {
            return { ...param, value: extractSourceDomain(formData.instance_url) }
          }
        } else if (param.key === "apikey") {
          return { ...param, value: formData.apikey }
        } else if (param.key.startsWith(dropDownData[0].fields[index].fieldName.toLowerCase())) {
          param.value = selectedValue
        }
        return param
      })

      const updatedExecutor = {
        ...fieldExecutor,
        queryParams,
      }

      try {
        const res = await executeApi(updatedExecutor)
        handleDropdownData(res, dependentIndex)
      } catch (err) {
        console.error("API execution failed:", err)
      }
    }
  }

  return (
    <div>
      <div className={styles.dFlex}>
        <div className={styles.section} style={{ width: "35%" }}>
          <div className={styles.targetGraphic}>
            <div className={styles.targetConnectorContainer}>
              {selectedSource ? (
                <div style={{ position: "relative" }}>
                  <img src="/assets/Target-desktop.png" alt="Target Connector" className={styles.targetGraphicImage} />
                  <div className={styles.selectedTargetLogoOverlay}>
                    <div className={styles.selectedTargetLogo}>
                      <img src={selectedSource.imgUrl || "/placeholder.svg"} alt={selectedSource.name} />
                    </div>
                  </div>
                </div>
              ) : (
                <img src="/assets/Targetconnector.png" alt="Target Connector" className={styles.targetGraphicImage} />
              )}
            </div>
          </div>
        </div>
        <div className={styles.section} style={{ width: "65%" }}>
          <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", marginBottom: "0" }}>
            <div className={globalStyles.selectionName}>SELECT A TARGET PLATFORM</div>
            <div style={{ display: "flex", alignItems: "center", cursor: "pointer" }}
              onClick={() => {
                displayArticle("What target platforms are supported?")
              }}>
              <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
              <span className={globalStyles.guideName}>What target platforms are supported?</span>
            </div>
          </div>
          {selectedSource ? (
            <div>
              <button
                className={globalStyles.mainButton}
                style={{
                  width: "100%",
                  marginTop: "20px",
                  backgroundColor: "#FFFFFF",
                  color: "#514742",
                  border: "1px solid #DCDAD9",
                  boxShadow: "2px 2px 5px 0 rgba(0, 0, 0, 0.10)",
                }}
                onClick={openSelector}
              >
                Reselect a target platform{" "}
              </button>
              <Dialog
                open={showModel}
                onClose={() => setShowModel(false)}
                PaperProps={{
                  sx: { width: "705px", maxWidth: "none", backgroundColor: "#170903", marginBottom: "20px" },
                }}
              >
                <DialogContent
                  sx={{ backgroundColor: "#170903", padding: "0", marginBottom: "30px", marginTop: "5px" }}
                >
                  <SourceTargetSelector
                    showModel={showModel}
                    setShowModel={setShowModel}
                    data={data
                      .filter((target) => target.isTarget && target.name.toLowerCase() !== "csv")
                      .map((target) => ({
                        src: target.imgUrl,
                        name: target.displayName,
                        value: target.name,
                      }))}
                    name="TARGET"
                    onSelect={handleSelect}
                  />
                </DialogContent>
              </Dialog>

              <div style={{ marginTop: "20px" }}>
                <div className={globalStyles.selectionName}>
                  CONNECT TO A {selectedSource.name.toUpperCase()} TARGET PROFILE
                </div>
                <div style={{ marginTop: "10px" }} className={styles.dFlex}>
                  <span className={globalStyles.poppinsHeaderStyle}>Enter the details</span>
                  <div style={{ display: "flex", alignItems: "center", marginLeft: "auto", cursor: "pointer" }}
                    onClick={() => {
                      displayArticle(`How do I find my instance URL & ${selectedSource.name} API key?`)
                    }}>
                    <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                    <span className={globalStyles.guideName}>
                      How do I find my instance URL & {selectedSource.name} API key?
                    </span>
                  </div>
                </div>
                <div>
                  <form>
                    <div>
                      {selectedSource?.fields?.map((field) => (
                        <div key={field.name} className={styles.inputContainer}>
                          <input
                            className={`${styles.formControl} ${field.type === "password" ? styles.passwordInput : ""}`}
                            type={field.type === "password" && visibleFields[field.name] ? "text" : field.type}
                            placeholder={
                              field.placeholder
                                ? `${field.placeholder}${!field.optional ? "*" : ""}`
                                : `Enter value${!field.optional ? "*" : ""}`
                            }
                            value={formData[field.name] || ""}
                            onChange={(e) => handleInputChange(e, field.name)}
                          />

                          {field.type === "password" && (
                            <span className={styles.eyeIconContainer} onClick={() => toggleVisibility(field.name)}>
                              {visibleFields[field.name] ? (
                                <EyeIcon className={styles.eyeIcon} />
                              ) : (
                                <EyeOffIcon className={styles.eyeIcon} />
                              )}
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                    {!isConnected ? (
                      <div style={{ display: "flex", gap: "10px", marginTop: "20px" }}>
                        <button
                          type="button"
                          className={styles.resetButton}
                          style={{
                            padding: "10px 20px",
                            borderRadius: "4px",
                            border: "1px solid #EA5822",
                            background: "white",
                            color: "#EA5822",
                            cursor: "pointer",
                            fontWeight: "500",
                            flex: "1",
                          }}
                          onClick={handleReset}
                        >
                          Reset
                        </button>
                        <button
                          className={globalStyles.mainButton}
                          style={{ flex: "3" }}
                          onClick={connectToTarget}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <span className={styles.loaderContainer}>
                              <span className={styles.loader}></span>
                              <span>Connecting...</span>
                            </span>
                          ) : connectionError ? (
                            "Reconnect"
                          ) : (
                            "Connect"
                          )}
                        </button>
                      </div>
                    ) : (
                      <div>
                        <button className={globalStyles.connectedSuccess} style={{ width: "100%", marginTop: "20px" }}>
                          Connected Successfully!{" "}
                          <CheckIcon
                            className="eye-icon"
                            style={{
                              color: "green",
                              marginLeft: "10px",
                            }}
                          />
                        </button>

                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "end",
                            marginTop: "20px",
                            cursor: "pointer"
                          }}
                          onClick={() => {
                            displayArticle("What is a workspace?")
                          }}
                        >
                          <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                          <span className={globalStyles.guideName}>What is a workspace?</span>
                        </div>

                        {dropDownData[0]?.fields.map((dropdown, index) => (
                          <FormControl key={dropdown.key} fullWidth className={styles.customDropdownContainer}>
                            <Select
                              key={index}
                              className={styles.customDropdownSelect}
                              value={formData[dropdown.key] || ""}
                              displayEmpty
                              renderValue={(selected) => {
                                if (!selected)
                                  return (
                                    <span style={{ color: "#746B68" }}>
                                      Select {dropdown.fieldName}
                                      {!dropdown.optional ? "*" : ""}
                                    </span>
                                  )

                                const selectedOption = dropdownOptions[index]?.find(
                                  (option) => option[dropdown.accessor] === selected,
                                )

                                return <span style={{ color: "#746B68" }}>{selectedOption?.[dropdown.attribute]}</span>
                              }}
                              onChange={(e) => {
                                handleInputChange(e, dropdown.key)
                                if (dropdown.dependent?.length > 0) {
                                  handleDependentData(index, e.target.value, dropdown.dependent)
                                }
                              }}
                            >
                              <MenuItem value="">Select {dropdown.fieldName}</MenuItem>
                              {dropdownOptions[index]?.map((option) => (
                                <MenuItem key={option[dropdown.accessor]} value={option[dropdown.accessor]}>
                                  {option[dropdown.attribute]}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        ))}

                        <div style={{ display: "flex", gap: "10px", marginTop: "20px" }}>
                          <button
                            type="button"
                            className={styles.resetButton}
                            style={{
                              padding: "10px 20px",
                              borderRadius: "4px",
                              border: "1px solid #EA5822",
                              background: "white",
                              color: "#EA5822",
                              cursor: "pointer",
                              fontWeight: "500",
                              flex: "1",
                            }}
                            onClick={handleReset}
                          >
                            Reset
                          </button>
                          <button
                            className={globalStyles.mainButton}
                            style={{ flex: "3" }}
                            onClick={moveToNextStep}
                            disabled={!formValid || !isConnected || credentialsModified}
                          >
                            Confirm & continue
                          </button>
                        </div>

                        {/* {openDialog && (
                          <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
                            <DialogContent sx={{ backgroundColor: "#170903", padding: "60px 100px" }}>
                              <span className={globalStyles.selectionName} style={{ fontSize: "16px" }}>
                                Connected to {selectedSource.name} profile!
                              </span>
                            </DialogContent>
                          </Dialog>
                        )} */}
                      </div>
                    )}
                  </form>
                </div>
              </div>
            </div>
          ) : (
            <div>
              <button
                className={globalStyles.mainButton}
                style={{ width: "100%", marginTop: "20px" }}
                onClick={openSelector}
              >
                Select a target platform
              </button>
              <Dialog
                open={showModel}
                onClose={() => setShowModel(false)}
                PaperProps={{
                  sx: { width: "705px", maxWidth: "none", backgroundColor: "#170903", marginBottom: "20px" },
                }}
              >
                <DialogContent
                  sx={{ backgroundColor: "#170903", padding: "0", marginBottom: "30px", marginTop: "5px" }}
                >
                  <SourceTargetSelector
                    showModel={showModel}
                    setShowModel={setShowModel}
                    data={data
                      .filter((target) => target.isTarget && target.name.toLowerCase() !== "csv")
                      .map((target) => ({
                        src: target.imgUrl,
                        name: target.displayName,
                        value: target.name,
                      }))}
                    name="TARGET"
                    onSelect={handleSelect}
                  />
                </DialogContent>
              </Dialog>
            </div>
          )}
        </div>
      </div>
      <ToastContainer />
    </div>
  )
}