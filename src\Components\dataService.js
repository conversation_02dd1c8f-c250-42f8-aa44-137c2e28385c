import { useState } from 'react';
import { BehaviorSubject } from 'rxjs';

export function useDataService(apiService) {
    const [plans, setPlans] = useState(null);
    const [migrationHistory, setMigrationHistory] = useState(null);
    const [migrationProviders, setMigrationProviders] = useState(null);

    const plansDataSubject = new BehaviorSubject(null);
    const plansData = plansDataSubject.asObservable();

    const getMigrationPlans = (email, force = false) => {
        return new Promise((resolve, reject) => {
            if (plans && plans.length > 0 && !force) {
                resolve('success');
            } else {
                apiService.getMigrationPlans(email)
                    .subscribe({
                        next: (res) => {
                            const temp = res;
                            setPlans(temp);
                            resolve('success');
                        },
                        error: (err) => {
                            reject(err);
                        }
                    });
            }
        });
    };

    const getMigrationHistory = (email, force = false) => {
        return new Promise((resolve, reject) => {
            if (migrationHistory && migrationHistory.length > 0 && !force) {
                resolve('success');
            } else {
                apiService.getMigrationHistory(email)
                    .subscribe({
                        next: (res) => {
                            const temp = res;
                            setMigrationHistory(temp);
                            resolve('success');
                        },
                        error: (err) => {
                            reject(err);
                        }
                    });
            }
        });
    };

    const getMigrationProviders = (force = false) => {
        return new Promise((resolve, reject) => {
            if (migrationProviders && !force) {
                resolve('success');
            } else {
                apiService.getMigrationProviders()
                    .subscribe({
                        next: (res) => {
                            const temp = res;
                            setMigrationProviders(temp);
                            resolve('success');
                        },
                        error: (err) => {
                            reject(err);
                        }
                    });
            }
        });
    };

    const getMigrationHistoryV1 = (email, force = false) => {
        return new Promise((resolve, reject) => {
            if (migrationHistory && migrationHistory.length > 0 && !force) {
                resolve('success');
            } else {
                apiService.getMigrationHistoryV1(email)
                    .subscribe({
                        next: (res) => {
                            const temp = res;
                            setMigrationHistory(temp);
                            resolve('success');
                        },
                        error: (err) => {
                            reject(err);
                        }
                    });
            }
        });
    };

    return {
        // Expose state
        plans,
        migrationHistory,
        migrationProviders,
        plansData,

        // Expose methods
        getMigrationPlans,
        getMigrationHistory,
        getMigrationProviders,
        getMigrationHistoryV1,

        // Expose setters (if needed elsewhere)
        setPlans,
        setMigrationHistory,
        setMigrationProviders
    };
}
