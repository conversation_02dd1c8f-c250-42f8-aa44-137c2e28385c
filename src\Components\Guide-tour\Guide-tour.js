import React, {useEffect, useRef, useState} from "react";
import "./Guide-tour.css";
import "../global_styles.css";
// import {ChevronDownIcon, ChevronUpIcon, ExternalLinkIcon, Cher} from "@heroicons/react/solid";
import {ChevronDownIcon, ChevronUpIcon, ExternalLinkIcon} from "@heroicons/react/solid";

export default function GuideTour({selectedGuideIndex, isOpen, onClose}){

    const drawerRef = useRef(null);
    const [expanded, setExpanded] = useState(null);

    useEffect(() => {
        if (isOpen) {
            setExpanded(selectedGuideIndex);
        }
    }, [selectedGuideIndex, isOpen]);

    useEffect(() => {
        let clickOutsideListener;

        if (isOpen) {
            const handleClickOutside = (event) => {
                if (drawerRef.current && !drawerRef.current.contains(event.target)) {
                    onClose();
                }
            };
            clickOutsideListener = () => document.addEventListener("click", handleClickOutside);
            setTimeout(clickOutsideListener, 50);
            return () => {
                document.removeEventListener("click", handleClickOutside);
            };
        }
    }, [isOpen, onClose]);

    const toggleExpand = (index) => {
        setExpanded(expanded === index ? null : index);
    };

    const handleClickInside = (event) => {
        event.stopPropagation();
    }

    const guide = [
    {item: 1, title: "Guide to prepare for a migration", video_url:'', subSteps:
       [
        {stepNo: 'Step NO', description: "Description of the action to be taken"},
         {stepNo: 'Step NO', description: "Description of the action to be taken"},
         {stepNo: 'Step NO', img: '/assets/sample-img-guide.png',description: "Description of the action to be taken"}
      ]
    },
    {item: 2, title: "Setting up a migration in 4 simple steps",video_url:'', subSteps:
          [
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', img: '/assets/sample-img-guide.png', description: "Description of the action to be taken"}
          ]
    },
    {item: 3, title: "Creating & using templates for faster migration", video_url:'', subSteps:
          [
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', img: '', description: "Description of the action to be taken"}
          ]
    },
    {item: 4, title: "Data mapping and transformation tips", video_url:'', subSteps:
          [
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', img: '', description: "Description of the action to be taken"}
          ]
    },
    {item: 5, title: "Understanding migration reports and logs", video_url:'', subSteps:
          [
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', description: "Description of the action to be taken"},
            {stepNo: 'Step NO', img: '', description: "Description of the action to be taken"}
          ]
    },
  ]
  return (
      <div className={`guide-container guide-drawer ${isOpen ? 'open' : ''}`} ref={drawerRef} onClick={handleClickInside}>
          <div className="guide-header">
              <img
                  src="/assets/saasgenie_logo.png"
                  alt="Your Migration Guide Logo"
                  className="guide-logo"
              />
              <h2>YOUR MIGRATION GUIDE</h2>
          </div>

          <div>
              {guide.map((item, index) => (
                  <div key={item.item} className={`${expanded === index ? 'guide-card': ''}`}>
                      <div className={`guide-question ${expanded === index ? '': 'normal-guide'}`} onClick={() => toggleExpand(index)}>
                          <span className="expanded-title">{item.title}</span>
                          {expanded === index ? (
                              <ChevronUpIcon className="expand-icon"/>
                          ) : (
                              <ChevronDownIcon className="expand-icon"/>
                          )}
                      </div>

                      {expanded === index && (
                          <div className="guide-steps">
                              <div className="video-placeholder">
                                  <button className="play-button">▶</button>
                              </div>
                              {item.subSteps.map((step, stepIndex) => (
                                  <div className="guide-step" key={stepIndex}>
                                      <h3 className="step-header">{step.stepNo}</h3>
                                      {step.img && <img src={step.img} style={{width: "400px", height: "200px"}} alt="step" className="step-image"/>}
                                      <p className="step-description">{step.description}</p>
                                      {stepIndex !== item.subSteps.length - 1 && <hr className="hr-div" />}
                                  </div>
                              ))}

                              <div className="feedback-section">
                                  <p className="feedback-head">Did this answer your question?</p>
                                  <div className="feedback-icons">
                                      <div className="hover-zoom">😞</div> <div className="hover-zoom">😐</div> <div className="hover-zoom">😊</div>
                                  </div>
                                  <p className="chat-link">Still facing issues? Chat with our genie</p>
                              </div>
                          </div>
                      )}
                  </div>
              ))}
          </div>
      </div>
  );
};

