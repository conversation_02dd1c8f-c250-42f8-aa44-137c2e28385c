.resources-container {
    display: flex;
    gap: 20px;
    padding: 20px;
    margin: 0 auto;
    width: 100%;
}

.section {
    flex: 1;
    width: 50%;
    padding: 20px;
    box-sizing: border-box;
}

.quick-guides-title {
    color: #EA5822;
    font-size: 14px;
    /*margin-bottom: 20px;*/
    font-family: Poppins;
    font-weight: 700;
    line-height: 24px;
}

.guides-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.guides-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #DCDAD9;
    font-size: 16px;
    color: #170903;
    font-weight: 400;
    font-family: Inter;
    line-height: 24px;
}

.help-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
}

.video-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #DCDAD980;
    padding: 40px;
    border-radius: 8px;
    width: 100%;
    height: 300px;
    box-sizing: border-box;
}

.video-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.video-iframe {
    width: 100%;
    height: 100%;
    border-radius: 12px;
    border: 0;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    background: #fff;
}

.play-button {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    border: none;
    background-color: #EA5822;
    color: #fff;
    font-size: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(234,88,34,0.15);
    transition: background-color 0.3s;
}

.play-button:hover {
    background-color: #d44d1a;
}

.text-video {
    font-size: 16px;
    color: #170903;
    font-weight: 400;
    font-family: Inter;
    line-height: 24px;
    margin-bottom: 24px;
    text-align: center;
}

