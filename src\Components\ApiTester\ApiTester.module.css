.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}


.header {
    display: flex;
    /*flex-wrap: wrap;*/
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    gap: 16px;
}

.methodSelect {
    width: 140px;
}

.urlInput {
    flex: 1;
    min-width: 250px;
}

.actionButtons {
    display: flex;
    gap: 12px;
}

.select {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #fff;
    font-size: 14px;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24'%3E%3Cpath fill='%23555' d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px top 50%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: border-color 0.2s, box-shadow 0.2s;
    margin-bottom: 10px;
}

/*.select:focus {*/
/*    outline: none;*/
/*    border-color: #3f87f5;*/
/*    box-shadow: 0 0 0 3px rgba(63, 135, 245, 0.15);*/
/*}*/

/*.input {*/
/*    width: 100%;*/
/*    padding: 12px;*/
/*    border: 1px solid #e0e0e0;*/
/*    border-radius: 6px;*/
/*    font-size: 14px;*/
/*    transition: border-color 0.2s, box-shadow 0.2s;*/
/*}*/

/*.input:focus {*/
/*    outline: none;*/
/*    border-color: #3f87f5;*/
/*    box-shadow: 0 0 0 3px rgba(63, 135, 245, 0.15);*/
/*}*/

/* Buttons */


.secondaryButton:hover {
    background-color: #e8e8e8;
}


.tabContainer {
    margin-top: 24px;
}

.tabButtons {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 0;
    overflow-x: auto;
    scrollbar-width: none;
    font-family: Poppins;
}

.tabButtons::-webkit-scrollbar {
    display: none;
}

.tabButton {
    padding: 12px 20px;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.2s;
    white-space: nowrap;
    font-family: Poppins;
    font-weight: 600;
    font-size: 16px;
}

.tabButton:hover {
    color: #333;
}

.activeTab {
    color: #EF8963;
    border-bottom: 2px solid #EF8963;
}

.tabContent {
    padding: 24px 0;
}


.section {
    margin-bottom: 24px;
}

.sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.sectionHeader h3 {
    margin: 0;
    font-family: Inter;
    font-weight: 600;
    font-size: 16px;
    color: #514742;

}

.iconButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 25px;
    /*border-radius: 50%;*/
    background-color: #EF8963;
    border: none;
    cursor: pointer;
    color: #555;
    font-size: 18px;
    transition: background-color 0.2s;
}


.table {
    width: 100%;
    border-collapse: collapse;
}

.tableRow {
    border-bottom: 1px solid #f0f0f0;
}

.tableRow:last-child {
    border-bottom: none;
}

.tableCell {
    padding: 12px 8px;
    vertical-align: top;
}

.inputGroup {
    display: flex;
    flex-direction: column;
}

.inputLabel {
    font-size: 12px;
    color: #777;
    margin-bottom: 6px;

}

.removeButton {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #ff5252;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    /*margin-top: 20px;*/
}



.formGroup {
    margin-bottom: 16px;

}
.formGroupWithMargin {
    /*margin-bottom: 16px;*/
    margin: 20px;
}

.label {
    display: block;
    margin-bottom: 8px;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #514742;
}

.note {
    font-family: Inter;
    font-weight: 400;
    font-size: 12px;
    color: #746B68;
}


.accordion details {
    margin-bottom: 16px;
    border: 1px solid #eee;
    border-radius: 6px;
    overflow: hidden;
}

.accordion details[open] {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

}

.accordion summary {
    padding: 14px 16px;
    cursor: pointer;
    font-weight: 500;
    background-color: #EFEEED;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
    font-family: Poppins;
    font-size: 14px;
    color: #514742;
}

.accordion summary:hover {
    background-color: #f2f2f2;
}

.accordionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: auto;

}
.accordionContent{
    padding: 20px;
    margin: 20px;
}


.radioGroup {
    display: flex;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 16px;
}

.radioLabel {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: Inter;
    font-weight: 400;
    color: #746B68;
    justify-content: center;
    padding-bottom: 5px;
}

.radioInput {
    margin-right: 8px;
    accent-color: #EF8963;
}

.rawBody {
    margin-top: 16px;
}

.jsonEditor, .xmlEditor {
    width: 95%;
    min-height: 220px;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    resize: vertical;
    background-color: #f9f9f9;
    tab-size: 2;
}

.jsonEditor:focus, .xmlEditor:focus {
    outline: none;
    border-color: #3f87f5;
    box-shadow: 0 0 0 3px rgba(63, 135, 245, 0.15);
}

/* Response section */
.responseSection {
    margin-top: 32px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.responseHeader {
    padding: 14px 16px;
    background-color: #f5f5f5;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.responseContent {
    padding: 16px;
}

.jsonResponse {
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    font-size: 13px;
    line-height: 1.5;
    background-color: #f9f9f9;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    color: #333;
}

/* Loading indicator */
.loadingSpinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 120px;
    color: #3f87f5;
}

.loadingSpinner::after {
    content: '';
    width: 32px;
    height: 32px;
    border: 3px solid #e0e0e0;
    border-top: 3px solid #3f87f5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


.methodSelect select option[value="GET"] {
    background-color: #61affe;
    color: white;
}

.methodSelect select option[value="POST"] {
    background-color: #49cc90;
    color: white;
}

.methodSelect select option[value="PUT"] {
    background-color: #fca130;
    color: white;
}

.methodSelect select option[value="DELETE"] {
    background-color: #f93e3e;
    color: white;
}

.methodSelect select option[value="PATCH"] {
    background-color: #50e3c2;
    color: white;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .methodSelect, .urlInput, .actionButtons {
        width: 100%;
        margin-bottom: 12px;
    }

    .actionButtons {
        justify-content: space-between;
    }

    .tabButton {
        padding: 10px 14px;
        font-size: 13px;
    }

    .primaryButton, .secondaryButton {
        padding: 10px 14px;
    }
}
