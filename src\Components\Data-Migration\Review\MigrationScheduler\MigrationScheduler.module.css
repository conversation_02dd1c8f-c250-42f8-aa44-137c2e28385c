.closeButton {
    background: transparent;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    position: absolute;
    top: 10px;  /* Adjust as needed */
    right: 10px; /* Moves it to the right */
}
.container{
    background-color: #170903;
    color: #ffffff;
    padding: 24px;
    border-radius: 8px;
    width: 500px;
}
.note{
    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #B9B5B3;
    margin: 15px 0;

}
.note2{
    font-family: Poppins;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    color:#97908E;


}
.headerStyle{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: #514742;
    border-radius: 4px;
    margin: 8px;
}

.headerSelectStyle{
    background-color: transparent;
    color: #EA5822;
    border: none;
    font-weight: 700;
    cursor: pointer;
    padding: 2px;
    outline: none;
}
.dFlex{
    display: flex;
    align-items: center;
}
.dropdownContainer {
    position: relative;
    flex: 0 0 auto;
}
.dropdownContainer1 {
    position: relative;
    flex: 1;
}

.dropdownSelect {
    background-color: #333333;
    color: white;
    padding: 12px;
    padding-right: 30px;
    border-radius: 4px;
    border: none;
    width: 100%;
}

.dropdownArrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}
.migrationMessage {
    font-family: Poppins;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    color: #B9B5B3;
    background-color: #514742;
    height: 55px;
    display: flex;
    /*justify-content: center;*/
    align-items: center;
    margin: 15px 0;
    padding: 0 5px;


}

.migrationTime {
    color: #EA5822;
    font-family: Inter;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    margin-left:10px;
}


