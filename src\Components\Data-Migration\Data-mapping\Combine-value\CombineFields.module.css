.combineFieldsContainer {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
  }

  .header {
    width: 100%;
  }

  .titleContainer {
    background-color: #170903;
    color: #EA5822;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
  }

  .title {
    margin: 0;
    color: #EA5822;
    font-family: Poppins, sans-serif;
    font-size: 27px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 88.889% */
}


  .closeIcon {
    cursor: pointer;
    width: 24px;
    height: 24px;
    color: white;
  }

  .mappingGuide {
    background-color: #f3f2f1;
    padding: 12px 20px;
    display: flex;
    align-items: center;
  }

  .guideLabel {
    color: #EA5822;
    font-family: Poppins, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 171.429% */
    margin-right: 10px;
}


.guideText {
    color: #170903;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
}


  .content {
    padding: 20px;
  }

  .formRow {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
  }

  .label {
    width: 180px;
    margin-right: 20px;
    font-size: 14px;
    color: #746B68;
    font-family: Poppins, sans-serif;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 171.429% */
    padding-top: 8px;
}


.value {
    color: #170903;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    padding-top: 4px;
}


  .fieldSelectionContainer {
    flex-grow: 1;
  }

  .selectWithButton {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .selectField {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    flex-grow: 1;
    width: 400px;
    font-family: Inter, sans-serif;
    appearance: menulist-button; /* This ensures the dropdown appears below */
    position: relative;
  }

  .addButton {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .addButton:hover {
    background-color: #f5f5f5;
  }

  .plusIcon {
    width: 16px;
    height: 16px;
    color: #333;
  }

  .selectedFieldsContainer {
    margin: 20px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-left: 200px;
  }

  .selectedField {
    background-color: #f3f2f1;
    border-radius: 4px;
    padding: 6px 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-family: Inter, sans-serif;
    color: #170903;
  }

  .removeFieldIcon {
    cursor: pointer;
    width: 14px;
    height: 14px;
    color: #666;
  }

  .removeFieldIcon:hover {
    color: #EA5822;
  }

  .buttonContainer {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }

  .combineButton {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    color: #333;
  }

  .combineButton:hover {
    background-color: #f5f5f5;
  }


  .sourceFieldOption {
    color: #170903;
    font-family: "Inter";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
}

/* Custom dropdown styles */
.customDropdown {
    position: relative;
    width: 400px;
    flex-grow: 1;
}

.dropdownButton {
    width: 100%;
    padding: 8px 12px;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    text-align: left;
    cursor: pointer;
    font-family: Inter, sans-serif;
    color: #170903;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdownOptions {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 4px 4px;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 0;
    transform: translateY(0);
}

.dropdownOption {
    padding: 8px 12px;
    cursor: pointer;
    font-family: Inter, sans-serif;
    color: #170903;
}

.dropdownOption:hover {
    background-color: #f5f5f5;
}

/* Original select styles (kept for compatibility) */
select {
    direction: ltr !important;
    position: relative !important;
}

select option {
    font-family: Inter, sans-serif !important;
    color: #170903 !important;
    background-color: white !important;
}
