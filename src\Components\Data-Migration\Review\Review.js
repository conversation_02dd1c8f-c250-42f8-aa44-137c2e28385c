import React, { useContext, useEffect, useRef, useState } from "react"
import styles from "./Review.module.css"
import globalStyles from "../../globalStyles.module.css"
import { Dialog, DialogContent, FormControl, FormControlLabel, MenuItem, Select, Switch } from "@mui/material"
import { HiCalendarDays, HiXMark } from "react-icons/hi2"
import { HiPlus } from "react-icons/hi"
import MigrationScheduler from "./MigrationScheduler/MigrationScheduler"
import { useNavigate, useLocation } from "react-router-dom"
import Pay from "./Pay/Pay"
import PaymentDetails from "./paymentdetails"
import { migrationTemplate, postActivity, saveMigrationPlan } from "../../apiService"
import { MigrationContext } from "../Data-Migration"
import { toast, ToastContainer } from "react-toastify"
import { displayArticle } from "../../../Helper/helper"

export default function Review({ isSample, paymentSuccess, templateName, planId, setPlanId }) {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const source = migrationState.sourceObjData
  const targetData = migrationState.targetData
  const dataMappingData = migrationState.dataMappingData
  const dataTypeData = migrationState.dataTypeData
  const selectedObjectData = migrationState.selectedObjectData
  const selectedEntityData = migrationState.selectedEntityData
  const sourceExeRes = migrationState.sourceExeRes
  const targetExeRes = migrationState.targetExeRes
  const [hasSampleMigration, setHasSampleMigration] = useState(false)
  const [sampleMigrationCount, setSampleMigrationCount] = useState(0)
  const [sampleMigrationLimitReached, setSampleMigrationLimitReached] = useState(false)

  const timerRef = useRef(null)
  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [fields, setFields] = React.useState({
    totalFields: 40,
    mappedFields: 32,
    skippedFields: 3,
    unmappedFields: 15,
    errorsInMapping: 0,
    estimatedTime: "3 hours 25mins",
  })
  const countryCodes = [
    { code: "+91", name: "India" },
    { code: "+1", name: "United States" },
    { code: "+44", name: "United Kingdom" },
    { code: "+61", name: "Australia" },
    { code: "+1", name: "Canada" },
  ]
  const [targetMappingConfig, setTargetMappingConfig] = useState({
    targetMappingObjectKey: "incidents",
  })
  const [sampleBatchSize, setSampleBatchSize] = useState("")
  const [liveMigrationPayload, setLiveMigrationPayload] = useState({})
  const [isFromMigration, setIsFromMigration] = useState(false)

  const navigate = useNavigate()
  const location = useLocation()

  const [isSampleMigration, setIsSampleMigration] = useState(isSample !== undefined ? isSample : true)
  const [isPaymentSucces, setIsPaymentSucces] = useState(paymentSuccess)
  const [payload, setPayload] = useState({
    source: [{}],
    target: [{}],
  })



  useEffect(() => {
    setIsPaymentSucces(paymentSuccess)
  }, [paymentSuccess])

  useEffect(() => {
    if (location.state && location.state.fromMigration) {
      setIsFromMigration(true)
    }
  }, [location.state])

  useEffect(() => {
    if (location.state) {
      if (location.state.templateName && location.state.templateName !== "Name of the Template") {
        setMigrationState((prevState) => ({
          ...prevState,
          templateName: location.state.templateName,
        }))
      }
      if (location.state.fromMigration) {
        if (location.state.migrationData) {
          const { sampleBatchSize: batchSize, tag: tagValue, migrationProgress } = location.state.migrationData
          if (batchSize) setSampleBatchSize(batchSize)
          if (tagValue) setTag(tagValue)

          // If coming back from a completed migration (progress was reset to 0),
          // clear any stored migration state and reset isFromMigration to allow new migrations
          if (migrationProgress === 0) {
            localStorage.removeItem("migrationProgress")
            localStorage.removeItem("migrationId")
            setIsFromMigration(false) // Reset this flag for completed migrations
          } else {
            // Only set isFromMigration to true if migration is not completed
            setIsFromMigration(true)
          }
        } else {
          setIsFromMigration(true)
        }
      }
    }
  }, [location.state, setMigrationState])

  useEffect(() => {
    if (migrationState.templateName && migrationState.templateName !== "Name of the Template") {
      setMigrationState((prevState) => ({
        ...prevState,
        templateName: templateName,
      }))
    }
  }, [templateName, setMigrationState])

  useEffect(() => {
    if (!isSampleMigration) {
      const updatedPayload = constructPayload()

      updatedPayload.target = updatedPayload?.target.map((target) => ({
        ...target,
        batchStrategy: {
          limit: source.apiRateLimit ? Number(source.apiRateLimit) : 160,
          limitBy: "minute",
          interval: 30,
          migrateSample: false,
          sampleSize: sampleBatchSize,
          immediateFirstBatch: false,
          retry: false,
          createErrorBatchOnEmptyRecords: true,
          createErrorBatchOnApiError: true,
        },
      }))
      const transformationPayload = {
        name: migrationState.templateName,
        executedBy: email,
        partnerKey: "3fde0cf904f891223fd5131bb989b49a",
        partner: "dev-saasgenie",
        scheduledTime: liveMigrationOption === "later" ? new Date(migrationDate) : Date.now(),
        transformationChunkMethod: selectedObjectData.additional_details
          ? selectedObjectData.additional_details.transformationChunkMethod
            ? selectedObjectData.additional_details.transformationChunkMethod
            : "default"
          : "default",
        transformationModels: [updatedPayload],
      }

      setLiveMigrationPayload(transformationPayload)
    }
  }, [isSampleMigration])

  const [liveMigrationOption, setLiveMigrationOption] = useState("")
  const [contacts, setContacts] = useState([{ email: "", phone: "", code: "+91" }])
  const [sentNotification, setSentNotification] = useState(false)
  const [sentDetailsToMyself, setSentDetailsToMyself] = useState(false)
  const [migrationDate, setMigrationDate] = useState("")
  const [showPricing, setShowPricing] = useState(false)
  const [tag, setTag] = useState("")
  const [targetsCopy, setTargetsCopy] = useState(null)

  const pieData = [
    { name: "Mapped", value: fields.mappedFields, color: "#EF8963" },
    { name: "Errors", value: fields.errorsInMapping, color: "#A43E18" },
    { name: "Empty", value: fields.totalFields - fields.mappedFields - fields.errorsInMapping, color: "#f5f5f5" },
  ]

  const handleSourceValueChange = (newValue) => {
    setLiveMigrationOption(newValue)
  }

  const addContact = () => {
    setContacts([...contacts, { email: "", phone: "", code: "+91" }])
  }
  const removeContact = () => {
    setContacts(contacts.slice(0, -1))
  }
  const [open, setOpen] = useState(false)

  const getchunkStrategyLimit = () => {
    const key = selectedObjectData?.chunkStrategyKey || "count"
    const { default: sourceType } = source?.type || {}

    if (source?.type === "api" && typeof source.queryParam === "object") {
      const paramValue = source.queryParam[key]
      return paramValue && !isNaN(Number(paramValue)) ? Number(paramValue) : 10
    }

    return source?.type === "csv" ? source.csvBatchSize : 10
  }
  const getPaginationFields = () => {
    if (selectedObjectData.paginationFields && selectedObjectData.paginationFields !== "") {
      return selectedObjectData.paginationFields
    }
    return {
      offsetField: "page",
      limitField: "per_page",
    }
  }

  const getDateRangeFields = () => {
    if (selectedObjectData.dateRangeFields && selectedObjectData.dateRangeFields !== "") {
      return selectedObjectData.dateRangeFields
    }
    return {
      datefield: "SUBMIT_DATE_UT",
      separator: ":",
      startDelimiter: "(",
      endDelimiter: ")",
      rangeSeparator: ";",
      incrementType: "day",
    }
  }
  const getMappingType = (sourcefield, isSourceFieldAvailable, override, defaultValue) => {
    if (isSourceFieldAvailable && (!override || (override && override.length == 0)) && defaultValue == "") {
      return "sourceAttribute"
    } else if (override && override.length > 0) {
      return ""
    } else if (defaultValue !== "") {
      return ""
    }
    return ""
  }
  const getFieldValue = (type) => {
    if (type == "string") {
      return "text"
    } else if (type == "number") {
      return "number"
    }
    return "text"
  }

  const getCombinedFieldsJs = (arr, isDelimiterIncluded = false, delimiter = "") => {
    let script = "target = '';\n"
    for (var i = 0; i < arr.length; i++) {
      const field = arr[i]
      script += "if(source['" + field + "'] != null && source['" + field + "'] != '') {\n"
      script += "    target = target + '" + field + ": ' + source['" + field + "']"
      if (isDelimiterIncluded && i !== arr.length - 1) {
        script += " + '" + delimiter + "';\n"
      } else if (i !== arr.length - 1) {
        script += " + '\\n';\n"
      } else {
        script += ";\n"
      }
      script += "}\n"
    }
    return script
  }
  const getValuesByColumnName = (columnName) => {
    if (source.uniqueSourceValues) {
      const columnObject = source.uniqueSourceValues.find((obj) => obj.column === columnName)
      return columnObject ? columnObject.values : []
    }
    return []
  }

  const getSourceMappingObjectKey = (sourceMappingConfig) => {
    return sourceMappingConfig?.sourceMappingObjectKey?.trim()
      ? sourceMappingConfig.sourceMappingObjectKey
      : "ticket_fields"
  }

  const getSourceMappingFieldName = (sourceMappingConfig) => {
    return sourceMappingConfig?.fieldName?.trim() ? sourceMappingConfig.fieldName : "title"
  }

  const getChoiceMappingAttribute = (sourceMappingConfig) => {
    return sourceMappingConfig?.mappingAttribute?.trim() ? sourceMappingConfig.mappingAttribute : "title"
  }
  const getSourceFieldDropdownValues = (name, attribute) => {
    if (source.type === "csv") {
      if (source.uniqueSourceValues) {
        const key = attribute.sourcefield !== "" ? attribute.sourcefield : undefined
        return getValuesByColumnName(key)
      }
    } else if (source.type === "api") {
      const sourceMappingKey = getSourceMappingObjectKey(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )
      const choiceKey = getChoiceMappingAttribute(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )
      const fieldName = getSourceMappingFieldName(
        selectedObjectData?.sourceMappingConfig ? selectedObjectData.sourceMappingConfig : "",
      )

      if (sourceExeRes) {
        if (name === "Departments") {
          return sourceExeRes[name].map((obj) => obj.title)
        } else if (name === selectedEntityData?.mainTarget[0].name) {
          if (sourceExeRes[sourceMappingKey] && sourceExeRes[sourceMappingKey].length > 0) {
            for (const field of sourceExeRes[sourceMappingKey]) {
              if (attribute.sourcefield === field[fieldName]) {
                return field.choices.map((choice) => choice[choiceKey])
              }
            }
          }
        } else {
          if (sourceExeRes[name]) {
            return sourceExeRes[name].map((obj) => obj.name)
          }
          return []
        }
      }
      return []
    }
    return []
  }
  const getOverrideValuesforTags = (field) => {
    const temp = []
    if (source.uniqueSourceValues) {
      const originalList = getSourceFieldDropdownValues("Tickets", field)
      const staticValue = tag
      const overrideList = originalList.map((item) => {
        const sourcevalue = item
        const sourcevalue1 = item.startsWith('"') && item.endsWith('"') ? item : `"${item}"`
        const targetvalue = sourcevalue1
          .replace(/^"|"$/g, "")
          .split(",")
          .map((substring) => substring.trim())
          .concat(staticValue)
        return {
          sourcevalue,
          targetvalue,
        }
      })
      return overrideList
    }
    return temp
  }
  const replaceCommonValues = (script, replacements) => {
    let updatedScript = script
    for (const [key, value] of Object.entries(replacements)) {
      const regex = new RegExp(`this\\.${key}`, "g")
      updatedScript = updatedScript.replace(regex, `"${value}"`)
    }
    return updatedScript
  }
  const getDefaultValueFromTicketFields = (fieldName) => {
    let defaultValue = ""

    if (targetExeRes && targetExeRes.ticketFields) {
      for (const field of targetExeRes.ticketFields) {
        if (
          (field.name && field.name.toLowerCase() === fieldName.toLowerCase()) ||
          (field.label && field.label.toLowerCase() === fieldName.toLowerCase())
        ) {
          if (field.choices && field.choices.length > 0) {
            if (
              fieldName === "status" ||
              fieldName === "priority" ||
              fieldName === "source" ||
              fieldName === "urgency" ||
              fieldName === "impact"
            ) {
              defaultValue = field.choices[0]["id"]
            } else {
              defaultValue = field.choices[0]["value"]
            }
          }
        }
      }
    }

    return defaultValue
  }
  const getTicketOverrideValues = (field, item) => {
    let key = "value"
    if (
      field.targetfield == "status" ||
      field.targetfield == "priority" ||
      field.targetfield == "source" ||
      (item.name == "Purchase Order" && field.is_custom_field)
    ) {
      key = "id"
    }

    const override = []
    if (field.mappings && field.mappings.length > 0) {
      for (const obj of field.mappings) {
        for (const source of obj.sourcevalue) {
          const map = {
            sourcevalue: source,
            targetvalue: obj.targetvalue[key],
          }
          if (field.type.includes("array")) {
            if (field.type.includes("array")) {
              if (Array.isArray(obj.targetvalue)) {
                map["targetvalue"] = obj.targetvalue.map((item) => item[key])
              } else if (typeof obj.targetvalue === "object" && obj.targetvalue !== null) {
                map["targetvalue"] = [obj.targetvalue[key]]
              }
            }
          }

          if (item.name == "Purchase Order" && field.type == "number") {
            map["targetvalue"] = Number(obj.targetvalue[key])
          }
          override.push(map)
        }
      }
    }
    return override
  }

  const overrideTicketFields = (target, field) => {
    const override = []

    if (target.mappings && target.mappings.length > 0) {
      for (const obj of target.mappings) {
        for (const source of obj.sourcevalue) {
          const targetValue = obj.targetvalue?.[field.targetValueKey] ?? "" // Extract the correct target value
          override.push({
            sourcevalue: source,
            targetvalue: targetValue,
          })
        }
      }
    }

    return override
  }

  const updateQueryParams = (executor, source) => {
    if (!executor) return

    const updateOrAddParam = (key, value) => {
      const index = executor.queryParams.findIndex((param) => param.key === key)
      if (index > -1) {
        executor.queryParams[index].value = value
      } else {
        executor.queryParams.push({ key, value, description: "", req: false })
      }
    }

    if (source.username && source.password) {
      updateOrAddParam("username", source.username)
      updateOrAddParam("password", source.password)
    }
    addQueryParamsToSource(executor)
  }
  const addQueryParamsToSource = (data) => {
    replaceByKey(data.queryParams, "authorization", {
      key: "authorization",
      value: source.connDetails.username ? source.connDetails.username : source.connDetails.apikey,
      description: "",
      req: false,
    })
    replaceByKey(data.queryParams, "domainUrl", {
      key: "domainUrl",
      value: extractSourceDomain(source.connDetails.instance_url),
      description: "",
      req: false,
    })
    if (source.queryParam && Object.keys(source.queryParam).length > 0) {
      updateQueryFilterParams(data.queryParams, source.queryParam)
    }
  }

  const updateQueryFilterParams = (source, target) => {
    if (source && target) {
      source.forEach((item) => {
        if (target.hasOwnProperty(item.key)) {
          item.value = target[item.key]
        }
      })
    }
  }

  const extractSubdomain = (url) => {
    const subdomainMatch = url.match(/^(?:https?:\/\/)?([^./]+)\./)
    return subdomainMatch ? subdomainMatch[1] : null
  }
  const extractSourceDomain = (url) => {
    try {
      const trimmedUrl = url.trim()
      const parsedUrl = new URL(trimmedUrl)
      return parsedUrl.hostname
    } catch (error) {
      console.error("Invalid URL:", error)
      return null
    }
  }

  const replaceByKey = (data, key, newValue) => {
    if (data) {
      const index = data.findIndex((item) => item.key === key)
      if (index !== -1) {
        data[index] = newValue
      }
    }
  }

  const getSectionValueList = (id, sections, ticketFields) => {
    return sections.reduce((acc, section) => {
      if (section.choice_ids.includes(id) && section.ticket_field_ids.length > 0) {
        section.ticket_field_ids.forEach((ticket_field_id) => {
          const matchedField = ticketFields.find((field) => field.id === ticket_field_id)
          if (matchedField) {
            acc.push(matchedField.name)
          }
        })
      }
      return acc
    }, [])
  }

  const getConversationsFieldMappings = (item) => {
    const ignoreFields = item.fieldMappings.filter((obj) => !obj.skip)

    return ignoreFields
  }
  const updateOrAddParam = (executor, key, value, description, req) => {
    executor.queryParams = executor.queryParams || []

    const param = executor.queryParams.find((p) => p.key === key)
    param ? (param.value = value) : executor.queryParams.push({ key, value, description, req })
  }

  const updateTargetQueryParameter = (targetMappingExecutor, target) => {
    Object.entries(target.formData).forEach(([key, value]) => {
      if (key === "instance_url") {
        updateOrAddParam(
          targetMappingExecutor,
          "domainUrl",
          target.domainType === "sourceDomain" ? extractSourceDomain(value) : extractSubdomain(value),
        )
        updateOrAddParam(
          targetMappingExecutor,
          "domain",
          target.domainType === "sourceDomain" ? extractSourceDomain(value) : extractSubdomain(value),
        )
      } else {
        updateOrAddParam(targetMappingExecutor, key, value)
      }
    })
  }

  const saveMigration = async () => {
    try {
      const payload = {
        plan_name: templateName,
        // migration_source: migrationSource,
        // migration_target: migrationTarget,
        migration_objects: [],
        // createdAt: Date.now(),
        updatedAt: Date.now(),
        email_id: email,
        isCompleted: true,
        additional_details: {
          sourceObjData: migrationState.sourceObjData,
          targetData: migrationState.targetData,
          dataTypeData: migrationState.dataTypeData,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
        },
      }
      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }
      const res = await saveMigrationPlan(payload)

      if (res?.message === "plan created successfully" && res?.response?.id) {
        const newPlanId = res.response.id
        setPlanId(newPlanId)

        const url = new URL(window.location.href)
        url.searchParams.delete("planId")
        url.searchParams.set("plan_id", newPlanId)
        window.history.replaceState({}, "", url.toString())
      }
    } catch (error) {
      toast.error(error?.response.data.message || "Something went wrong!", {
        position: "top-right",
      })
    }
  }

  const handleSampleMigration = async () => {
    // Check if sample migration limit has been reached
    if (sampleMigrationLimitReached) {
      toast.error("Maximum sample migration limit (2) exceeded for this template", {
        position: "top-right",
      })
      return
    }

    if (!migrationState.templateName || migrationState.templateName.trim() === "Name of the Template") {
      toast.error("Please enter the Template name before starting the migration", {
        position: "top-right",
      })
      return
    }

    if (!sampleBatchSize || sampleBatchSize === "") {
      toast.error("Please enter the number of records for sample migration", {
        position: "top-right",
      })
      return
    }

    const totalRecords = Number(dataTypeData.numberOfRecords)
    if (Number(sampleBatchSize) > totalRecords) {
      toast.error("Sample migration count cannot exceed total records count", {
        position: "top-right",
      })
      return
    } try {
      console.log("Plan ID:.............", planId)
      const oldPlanData = await migrationTemplate.get({ id: planId })
      const inJson = JSON.parse(oldPlanData.metadata)

      const countUpdatedMetadata = {
        ...inJson,
        totalMigrationCount: (inJson?.totalMigrationCount || 0) + 1,
      }
      await migrationTemplate.update(planId,
        {
          metadata: JSON.stringify(countUpdatedMetadata)
        }
      ) // Update the metadata with the updated count
    } catch (error) {
      console.log("Error saving migration plan:", error)
    }

    saveMigration()
    const transformationPayload = {
      name: templateName,
      executedBy: email,
      partnerKey: "3fde0cf904f891223fd5131bb989b49a",
      partner: "dev-saasgenie",
      scheduledTime: Date.now(),
      transformationChunkMethod: selectedObjectData.additional_details
        ? selectedObjectData.additional_details.transformationChunkMethod
          ? selectedObjectData.additional_details.transformationChunkMethod
          : "default"
        : "default",
      transformationModels: [constructPayload()],
    }

    localStorage.setItem("migrationInProgress", "true")

    // Clear any previous migration state to ensure clean start
    localStorage.removeItem("migrationProgress")
    localStorage.removeItem("migrationId")

    console.log(transformationPayload)
    localStorage.setItem("sampleMigrationButtonClicked", "true")
    const activityPayload = {
      email: email,
      activity: "Sample Migration started",
      // timestamp: Date.now(),
    }
    postActivity(activityPayload)

    let searchParams = new URLSearchParams(location.search)
    // Clean up any existing planId parameter and ensure we only use plan_id
    searchParams.delete("planId")
    if (planId) {
      searchParams.set("plan_id", planId)
    }

    localStorage.setItem("sampleMigrationData", JSON.stringify({
      sampleBatchSize: sampleBatchSize,
      tag: tag,
    }))

    navigate(`/migration-results?${searchParams.toString()}`, {
      state: {
        sample: isSampleMigration,
        sampleBatchSize: sampleBatchSize,
        tag: tag,
        samplePayload: transformationPayload,
      },
    })
  }



  // Check if a sample migration has been completed for this plan and count the number of migrations
  useEffect(() => {
    // Use plan ID as the primary key for checking completed sample migrations
    if (planId) {
      const planKey = `completedSampleMigration_${planId}`
      const countKey = `sampleMigrationCount_${planId}`

      const hasSampleMigrationCompleted = localStorage.getItem(planKey) === "true"
      const count = parseInt(localStorage.getItem(countKey) || "0")

      setHasSampleMigration(hasSampleMigrationCompleted)
      setSampleMigrationCount(count)
      const limitReached = count >= 2 // Limit to 2 sample migrations
      setSampleMigrationLimitReached(limitReached)

      // Don't show toaster notification automatically when component loads
      // Only show it when user actually tries to start a migration
    }
  }, [planId, isSampleMigration])

  const constructPayload = () => {

    const updatedTargets = JSON.parse(JSON.stringify(dataMappingData?.targets ? dataMappingData?.targets : []));
    console.log(dataTypeData);


    if (source.type === 'api') {
      payload.source = payload.source || [{}];
      payload.source[0] = payload.source[0] || {};
      payload.source[0]['source_type'] = 'API';
      payload.source[0]['sourceExecutor'] = selectedObjectData.sourceExecutor;
      payload.source[0]['sourceProvider'] = selectedObjectData.sourceProvider;
      payload.source[0]['rootMapping'] = selectedObjectData.source_rootMapping ? selectedObjectData.source_rootMapping : '';
      payload.source[0]['source_response_type'] = selectedObjectData.source_response_type ? selectedObjectData.source_response_type : 'array<json>';

      if (dataTypeData.numberOfRecords) {
        payload.source[0]['chunkStrategy'] = payload.source[0]['chunkStrategy'] || {};
        payload.source[0]['chunkStrategy']['totalRecords'] = Number(dataTypeData.numberOfRecords);
        payload.source[0]['chunkStrategy']['limit'] = getchunkStrategyLimit();
        payload.source[0]['chunkStrategy']['paginationMethod'] = selectedObjectData.paginationMethod ? selectedObjectData.paginationMethod : 'pagelimit';
        payload.source[0]['chunkStrategy']['paginationFields'] = getPaginationFields();
        if (selectedObjectData.paginationMethod == 'daterange') {
          payload['source'][0]['chunkStrategy']['dateRangeFields'] = getDateRangeFields();

          if (payload['source'][0]['chunkStrategy']['dateRangeFields'] &&
            payload['source'][0]['chunkStrategy']['dateRangeFields']['incrementType']) {
            payload['source'][0]['chunkStrategy']['dateRangeFields']['incrementType'] = 'day';
          }
        }
      }
    } else if (source.type === 'csv') {
      payload.source = payload.source || [{}];
      payload.source[0] = payload.source[0] || {};
      payload.source[0]['source_type'] = 'CSV';
      payload.source[0]['sourceExecutor'] = {
        "id": 106161,
        "executor_type": "THIRDPARTY",
        "executor_name": "listTickets",
        "executor_version": "v2",
        "executor_display_name": "List Tickets",
        "executor_description": "To get a list of all tickets in your account, use the Incremental Ticket Export, Cursor Based or Incremental Ticket Export, Time Based endpoint.",
        "executor_category": "Ticketing",
        "executor_category_group": "Ticketing",
        "service_provider": "csv",
        "executor_scope": null,
        "partner": "b2b",
        "executor_method_type": "GET",
        "apiUrl": "https://s3.amazonaws.com/hari.apiplatform.io/tickets-sample-good.csv",
        "dataroot": null,
        "pathParams": [
          {
            "key": "subdomain",
            "value": "",
            "description": ""
          }
        ],
        "queryParams": [],
        "headers": [],
        "authModel": {
          "authType": "basic_authentication",
          "authAttributes": {
            "username": "",
            "password": ""
          }
        },
        "requestBody": {
          "value": "",
          "key": "payload",
          "description": "Document in JSON format",
          "annotation": "RequestBody"
        },
        "prerequisites": null,
        "executors": null,
        "functions": null,
        "responseAttributes": [],
        "response": null,
        "attributeMappings": null,
        "source": null,
        "block_attributes": null,
        "workflow_credentials": null,
        "disabled": false,
        "published": true,
        "scopeOfAccess": "public",
        "registeredAsIs": true,
        "creationTime": *************,
        "lastModifiedTime": *************,
        "executionLib": null,
        "isDocumentationPublished": true,
        "mockResponses": [],
        "isMockResponseEnabled": false,
        "block_id": null,
        "gatewaySettings": null,
        "apiQueryModel": null,
        "properties": null,
        "vid": null,
        "nid": null,
        "account": "b2b"
      };
      payload.source[0].sourceExecutor.apiUrl = dataTypeData.uploadedFiles[0].s3Url;
      payload.source[0]['rootMapping'] = selectedObjectData.source_rootMapping ? selectedObjectData.source_rootMapping : '';
      payload.source[0]['source_response_type'] = selectedObjectData.source_response_type ? selectedObjectData.source_response_type : 'array<json>';
      if (dataTypeData.numberOfRecords) {
        payload.source[0]['chunkStrategy'] = payload.source[0]['chunkStrategy'] || {};
        payload.source[0]['chunkStrategy']['totalRecords'] = Number(source.lengthOfCSV);
        payload.source[0]['chunkStrategy']['limit'] = Number(dataTypeData.numberOfRecords);

      }

    }
    const processTicketOverrides = (targets) => {
      return targets?.map(target => {
        if (target.name === selectedEntityData.mainTarget[0].name) {
          target.fieldMappings = target.fieldMappings.map(field => {
            for (const otherTarget of targets) {
              if (otherTarget.overrideFields && field.targetfield === otherTarget.overrideFields.targetField) {
                const overrideData = overrideTicketFields(otherTarget.fieldMappings[0], otherTarget.overrideFields);
                if (overrideData) {
                  field.override = field.override ? [...field.override, ...overrideData] : overrideData;
                }
              }
            }

            if (field.default === '' && field.override?.length > 0) {
              const nullSource = field.override.find(item => item.sourcevalue === null);
              if (nullSource) {
                field.default = nullSource.targetvalue;
              }
            }

            return field;
          });
        }
        return target;
      });
    };
    const processDefaultValues = (targets) => {
      return targets.map(target => {
        if (target.name === selectedEntityData.mainTarget[0].name) {
          target.fieldMappings = target.fieldMappings.map(field => {
            if (!field.skip && !field.mappingEnabled) {

              if (!field.default || field.default === null || field.default === '') {
                const defaultValue = getDefaultValueFromTicketFields(field.targetfield);
                if (defaultValue !== '') {
                  field.default = defaultValue;
                }
              }
            }

            if (field.mappingEnabled && field.mappings?.length > 0) {
              field.override = getTicketOverrideValues(field, target);
              if (field.default === '' && field.override?.length > 0) {
                const nullSource = field.override.find(item => item.sourcevalue === null);
                if (nullSource) {
                  field.default = nullSource.targetvalue;
                }
              }
            }



            return field;
          });
        }
        return target;
      });
    };

    let processedTargets = processTicketOverrides(updatedTargets);
    processedTargets = processDefaultValues(processedTargets);

    let finalTargets = [];
    let isDependencyAdded = false;

    processedTargets.forEach(item => {
      item.fieldMappings = item.fieldMappings.map(field => {
        if (!field.isLookupEnabled) {
          if (field.type === "number") {
            if (field.override?.length) {
              field.override = field.override.map(i => {
                if (i.targetvalue && i.targetvalue !== "") {
                  return { ...i, targetvalue: Number(i.targetvalue) };
                }
                return i;
              });
            }
            field.value = Number(field.default);
          } else {
            field.value = field.default;
          }

          field.mappingType = getMappingType(
            field.sourcefield,
            field.isSourceFieldAvailable,
            field.override,
            field.default
          );
          field.attributeType = getFieldValue(field.type);

          if (field.combinedFields?.length > 0) {
            field.value = getCombinedFieldsJs(
              field.combinedFields,
              selectedEntityData?.isDelimiterEnabled,
              selectedEntityData.delimiter
            );
            field.mappingType = "js";
          }

          if (field.is_custom_field &&
            selectedObjectData.additional_details?.custom_field_key &&
            selectedObjectData.additional_details.custom_field_key !== '') {
            field.custom_field_key = selectedObjectData.additional_details.custom_field_key;
          }

          if (item.name === selectedEntityData?.mainTarget[0]?.name && field.targetfield === "tags") {
            const override = getOverrideValuesforTags(field);
            if (override?.length > 0) {
              field.mappingType = "";
              field.override = override;
            } else {
              field.value = [tag];
              field.mappingType = "static";
            }
          }
        } else {
          const replacements = { domain: '', apikey: '' };
          field.value = replaceCommonValues(field.value, replacements);
        }
        return field;
      });

      if (item.name === selectedEntityData?.mainTarget[0]?.name) {
        item.fieldMappings = item.fieldMappings.filter(obj => !obj.skip);
        finalTargets.push(item);
      }

      if (selectedEntityData?.dependentTargets?.length > 0) {
        isDependencyAdded = true;
        payload.sourceDataExtender = payload.sourceDataExtender || [];

        selectedEntityData.sourceExtender.forEach((extender, i) => {
          if (!payload.sourceDataExtender[i]) {
            payload.sourceDataExtender[i] = {};
          }

          payload.sourceDataExtender[i] = {
            source_type: source.type.toUpperCase(),
            sourceExecutor: extender.sourceExtenderExecutor,
            sourceProvider: extender.sourceProvider,
            sourceMergerJS: extender.sourceMergerJS,
            rootMapping: source.type === "csv" ? "tickets" : "",
            sourceConnector: {},
            source_response_type: "array<json>",
            sourceDatabase: "",
            sourceDatabaseType: "",
            sourceMethod: "",
            sourceDatabaseObject: "",
            tables: [],
            code: "",
            query_builder: { fields: [] },
            type: "Database",
            method_type: "",
            dbDetails: []
          };

          if (source.type === 'csv') {
            payload.sourceDataExtender[i].sourceExecutor.apiUrl = dataTypeData.uploadedFiles[i + 1].s3Url;
          }

          updateQueryParams(payload.sourceDataExtender[i].sourceExecutor, source?.connDetails);
        });
      }
    });

    finalTargets = finalTargets.map(target => {
      return {
        ...target,
        targetExecutor: {
          ...target.targetExecutor,
          queryParams: selectedObjectData.targetMappingExecutor.queryParams
        }
      };
    });



    payload.target = finalTargets;

    setTargetsCopy(processedTargets);

    for (const target of payload.target) {
      if (selectedObjectData.sourceUniqueIdentifier) {
        target.sourceUniqueIdentifier = (source.type === 'csv' && selectedEntityData.mainTarget[0].name === 'Purchase Order')
          ? 'Order number'
          : selectedObjectData.sourceUniqueIdentifier;
      }

      if (selectedObjectData.payloadVerifier) {
        target.payloadVerifier = selectedObjectData.payloadVerifier;

        if (sourceExeRes?.ticketFields) {
          const ticketFields = sourceExeRes.ticketFields;
          const validationData = [];

          for (const field of ticketFields) {
            if (field.sections.length > 0 && field.field_type.includes('custom_')) {
              const validationField = field.name;
              const sections = {};

              field.choices.forEach(choice => {
                sections[choice.value] = getSectionValueList(choice.id, field.sections, ticketFields);
              });

              let section_fields = [];
              field.sections.forEach(section => {
                section_fields = [...section_fields, ...section.ticket_field_ids];
              });

              const sectionFieldsSet = new Set(section_fields);
              const fieldsList = ticketFields
                .filter(field => sectionFieldsSet.has(field.id))
                .map(field => field.name);

              if (validationField && section_fields.length > 0) {
                validationData.push({
                  validationField,
                  fieldsList,
                  sections
                });
              }
            }
          }

          if (validationData.length > 0) {
            target.payloadVerifierJs = `function filterCustomFields(ticket, validationData) {
                            if (ticket.custom_fields) {
                                validationData.forEach(function(data) {
                                    var validationField = data.validationField;
                                    var fieldsList = data.fieldsList;
                                    var sections = data.sections;

                                    var choice = ticket.custom_fields[validationField];
                                    if (sections[choice]) {
                                        var sectionFields = sections[choice];
                                        Object.keys(ticket.custom_fields).forEach(function(field) {
                                            if (fieldsList.indexOf(field) !== -1 && sectionFields.indexOf(field) === -1) {
                                                delete ticket.custom_fields[field];
                                            }
                                        });
                                    }
                                });
                            }
                            return ticket;
                        }

                        var validationData = ${JSON.stringify(validationData)};
                        target = filterCustomFields(source, validationData);`;
          } else {
            target.payloadVerifierJs = '';
            target.payloadVerifier = false;
          }
        }
      }

      if (target.sourceValidateExecutor.pathParams && target.sourceValidateExecutor.pathParams.length > 0) {
        target.sourceValidateExecutor.pathParams[0].value = extractSubdomain(targetData.formData.instance_url);
      }
      if (target.sourceValidateExecutor && target.sourceValidateExecutor.authModel && target.sourceValidateExecutor.authModel.authAttributes && target.sourceValidateExecutor.authModel.authAttributes.username) {
        target.sourceValidateExecutor.authModel.authAttributes.username = targetData.formData.apikey;
      }

      target.batchStrategy = {
        limit: source.apiRateLimit ? Number(source.apiRateLimit) : 160,
        limitBy: 'minute',
        interval: 30,
        migrateSample: true,
        sampleSize: sampleBatchSize,
        immediateFirstBatch: true,
        retry: false
      };

      if (isDependencyAdded) {
        if (!Array.isArray(target.dependentTargets)) {
          target.dependentTargets = [];
        }

        target.dependentTargets = JSON.parse(JSON.stringify(selectedEntityData.dependentTargets));

        console.log(selectedEntityData, updatedTargets, 'up');


        selectedEntityData.dependentTargets.forEach((dependentTarget, index) => {
          const matchingTarget = updatedTargets.find(t => t.name === dependentTarget.name);
          if (matchingTarget) {
            dependentTarget.fieldMappings = [...matchingTarget.fieldMappings];
          }
        });
        console.log(selectedEntityData);

        if (getConversationsFieldMappings(selectedEntityData.dependentTargets[0])?.length > 0) {
          target.dependentTargets = selectedEntityData.dependentTargets.map(depTarget => {
            console.log(depTarget);
            depTarget.fieldMappings = getConversationsFieldMappings(depTarget);
            console.log(depTarget);

            delete depTarget.sourceValidateExecutor;
            delete depTarget.sourceValidationKeys;

            depTarget.checked = true;
            depTarget.mappings = [];
            depTarget.automap = true;

            depTarget.batchStrategy = {
              limit: source.apiRateLimit ? Number(source.apiRateLimit) : 160,
              limitBy: 'minute',
              interval: 30,
              migrateSample: true,
              sampleSize: sampleBatchSize,
              immediateFirstBatch: true,
              retry: false
            };

            return depTarget;
          });
        }

        target.dependentTargets.forEach((depTarget, i) => {
          depTarget.targetExecutor = JSON.parse(
            JSON.stringify(selectedEntityData.dependentTargets[i].targetExecutor)
          );
        });

        if (dataMappingData.isNotes) {
          target.dependentTargets.forEach((depTarget, i) => {
            depTarget.targetExecutor.apiUrl = selectedEntityData.dependentTargets[i].additionalDetails.notesURL;
          });
        }



        target.dependentTargets.forEach(depTarget => {
          const targetExecutor = depTarget.targetExecutor;

          updateTargetQueryParameter(targetExecutor, targetData);
        });




        if (selectedObjectData.sourceUniqueIdentifier) {
          target.dependentTargets.forEach(depTarget => {
            depTarget.sourceUniqueIdentifier = (source.type === 'csv' && selectedEntityData.mainTarget[0].name === 'Purchase Order')
              ? 'Order number'
              : selectedObjectData.sourceUniqueIdentifier;

            depTarget.payloadVerifier = false;
            depTarget.payloadVerifierJs = '';
          });
        }


        // if (target.dependentTargets.length > 0) {
        //     target.dependentTarget = target.dependentTargets[0];
        //     delete target.dependentTargets;
        // }
      }
    }

    console.log(payload);
    payload['dependency'] = null;
    payload['executedBy'] = email;
    payload['name'] = templateName;
    return payload;
  };

  const handleContinueMigration = async () => {
    // Validation checks similar to handleSampleMigration
    if (!migrationState.templateName || migrationState.templateName.trim() === "Name of the Template") {
      toast.error("Please enter the Template name before starting the migration", {
        position: "top-right",
      })
      return
    }

    if (!sampleBatchSize || sampleBatchSize === "") {
      toast.error("Please enter the number of records for sample migration", {
        position: "top-right",
      })
      return
    }

    // Set migration in progress flag
    localStorage.setItem("migrationInProgress", "true")

    // Clear any previous migration state to ensure clean start
    localStorage.removeItem("migrationProgress")
    localStorage.removeItem("migrationId")

    // Set the flag that triggers the API call in Migration.js
    localStorage.setItem("sampleMigrationButtonClicked", "true")

    // Log activity
    const activityPayload = {
      email: email,
      activity: "Sample Migration started",
      // timestamp: Date.now(),
    }
    postActivity(activityPayload)

    // Create transformation payload for the new migration
    const transformationPayload = {
      name: migrationState.templateName,
      executedBy: email,
      partnerKey: "3fde0cf904f891223fd5131bb989b49a",
      partner: "dev-saasgenie",
      scheduledTime: Date.now(),
      transformationModels: [constructPayload()],
      tag: tag || "default",
    }

    // Store sample migration data
    localStorage.setItem("sampleMigrationData", JSON.stringify({
      sampleBatchSize: sampleBatchSize,
      tag: tag,
    }))

    let searchParams = new URLSearchParams(location.search)
    // Clean up any existing planId parameter and ensure we only use plan_id
    searchParams.delete("planId")
    if (planId) {
      searchParams.set("plan_id", planId)
    }

    navigate(`/migration-results?${searchParams.toString()}`, {
      state: {
        sample: isSampleMigration,
        sampleBatchSize: sampleBatchSize,
        tag: tag,
        samplePayload: transformationPayload, // Include the transformation payload
        // Don't pass continueMigration flag for new migrations
      },
    })
  }

  return (
    <div>
      {!showPricing ? (
        <div>
          <div className={styles.dFlex}>
            <div className={styles.section}>
              <div className={styles["migration-container"]}>
                <div className={styles["db-icon-left"]}>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                </div>
                <div className={styles["db-icon-right"]}>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                  <div className={styles["db-lines"]}></div>
                </div>
                <div className={styles["packets-area"]}>
                  <div className={styles["data-packet"]}></div>
                </div>
                {/* <div className={styles['migration-text']}>SAMPLE MIGRATION</div> */}
              </div>
            </div>
            <div className={styles.section}>
              {isSampleMigration ? (
                <div style={{ width: "90%" }}>
                  <div className={styles.dFlex}>
                    <span className={globalStyles.poppinsHeaderStyle}>Sample Migration Settings</span>
                  </div>
                  <input
                    style={{ width: "95%", marginTop: "10px" }}
                    className="form-control"
                    onChange={(event) => {
                      const value = event.target.value
                      const totalRecords = Number(dataTypeData.numberOfRecords)
                      if (value === "") {
                        setSampleBatchSize(value)
                      } else if (!isNaN(Number(value)) && Number(value) > 0 && Number(value) <= totalRecords) {
                        setSampleBatchSize(value)
                      } else if (Number(value) > totalRecords) {
                        toast.error("Sample migration count cannot exceed total records count", {
                          position: "top-right",
                        })
                      }
                    }}
                    value={sampleBatchSize}
                    placeholder="Enter the number of records you want to migrate*"
                  />
                  <div className={`${styles.dFlex} ${styles.dotName}`}>
                    <div>Maximum of 50 records for sample migration</div>
                  </div>
                  <div className={styles.dFlex} style={{ marginTop: "20px" }}>
                    <span className={globalStyles.poppinsHeaderStyle}>Tag your target records</span>
                    <div className={styles.dFlex} style={{ marginLeft: "auto" }}>
                      <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                      <span className={globalStyles.guideName}>What are the benefits of tags?</span>
                    </div>
                  </div>
                  <input
                    className="form-control"
                    style={{ width: "95%", marginTop: "10px" }}
                    placeholder="Tag name"
                    value={tag}
                    onChange={(event) => setTag(event.target.value)}
                  />                  <div style={{
                    display: "flex",
                    gap: "10px",
                    margin: "20px 0",
                    flexDirection: hasSampleMigration ? "row" : "column"
                  }}>
                    <button
                      className={globalStyles.mainButton}
                      style={{
                        width: "100%",
                        opacity: sampleMigrationLimitReached ? 0.5 : 1,
                        cursor: sampleMigrationLimitReached ? "not-allowed" : "pointer",
                        backgroundColor: sampleMigrationLimitReached ? "#ccc" : ""
                      }}
                      onClick={isFromMigration ? handleContinueMigration : handleSampleMigration}
                      disabled={sampleMigrationLimitReached}
                    >
                      Start Sample Migration
                    </button>

                    {hasSampleMigration && (
                      <button
                        className={globalStyles.mainButton}
                        style={{ width: "100%" }}
                        onClick={() => setIsSampleMigration(false)}
                      >
                        Skip to Live Migration
                      </button>
                    )}
                  </div>

                  {sampleMigrationLimitReached && (
                    <div className={styles.dotName} style={{ color: "#E37B52", marginBottom: "15px", textAlign: "center" }}>
                      Maximum sample migration limit exceeded for the template
                    </div>
                  )}
                </div>
              ) : (
                <div style={{ width: "90%" }}>
                  <div className={styles.dFlex}>
                    <span className={globalStyles.poppinsHeaderStyle}>Live Migration Settings</span>
                    <div className={styles.dFlex} style={{ marginLeft: "auto" }}>
                      <img
                        src="/assets/help.png"
                        alt="Help"
                        className={globalStyles.helpIcon}
                        onClick={() => {
                          displayArticle("Pricing and plan options")
                        }}
                      />
                      <span className={globalStyles.guideName}>Pricing and plan options</span>
                    </div>
                  </div>

                  {/* <div className={`${styles.dFlex} ${styles.dotName}`} style={{ marginTop: "5px" }}>
                    <div>Estimated time for the live migration</div>
                    <div style={{ marginLeft: "auto" }}>{fields.estimatedTime}</div>
                  </div> */}

                  {/* <div className={styles.dFlex} style={{ marginTop: "20px" }}>
                    <span className={globalStyles.poppinsHeaderStyle}>Tag your target records</span>
                    <div className={styles.dFlex} style={{ marginLeft: "auto" }}>
                      <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} onClick={() => { displayArticle("Pricing and plan options") }} />
                      <span className={globalStyles.guideName}>Pricing and plan options</span>
                    </div>
                  </div> */}

                  <input className="form-control" style={{ width: "95%", marginTop: "10px", fontFamily: "Inter" }} placeholder="Tag name" />

                  <div style={{ marginTop: "20px", marginBottom: "30px" }}>
                    <span className={globalStyles.poppinsHeaderStyle}>When do you want to start the migration?</span>
                    <FormControl fullWidth className={globalStyles.customDropdownContainer}>
                      <Select
                        value={liveMigrationOption || ""}
                        onChange={(event) => handleSourceValueChange(event.target.value)}
                        className={globalStyles.customDropdownSelect}
                        displayEmpty
                        renderValue={(selected) =>
                          selected ? (
                            <span className={globalStyles.guideName} style={{ fontSize: "14px" }}>
                              {selected === "now" ? "Now" : "Schedule for later"}
                            </span>
                          ) : (
                            <span className={globalStyles.guideName} style={{ fontSize: "14px" }}>
                              Choose below*
                            </span>
                          )
                        }
                      >
                        <MenuItem value="now" style={{ fontFamily: 'Inter' }}>Now</MenuItem>
                        <MenuItem value="later" style={{ fontFamily: 'Inter' }}>Schedule for later</MenuItem>
                      </Select>
                    </FormControl>
                    {liveMigrationOption === "later" && (
                      <div>
                        <div className={globalStyles.passwordContainer}>
                          <input
                            className={globalStyles.passwordInput}
                            placeholder="Set Date and time"
                            style={{ width: "95%" }}
                            value={migrationDate}
                            readOnly={true}
                          />
                          <button type="button" className={globalStyles.passwordToggle} onClick={() => setOpen(true)}>
                            <HiCalendarDays className={globalStyles.closeIcon} />
                          </button>
                          <Dialog open={open} onClose={() => setOpen(false)}>
                            <DialogContent sx={{ backgroundColor: "#170903", padding: "0" }}>
                              <MigrationScheduler
                                open={open}
                                onClose={() => setOpen(false)}
                                fullWidth
                                maxWidth="md"
                                setMigrationDate={setMigrationDate}
                              />
                            </DialogContent>
                          </Dialog>
                        </div>
                      </div>
                    )}
                    <div
                      className={styles.dFlex}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        marginTop: "20px",
                        width: "100%",
                        maxWidth: "300px",
                      }}
                    >
                      <div className={styles.dotName} style={{ fontSize: "16px" }}>
                        Send notifications and updates
                      </div>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={sentNotification}
                            onChange={(event) => setSentNotification(!sentNotification)}
                            sx={{
                              width: 40,
                              height: 20,
                              padding: 0,
                              "& .MuiSwitch-switchBase": {
                                padding: 0,
                                margin: "2px 3px",
                                transitionDuration: "300ms",
                                "&.Mui-checked": {
                                  transform: "translateX(20px)",
                                  color: "#fff",
                                  "& + .MuiSwitch-track": {
                                    backgroundColor: "#E97451",
                                    opacity: 1,
                                    border: 0,
                                  },
                                  "& .MuiSwitch-thumb": {
                                    backgroundColor: "#fff",
                                    width: 14,
                                    height: 14,
                                  },
                                  "&.Mui-disabled + .MuiSwitch-track": {
                                    opacity: 0.5,
                                  },
                                },
                              },
                              "& .MuiSwitch-thumb": {
                                backgroundColor: "#fff",
                                boxSizing: "border-box",
                                width: 14,
                                height: 14,
                                borderRadius: "50%",
                                transition: "width 0.2s, height 0.2s",
                              },
                              "& .MuiSwitch-track": {
                                borderRadius: 10,
                                backgroundColor: "#B9B5B3",
                                opacity: 1,
                                transition: "background-color 0.5s",
                              },
                            }}
                          />
                        }
                      />
                    </div>
                    {sentNotification && (
                      <div>
                        <div className={styles.dFlex} style={{ gap: "15px" }}>
                          <input
                            className={globalStyles.formCheckInput}
                            type="checkbox"
                            id="checkbox"
                            style={{ marginTop: "8px" }}
                            value={sentDetailsToMyself}
                            aria-label="..."
                            onChange={(event) => setSentDetailsToMyself(!sentDetailsToMyself)}
                          />
                          <span className={styles.dotName} style={{ fontSize: "16px" }}>
                            Send details to myself
                          </span>
                        </div>
                        {contacts.map((contact, index) => (
                          <div key={index} style={{ marginBottom: "15px" }}>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                marginTop: "15px",
                              }}
                            >
                              {/*<div style={{ fontWeight: "bold" }}>Enter the contact details</div>*/}
                              {contacts.length === 1 ? (
                                <div
                                  className={styles.dFlex}
                                  style={{
                                    marginTop: "15px",
                                    width: "100%",
                                  }}
                                >
                                  <div className={globalStyles.poppinsHeaderStyle}>Or enter the contact details</div>
                                  <button
                                    className={globalStyles.pageButton}
                                    style={{ marginLeft: "auto" }}
                                    onClick={() => setSentNotification(!sentNotification)}
                                  >
                                    <HiXMark className={globalStyles.closeIcon} />
                                  </button>
                                </div>
                              ) : (
                                <div className={styles.dFlex} style={{ marginTop: "15px", width: "100%" }}>
                                  <div className={globalStyles.poppinsHeaderStyle}>Enter the contact details</div>
                                  <button
                                    className={globalStyles.pageButton}
                                    style={{
                                      marginLeft: "auto",
                                      justifyContent: "flex-end",
                                    }}
                                    onClick={removeContact}
                                  >
                                    <HiXMark className={globalStyles.closeIcon} />
                                  </button>
                                </div>
                              )}
                            </div>
                            <input
                              className="form-control"
                              style={{ width: "95%", marginTop: "10px" }}
                              placeholder="Enter the Email ID*"
                            />
                            <div style={{ display: "flex", gap: "10px" }}>
                              <FormControl className={globalStyles.customDropdownContainer} style={{ width: "15%" }}>
                                <Select
                                  value={contact.code}
                                  displayEmpty
                                  className={globalStyles.customDropdownSelect}
                                  renderValue={(selected) => (
                                    <span className={globalStyles.guideName} style={{ fontSize: "14px" }}>
                                      {selected}
                                    </span>
                                  )}
                                >
                                  {countryCodes.map((country) => (
                                    <MenuItem key={country.code} value={country.code}>
                                      {country.code} ({country.name})
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                              <input
                                className="form-control"
                                style={{ width: "80%", marginTop: "12px" }}
                                placeholder="Enter the phone number*"
                              />
                            </div>
                          </div>
                        ))}
                        {contacts.length === 1 && (
                          <button
                            className={globalStyles.pageButton}
                            style={{
                              width: "100%",
                              height: "40px",
                              marginTop: "20px",
                            }}
                            onClick={addContact}
                          >
                            <HiPlus />
                            Add another contact
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                  <button
                    className={globalStyles.mainButton}
                    style={{ width: "100%" }}
                    onClick={() => {
                      if (!liveMigrationOption) {
                        toast.error("Please select when you want to start the migration", {
                          position: "top-right",
                        });
                        return;
                      }

                      if (liveMigrationOption === "later" && !migrationDate) {
                        toast.error("Please select a date and time for the scheduled migration", {
                          position: "top-right",
                        });
                        return;
                      }

                      setShowPricing(!showPricing);
                    }}
                  >
                    Select your pricing package
                  </button>
                </div>
              )}
            </div>
          </div>
          <ToastContainer />
        </div>      ) : (
        <div>
          {!isPaymentSucces ? (
            <Pay />
          ) : (
            <div>
              <PaymentDetails liveMigrationPayload={liveMigrationPayload} planId={planId} />
            </div>
          )}
        </div>
      )}
    </div>
  )
}