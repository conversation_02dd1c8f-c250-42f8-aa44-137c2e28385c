import React, {useState} from "react";
import globalStyles from "../../globalStyles.module.css";
import styles from "./ChangePassword.module.css";
import {CheckIcon, XIcon, EyeIcon, EyeOffIcon} from "@heroicons/react/solid";
import {changePassword} from "../../apiService";
import CryptoJS from "crypto-js";

export default function ChangePassword({close}) {
    const [isChanged, setIsChanged] = useState(false);
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [strongPassword, setStrongPassword] = useState(false);
    const [passwordsMatch, setPasswordsMatch] = useState(true);
    const [email, setEmail] = useState(localStorage.getItem('email'));
    const [formError, setFormError] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const checkStrongPassword = (passwordValue) => {
        const strongPasswordPattern = /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[A-Za-z\d!@#$%^&*]{8,}$/;
        return strongPasswordPattern.test(passwordValue);
    };

    const handlePasswordChange = (e) => {
        const passwordValue = e.target.value;
        setPassword(passwordValue);
        setStrongPassword(checkStrongPassword(passwordValue));

        // Check if passwords match whenever either field changes
        if (confirmPassword) {
            setPasswordsMatch(passwordValue === confirmPassword);
        }
    };

    const handleConfirmPasswordChange = (e) => {
        const confirmValue = e.target.value;
        setConfirmPassword(confirmValue);
        setPasswordsMatch(password === confirmValue);
    };

    const encrypt = (text) => {
        console.log(text);
        const secretKey = CryptoJS.enc.Utf8.parse("sgmg22025sgmg220"); // 16-byte key
        const iv = CryptoJS.enc.Utf8.parse("1234567890123456"); // 16-byte IV

        return CryptoJS.AES.encrypt(text, secretKey, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }).toString();
    };
    const confirm = async () => {
        // Reset any previous errors
        setFormError("");

        // Validate form before submission
        if (!password) {
            setFormError("Password cannot be empty");
            return;
        }

        if (!strongPassword) {
            setFormError("Password must meet the strength requirements");
            return;
        }

        if (!confirmPassword) {
            setFormError("Please confirm your password");
            return;
        }

        if (password !== confirmPassword) {
            setFormError("Passwords do not match");
            return;
        }

        try {
            const res = await changePassword(email, 'update', encrypt(password));

            if (res) {
                setIsChanged(true);
                setTimeout(() => {
                    close();
                }, 3000);
            } else {
                setFormError(res?.message || "Failed to change password. Please try again.");
            }
        } catch (error) {
            setFormError("An error occurred. Please try again later.");
            console.error("Password change error:", error);
        }
    }


    const togglePasswordVisibility = () => {
        setShowPassword(!showPassword);
    };

    const toggleConfirmPasswordVisibility = () => {
        setShowConfirmPassword(!showConfirmPassword);
    };

    return (
        <div className={styles.container}>
            {!isChanged ? (
                <div style={{width: "100%"}}>
                    <div className={styles.dFlex} style={{justifyContent: "space-between"}}>
                        <h2 className={globalStyles.selectionName}>CHANGE YOUR PASSWORD</h2>
                        <button className={styles.closeButton} onClick={close}>×</button>
                    </div>

                    {formError && (
                        <div className={globalStyles.interSummaryStyle} style={{color: "#F8F8F7"}}>
                            {formError}
                        </div>
                    )}

                    <div className={styles.dFlex} style={{alignItems: "center", width: "100%", marginTop: "20px"}}>
                        <label className={globalStyles.poppinsHeaderStyle} style={{width: "240px", textAlign: "left"}}>
                            Set new password
                        </label>
                        <div className={styles.passwordWrapper}>
                            <input
                                type={showPassword ? "text" : "password"}
                                className={styles.passwordInput}
                                placeholder="Your password"
                                value={password}
                                onChange={handlePasswordChange}
                            />
                            <div className={styles.passwordIconsContainer}>
                                <button type="button" className={styles.eyeButton} onClick={togglePasswordVisibility}>
                                    {showPassword ? (
                                        <EyeOffIcon className={styles.eyeIcon} />
                                    ) : (
                                        <EyeIcon className={styles.eyeIcon} />
                                    )}
                                </button>
                                {password.length > 0 && (
                                    <span className={styles.validationIcon}>
                                        {!strongPassword ? (
                                            <XIcon className={styles.eyeIcon} style={{color: "red"}}/>
                                        ) : (
                                            <CheckIcon className={styles.eyeIcon} style={{color: "green"}}/>
                                        )}
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className={styles.dFlex} style={{alignItems: "center", width: "100%", marginTop: "20px"}}>
                        <label className={globalStyles.poppinsHeaderStyle} style={{width: "240px", textAlign: "left"}}>
                            Confirm password
                        </label>
                        <div className={styles.passwordWrapper}>
                            <input
                                type={showConfirmPassword ? "text" : "password"}
                                className={styles.passwordInput}
                                placeholder="Confirm password"
                                value={confirmPassword}
                                onChange={handleConfirmPasswordChange}
                            />
                            <div className={styles.passwordIconsContainer}>
                                <button type="button" className={styles.eyeButton} onClick={toggleConfirmPasswordVisibility}>
                                    {showConfirmPassword ? (
                                        <EyeOffIcon className={styles.eyeIcon} />
                                    ) : (
                                        <EyeIcon className={styles.eyeIcon} />
                                    )}
                                </button>
                                {confirmPassword && (
                                    <span className={styles.validationIcon}>
                                        {!passwordsMatch ? (
                                            <XIcon className={styles.eyeIcon} style={{color: "red"}}/>
                                        ) : (
                                            <CheckIcon className={styles.eyeIcon} style={{color: "green"}}/>
                                        )}
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>                    <div className={globalStyles.guideName} style={{fontSize: "16px", marginTop: "20px", textAlign: "right"}}>
                        Password must be at least 8 characters long, contain 1 capital letter, 1 number and 1 special character.
                    </div>
                    <button
                        className={globalStyles.mainButton}
                        style={{width: "100%", marginTop: "20px"}}
                        onClick={confirm}
                        disabled={!strongPassword || !passwordsMatch || !password || !confirmPassword}
                    >
                        Confirm new password
                    </button>
                </div>
            ) : (
                <div className={`${globalStyles.selectionName} ${styles.dFlex}`} style={{justifyContent: "center"}}>
                    YOUR PASSWORD HAS BEEN CHANGED
                </div>
            )}
        </div>
    )
}
