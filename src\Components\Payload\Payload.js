import { useState } from 'react';
import { HiXMark } from 'react-icons/hi2';
import styles from './Payload.module.css';
import globalStyles from '../globalStyles.module.css';

const PayloadDialog = ({
                           isOpen,
                           selectedPayload,
                           errorLogData,
                           onClose,
                           show = "false"
                       }) => {
    const [activeTab, setActiveTab] = useState("response");

    if (isOpen && (!selectedPayload || Object.keys(selectedPayload).length === 0)) {
        return (
            <div className={styles.dialogOverlay}>
                <div className={styles.dialogContent}>
                    <div className={styles.dialogHeader}>
                        <span className={globalStyles.interSummaryStyle}>Payload Details</span>
                        <HiXMark className={styles.closeDialogIcon} onClick={onClose} />
                    </div>
                    <div className={styles.dialogBody}>
                        <div className={styles.noRecordsMessage}>No records fetched</div>
                    </div>
                    <div className={styles.dialogFooter}>
                        <button
                            className={styles.closeButton}
                            onClick={onClose}
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!isOpen) return null;

    const getDialogTitle = () => {
        return selectedPayload.type || "Payload Details";
    };

    const formatJsonForDisplay = (jsonData) => {
        if (!jsonData) return "No data available";
        try {
            if (typeof jsonData === 'string') {
                try {
                    const parsedJson = JSON.parse(jsonData);
                    return JSON.stringify(parsedJson, null, 2);
                } catch {
                    return jsonData;
                }
            } else {
                return JSON.stringify(jsonData, null, 2);
            }
        } catch (error) {
            console.error("Error formatting JSON:", error);
            return "Error formatting JSON data";
        }
    };

    const getResponseBody = () => {
        if (!selectedPayload.additionalDetails || !selectedPayload.additionalDetails.response) {
            return "No response data available";
        }

        const responseBody = selectedPayload.additionalDetails.response.body;
        return formatJsonForDisplay(responseBody);
    };

    const hasErrorLogData = errorLogData && (
        (typeof errorLogData === 'object' && Object.keys(errorLogData).length > 0) ||
        (typeof errorLogData === 'string' && errorLogData.trim() !== '')
    );

    if (activeTab === "payload" && !hasErrorLogData) {
        setActiveTab("response");
    }

    return (
        <div className={styles.dialogOverlay}>
            <div className={styles.dialogContent}>
                <div className={styles.dialogHeader}>
                    <span className={globalStyles.interSummaryStyle}>{getDialogTitle()}</span>
                    <HiXMark className={styles.closeDialogIcon} onClick={onClose} />
                </div>

                <div className={styles.dialogTabs}>
                    <button
                        className={`${styles.tabButton} ${activeTab === "response" ? styles.activeTab : ""}`}
                        onClick={() => setActiveTab("response")}
                    >
                        RESPONSE
                    </button>
                    {hasErrorLogData && (
                        <button
                            className={`${styles.tabButton} ${activeTab === "payload" ? styles.activeTab : ""}`}
                            onClick={() => setActiveTab("payload")}
                        >
                            PAYLOAD
                        </button>
                    )}
                </div>

                <div className={styles.dialogBody}>
                    {activeTab === "response" && (
                        <>
                            <div className={styles.statusLine}>
                                <span className={globalStyles.interSummaryStyle}>Status:</span>{" "}
                                <span
                                    className={
                                        selectedPayload.additionalDetails?.response?.statusCode < 400
                                            ? styles.successCode
                                            : styles.errorCode
                                    }
                                >
                                    {selectedPayload.additionalDetails?.response?.statusCode}
                                </span>
                            </div>
                            <pre className={styles.codeDisplay}>
                                {getResponseBody()}
                            </pre>
                        </>
                    )}

                    {activeTab === "payload" && hasErrorLogData && (
                        <pre className={styles.codeDisplay}>
                            {formatJsonForDisplay(errorLogData)}
                        </pre>
                    )}
                </div>

                <div className={styles.dialogFooter}>
                    <button
                        className={styles.closeButton}
                        onClick={onClose}
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

export default PayloadDialog;
