import React, {useState} from "react";
import globalStyles from "../../globalStyles.module.css";
import styles from "./PhoneNumber.module.css";
import {FormControl, MenuItem, Select} from "@mui/material";

export default function Phonenumber({close}) {
    const [isChanged, setIsChanged] = useState(false);
    const countryCodes = [
        { code: "+91", name: "India" },
        { code: "+1", name: "United States" },
        { code: "+44", name: "United Kingdom" },
        { code: "+61", name: "Australia" },
        { code: "+1", name: "Canada" },
    ];
    const handlePhoneNumber = () => {
        setIsChanged(true);
        setTimeout(() => {
            close();
        }, 3000);
    }
    return (
        <div className={styles.container}>
            {!isChanged ? (
                <div>
                    <div className={styles.dFlex} style={{justifyContent: "space-between"}}>
                        <h2 className={globalStyles.selectionName}><PERSON>AN<PERSON> PHONE NUMBER</h2>
                        <button className={styles.closeButton} onClick={close}>×</button>
                    </div>
                    {/*<div className={globalStyles.guideName} style={{fontSize: "16px"}}>Enter a 10-digit mobile number</div>*/}
                    <div>
                        <div className={styles.dFlex}
                             style={{
                                 display: "flex",
                                 alignItems: "center",
                                 width: "100%",
                                 marginBottom: "20px",
                                 marginTop: "15px"
                             }}>
                            <label className={globalStyles.poppinsHeaderStyle}
                                   style={{width: "250px", textAlign: "left"}}>
                                Updates sent to
                            </label>
                            <div style={{display: "flex", gap: "10px", width: "100%"}}>
                                <FormControl
                                    className={globalStyles.customDropdownContainer}
                                    style={{width: "25%"}}>
                                    <Select displayEmpty
                                            className={globalStyles.customDropdownSelect}

                                            renderValue={(selected) => (
                                                <span className={globalStyles.guideName}
                                                      style={{fontSize: "14px"}}>
                                                            {selected || "Select Code"}
                                                          </span>
                                            )}>
                                        {countryCodes.map((country) => (
                                            <MenuItem key={country.code}
                                                      value={country.code}>
                                                {country.code} ({country.name})
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                                <input
                                    className="form-control"
                                    style={{width: "100%", marginTop: "12px"}}
                                    placeholder="Enter the phone number"

                                />
                            </div>
                        </div>
                        <button className={globalStyles.mainButton} style={{width: "100%"}} onClick={handlePhoneNumber}>Confirm phone number</button>

                    </div>
                </div>
            ) : (
                <div className={`${globalStyles.selectionName} ${styles.dFlex}`} style={{justifyContent: "center"}}>
                    YOUR PHONE NUMBER HAS BEEN CHANGED
                </div>
            )}


        </div>
    )
}
