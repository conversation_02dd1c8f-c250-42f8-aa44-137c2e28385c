.tab {
  background-color: #170903;
  display: flex !important;
  justify-content: stretch !important;
  margin-top: 20px;
}

.dFlex {
  display: flex;
  align-items: center;
}

.container {
  height: 48px;
  width: 100%;
  border: 1px solid #dcdad9;
  padding: 10px 20px;
  border-radius: 5px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 25px;
  /*justify-content: stretch;*/
}

.container:hover {
  background-color: #dcdad9;
}

.iconStyle {
  height: 24px;
  width: 24px;
  cursor: pointer;
}

.dataTypeIcon {
  height: 16px;
  width: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.fieldWithIcon {
  display: flex;
  align-items: center;
}

.boxName {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  color: #170903;
  line-height: 24px;
}

.buttonStyle {
  border: 1px solid #dcdad9;
  padding: 5px 15px;
  border-radius: 3px;
  background-color: white;
  cursor: pointer;
}

.tableContainer {
  overflow-x: auto;
  /*margin-bottom: 150px;*/
}

.iconText {
  font-size: 12px;
}

.typeChip {
  background-color: white;
  width: 35px;
  height: 24px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #b9b5b3;
  color: #746b68;
  margin: auto;
}

.checkbox {
  height: 1.25rem;
  width: 1.25rem;
  color: #2563eb;
}

.linkButton {
  color: #6b7280;
}

.linkButton:hover {
  color: #374151;
}

.iconContainer {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.formCheckInput:checked {
  appearance: none;
  /*background-color: #746B68;*/
  /*border-color: #746B68;*/
  position: relative;

  input {
    padding: 0 !important;
  }
}

.formCheckInput:checked::after {
  appearance: none;
  padding: 0;
  content: "";
  width: 8px;
  height: 8px;
  background-color: #5a524f;
  border-radius: 50%;
  /*display: block;*/
  position: absolute;
  /*top: 50%;*/
  /*left: 50%;*/
  transform: translate(-50%, -50%);
}

.fieldName {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  color: #170903;
}

.settingsContainer {
  position: relative;
  display: inline-block;
}

.settingsDropdown {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  border: 1px solid #dcdad9;
  border-radius: 4px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
  padding: 5px 0;
}

.settingsOption {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 12px;
}

.settingsOption:hover {
  background-color: #f5f5f5;
}

.filterContainer {
  position: relative;
  display: inline-block;
  z-index: 1000;
}

.filterDropdown {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  border: 1px solid #dcdad9;
  border-radius: 4px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 170px;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 5px;
  overflow: hidden;
}

.filterOption {
  padding: 12px 15px;
  cursor: pointer;
  font-size: 12px;
  width: 100%;
  border-bottom: 1px solid #dcdad9;
  color: #170903;
  font-family: Inter, sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
  box-sizing: border-box;
}

.filterOption:last-child {
  border-bottom: none;
}

.filterOption:hover {
  background-color: #f5f5f5;
}

.noDataCell {
  height: 100px;
  text-align: center;
}

.noDataFound {
  text-align: center;
  font-size: 14px;
  color: #746b68;
  width: 100%;
  padding: 20px;
  font-family: Inter, sans-serif;
}

.restoreButton {
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  cursor: pointer;
  width: 100%;
  margin-top: 10px;
}
