
/*.modalOverlay {*/
/*    position: fixed;*/
/*    top: 0;*/
/*    left: 0;*/
/*    right: 0;*/
/*    bottom: 0;*/
/*    background-color: rgba(0, 0, 0, 0.7);*/
/*    display: flex;*/
/*    justify-content: center;*/
/*    align-items: center;*/
/*    z-index: 1000;*/
/*}*/

.modalContent {
    background: #170903;
    border-radius: 10px;
    width: auto;
    /*max-width: 95%;*/
    color: white;
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;

}


.closeButton {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

.modalBody {
    padding: 20px;
}

.editorContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.avatarEditor {
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 20px;
    border: 2px solid #444;
}

.controls {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.uploadControls {
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 20px;

    margin: 0 auto;
}

.uploadButton, .resetButton {
    padding: 5px 15px;
    background: #FFFFFF;
    border: 1px solid #DCDAD9;
    border-radius: 3px;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    display: flex;
    align-items: center;

}
.uploadIcon {
    display: flex;
    align-items: center;
    padding-left: 15px;
}

.uploadButton:hover, .resetButton:hover {
    background-color: #cbc9c9;
}


.fileInput {
    display: none;
}

.supportedFormats {
    color: #888;
    font-size: 14px;
    text-align: center;
    margin: 10px 0;
}


.avatarEditor canvas {
    border-radius: 50%;
}
.dFlex{
    display: flex;
    align-items: center;
}
