.container {
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
}

.mainSection {
  position: absolute;
  top: 0;
  left: 240px;
  right: 0;
  bottom: 0;
  padding: 20px 45px 20px 45px;
  transition: left 0.3s ease;
  background-color: #fff;
  overflow-y: auto;
  overflow-x: hidden;
}

.mainSection.expanded {
  left: 110px;
}

.headerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.titleWithBackButton {
  display: flex;
  align-items: center;
  gap: 10px;
}

.backButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.backButton:hover {
  background-color: #e9ecef;
}

.backIcon {
  width: 20px;
  height: 20px;
  color: #170903;
}

.headerControls {
  display: flex;
  gap: 10px;
}

.statsContainer {
  display: flex;
  gap: 15px;
  width: 100%;
  margin-bottom: 20px;
}

.statItem {
  flex: 1;
  height: 78px;
  border-radius: 5px;
  border: 1px solid #ddd;
  padding: 15px 25px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.statNumber {
  font-size: 24px;
  font-weight: bold;
  color: #746b68;
  font-family: Poppins;
}

.statContent {
  display: flex;
  align-items: center;
  gap: 10px;
}

.statLabel {
  font-size: 14px;
  color: #170903;
  font-family: Inter, sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  white-space: nowrap;
}

.migrationReportSection {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 20px;
  overflow: hidden;
  background-color: white;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #170903;
  color: #ea5822;
  padding: 10px 15px;
  font-family: Poppins;
}

.sectionHeader h2 {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.statusBadge {
  display: inline-flex;
  padding: 5px 15px;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.completed {
  background-color: #2F855A;
  color:white;
}
.payloadButton {
  background: transparent;
  border: none;
  cursor: pointer;
  color: #666;
  font-size: 18px;
}


.partiallycompleted{
  background-color: #F59E0B;
  color:white;
}

.scheduled {
  background-color: grey;
  color: black;
}

.inProgress {
  background-color: #746B68;
  color: white;
}

.failed {
  background-color: #DC2626;
  color: white;
}

.unknown {
  background-color:#6B7280;
  color:white;
}


.detailsGrid {
  padding: 15px;
}

.detailRow {
  display: flex;
  margin-bottom: 15px;
}

.detailRow:last-child {
  margin-bottom: 0;
}

.detailColumn {
  flex: 1;
  padding: 0 10px;
}

.detailLabel {
  font-size: 12px;
  color: #746b68;
  margin-bottom: 5px;
  font-family: Inter, sans-serif;
}

.detailValue {
  font-size: 14px;
  color: #170903;
  font-family: Inter, sans-serif;
  font-weight: 500;
}

.collapsibleSection {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 20px;
  overflow: hidden;
  background-color: white;
}

.collapsibleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #170903;
  color: #ea5822;
  padding: 10px 15px;
  font-family: Poppins;
  cursor: pointer;
}

.collapsibleHeader h2 {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.chevron {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.chevronUp {
  transform: rotate(180deg);
}

.collapsibleContent {
  padding: 0;
}

.seeMoreContainer {
  display: flex;
  justify-content: center;
  padding: 10px;
  border-top: 1px solid #eee;
}

.seeMore {
  color: #746b68;
  font-size: 14px;
  font-family: Inter, sans-serif;
  cursor: pointer;
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
}

.migrateAgainButton,
.downloadReportButton {
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  font-family: Inter, sans-serif;
}

.migrateAgainButton {
  background-color: #ef8963;
  color: #170903;
}

.downloadReportButton {
  background-color: #ef8963;
  color: #170903;
}

/* Activity Table Styles */
.activityTableWrapper {
  width: 100%;
  overflow-x: auto;
}

.activityTable {
  width: 100%;
  border-collapse: collapse;
  font-family: "Inter", sans-serif;
}

.activityTable th {
  background-color: #f5f5f5;
  padding: 12px 16px;
  text-align: left;
  color: #746b68;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.activityTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #170903;
}

.activityTable tr:last-child td {
  border-bottom: none;
}

/* Payment Details Styles */
.paymentDetailsContainer {
  display: flex;
  padding: 20px;
  background-color: #f9f9f9;
}

.paymentDetailsLeft {
  flex: 1;
  padding-right: 20px;
}

.paymentDetailsRight {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.paymentDetailRow {
  display: flex;
  border-bottom: 1px solid #eee;
  padding: 10px 0;
}

.paymentDetailRow:last-of-type {
  border-bottom: none;
}

.paymentDetailLabel {
  flex: 1;
  color: #746b68;
  font-size: 14px;
  font-family: Inter, sans-serif;
  font-weight: 600;
}

.paymentDetailValue {
  flex: 1;
  color: #170903;
  font-size: 14px;
  font-weight: 500;
  font-family: Inter, sans-serif;
}

.downloadInvoiceButton {
  margin-top: 15px;
  padding: 10px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  color: #170903;
  font-family: Inter, sans-serif;
}

.downloadInvoiceButton:hover {
  background-color: #f5f5f5;
}

/* Batch Table Styles */

.batchTableContainer {
  display: flex;
  justify-content: center; /* Centers the table */
  width: 100%;
  overflow-x: auto;
}

.batchTableWrapper {
  width: 100%;
  overflow-x: auto;
  padding: 15px;
  background-color: white;
  border-radius: 5px;
}

.tableHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tableHeader h3 {
  font-size: 16px;
  color: #170903;
  font-weight: 600;
  margin: 0;
}

.batchTable {
  width: 100%;
  border-collapse: collapse;
  font-family: "Inter", sans-serif;
}

.batchTable th {
  background-color: #f5f5f5;
  padding: 12px 16px;
  text-align: left;
  color: #746b68;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.batchTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #170903;
}

.statusIndicator {
  display: inline-flex;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.migrationIdLink {
  color: #0096ff;
  text-decoration: underline;
  cursor: pointer;
}

.batchNameLink {
  color: #0096ff;
  text-decoration: underline;
  cursor: pointer;
}

/* Chart and Stats Container */
.chartAndStatsContainer {
  display: flex;
  padding: 20px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  border-radius: 5px;
  margin: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: inter;
}

.chartContainer {
  flex: 0 0 50%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 5px;
  padding: 15px;
}

.chartHeader {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chartHeader h3 {
  font-size: 16px;
  color: #170903;
  font-weight: 600;
  font-family: Inter, sans-serif;
  margin: 0;
}

.statsGrid {
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 20px;
  padding-left: 30px;
}

.statGridItem {
  display: flex;
  align-items: center;
  gap: 10px;
}

.statGridLabel {
  font-size: 14px;
  color: #746b68;
  font-weight: 600;
  width: 150px;
  font-family: inter;
}

.statGridValue {
  font-size: 16px;
  color: #170903;
  font-weight: 500;
}

.failedValue {
  color: #ff5252;
}

.retryButton {
  background-color: #ef8963;
  color: #170903;
  border: none;
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  font-family: Inter, sans-serif;
}

.retryButton:hover {
  background-color: #d67553;
}

.loadingIndicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
  width: 100%;
  color: #746b68;
  font-size: 16px;
  font-family: Inter, sans-serif;
}

.customTooltip {
  background-color: white;
  border: 1px solid #ddd;
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  color: #170903;
  font-family: Inter;
}

.noDataMessage {
  padding: 20px;
  text-align: center;
  color: #746b68;
  font-size: 14px;
  font-family: Inter;
}

.noBatchData {
  text-align: center;
  padding: 20px;
  color: #746b68;
}

/* Batch Execution Summary Styles */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #746b68;
}

.timeItem {
  display: flex;
  align-items: center;
  gap: 10px;
}

.timeLabel {
  font-size: 14px;
  font-weight: 500;
  color: #746b68;
}

.timeValue {
  font-size: 14px;
  color: #170903;
}

.recordsSection {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 20px;
  font-family: inter;
}

.recordsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.recordsHeader h2 {
  font-size: 16px;
  color: #170903;
  font-weight: 600;
  margin: 0;
}

.filterContainer {
  position: relative;
}

.filterDropdownContainer {
  position: relative;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 5px;
  background: #fff;
  border: 1px solid #dcdad9;
  border-radius: 3px;
  padding: 8px 15px;
  font-size: 14px;
  color: #170903;
  cursor: pointer;
}

.filterIcon {
  font-size: 16px;
  color: #746b68;
}

.filterDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.filterOption {
  padding: 10px 15px;
  font-size: 14px;
  color: #170903;
  cursor: pointer;
  transition: background-color 0.2s;
  font-family: inter;
}

.filterOption:hover {
  background-color: #f5f5f5;
}

.recordsTableContainer {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 15px;
  font-family: inter;
}

.recordsTable {
  width: 100%;
  border-collapse: collapse;
  font-family: "Inter", sans-serif;
}

.recordsTable th {
  background-color: #f5f5f5;
  padding: 12px 16px;
  text-align: left;
  color: #746b68;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.recordsTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #170903;
}

.logMessageCell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: inter;
}

.actionButtons {
  display: flex;
  gap: 5px;
}

.retryRecordButton,
.expandRecordButton {
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retryRecordButton {
  color: #ef8963;
}

.expandRecordButton {
  color: #746b68;
}

.retryRecordButton:hover,
.expandRecordButton:hover {
  background-color: #f5f5f5;
}

.noRecordsCell {
  text-align: center;
  padding: 20px;
  color: #746b68;
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pageInfo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.paginationText {
  font-size: 14px;
  color: #746b68;
  font-family: "inter";
}

.pageButtons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.arrowButton {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  color: #746b68;
}

.arrowButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageButton {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  color: #170903;
  font-size: 12px;
}

.pageButton.active {
  background-color: #170903;
  color: white;
  border-color: #170903;
}

.pageEllipsis {
  width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #746b68;
}

.rowsPerPageContainer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rowsButtons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rowsButton {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  color: #170903;
  font-size: 12px;
}

.rowsButton.active {
  background-color: #170903;
  color: white;
  border-color: #170903;
}

/* Expanded record styles */
.expandedRow {
  background-color: #ffffff;
}

.expandedContent {
  padding: 15px;
}

.loadingSpinner {
  display: inline-block;
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #ef8963;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.conversationContainer {
  background-color: white;
  border-radius: 5px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.conversationContainer h4 {
  font-size: 14px;
  color: #170903;
  margin-top: 0;
  margin-bottom: 10px;
  font-family: Inter, sans-serif;
}

.conversationData {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  overflow-x: auto;
}

.conversationTable {
  width: 100%;
  border-collapse: collapse;
  font-family: "Inter", sans-serif;
  font-size: 12px;
}

.conversationTable th {
  background-color: #e0e0e0;
  padding: 8px 12px;
  text-align: left;
  color: #746b68;
  font-weight: 600;
  border-bottom: 1px solid #ccc;
}

.conversationTable td {
  padding: 8px 12px;
  border-bottom: 1px solid #ddd;
  color: #170903;
}

/* Success and error row styles */
.successRow {
  background-color: rgba(76, 175, 80, 0.05);
}

.errorRow {
  background-color: rgba(255, 82, 82, 0.05);
}

/* Table container */
.tableContainer {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 15px;
}

/* Loader container */
.loaderContainer {
  text-align: center;
  padding: 20px;
}

.dFlex {
  display: flex;
  align-items: center;
}

.statsCard {
  width: 50%;
  border-radius: 10px;
  border: 1px solid #dcdad9;
  padding: 20px;
}

.statsContent {
  display: flex;
  gap: 20px;
}
.metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.statusBadge {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px 15px;
  border-radius: 7px;
  width: 75px;
  height: 30px;
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: #170903;
}

.metricItem {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
  gap: 12px;
  margin-left: 15px;
}

.metricNumber {
  font-size: 36px;
  font-weight: 600;
  color: #746b68;
  line-height: 24px;
  font-family: Poppins;
}
.metricLabel {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-size: 16px;
  justify-content: flex-end;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.templateDot {
  background-color: #A43E18;
}

.migrationDot {
  background-color: #EF8963;
}

.dotName {
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: #170903;
}
.chartContainer {
  flex: 1;
  position: relative;
}
.summaryCard {
  width: 50%;
  padding: 0 20px;
  margin-top: 0;
  flex: 1.2;
}
.summaryContent {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.summaryRow {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 60px;
  align-items: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
  display: inline-block;
}
