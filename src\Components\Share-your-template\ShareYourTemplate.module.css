.modalOverlay {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modalContainer {
    width: 725px;
    background-color: #120b08;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    padding: 30px 45px 35px 45px;
    display: flex;
    flex-direction: column;
  }

  .modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .modalTitle {
    color: #EA5822;
    font-family: Poppins;
    font-size: 27px;
    font-weight: 600;
    line-height: 24px;
  }

  .closeButton {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
  }

  .modalContent {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .templateInfo {
    margin-bottom: 10px;
  }

  .infoRow {
    display: flex;
    justify-content: space-between;
    gap: 45px;
    flex-wrap: wrap;
  }

  .infoItem {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .infoLabel {
    color: #97908E;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
  }

  .infoValue {
    color: #F8F8F7;
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }

  .detailsHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .sectionTitle {
    color: #EA5822;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 700;
    line-height: 24px;
  }

  .copyLinkButton {
    background: none;
    border: 1px solid #a0a0a0;
    color: #97908E;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
  }

  .linkIcon {
    font-size: 16px;
  }

  .formGroup {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .formLabel {
    flex: 0 0 120px;
    color: #97908E;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
  }

  .formInput {
    flex: 1;
    padding: 10px 15px;
    border-radius: 4px;
    border: none;
    background-color: white;
    font-family: "Inter";
    font-weight: 400;
    font-size: 16px;
  }

  .selectWrapper {
    position: relative;
    flex: 1;
  }

  .formSelect {
    width: 100%;
    padding: 10px 15px;
    border-radius: 4px;
    border: none;
    background-color: white;
    font-family: "Inter", sans-serif;
    font-weight: 400;
    font-size: 16px;
    appearance: none;
  }

  .selectArrow {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }
