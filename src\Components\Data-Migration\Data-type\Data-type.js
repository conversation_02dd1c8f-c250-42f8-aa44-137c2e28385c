"use client"

import { useContext, useEffect, useState } from "react"
import styles from "./Data-type.module.css"
import globalStyles from "../../globalStyles.module.css"
import { HiXMark, HiTrash, HiDocument } from "react-icons/hi2"
import {
  executeApi,
  getMigrationEntities,
  getMigrationObjects,
  getMigrationTargetById,
  getUniqueValuesFromCSV,
  saveMigrationPlan,
  validateSourceData,
} from "../../apiService"
import { MigrationContext } from "../Data-Migration"
import UploadCSV from "../UploadCSV/uploadcsv"
import { toast, ToastContainer } from "react-toastify"
import LoaderSpinner from "../../loaderspinner"
import { displayArticle } from "../../../Helper/helper"
import { BlobServiceClient } from "@azure/storage-blob"

const deleteFileFromAzureBlob = async (fileUrl) => {
  try {
    const SAS_URL = "https://saasgeniestorage.blob.core.windows.net/?sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2025-12-31T13:47:32Z&st=2025-06-02T05:47:32Z&spr=https,http&sig=3lxpy88Cx8o0JfTZDtYk54ESpIZElAmlCTmeZKkwh6s%3D"
    const blobServiceClient = new BlobServiceClient(SAS_URL)

    // Extract container name and blob name from the file URL
    const url = new URL(fileUrl)
    const containerName = "migrategenie-v2-storage" // Replace with your container name
    const blobName = decodeURIComponent(url.pathname.split("/").pop())

    const containerClient = blobServiceClient.getContainerClient(containerName)
    const blockBlobClient = containerClient.getBlockBlobClient(blobName)

    await blockBlobClient.delete()
    console.log(`File deleted successfully: ${blobName}`)
  } catch (error) {
    console.error("Error deleting file from Azure Blob Storage:", error)
    throw error
  }
}

export default function Datatype({
  setSelectedTab,
  setDependentFields,
  dependentFields,
  templateName,
  planId,
  setPlanId,
}) {
  const { migrationState, setMigrationState } = useContext(MigrationContext)
  const dataTypeData = migrationState.dataTypeData
  const source = migrationState.sourceObjData
  const target = migrationState.targetData
  const selectedObjectData = migrationState.selectedObjectData

  const [email, setEmail] = useState(localStorage.getItem("email"))
  const [selectedData, setSelectedData] = useState([])
  const [entityData, setEntityData] = useState([])
  const [objectData, setObjectData] = useState([])
  const [selectedObject, setSelectedObject] = useState(null)
  const [selectedObjectName, setSelectedObjectName] = useState(null)
  const [selectedObjectIndex, setSelectedObjectIndex] = useState(null)
  const [checked, setChecked] = useState(false)
  const [numberOfRecords, setNumberOfRecords] = useState("")
  const [confirm, setConfirm] = useState(false)
  const [showUploadCSV, setShowUploadCSV] = useState(false)
  const [uploadedFileData, setUploadedFileData] = useState(null)
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [currentUploadEntity, setCurrentUploadEntity] = useState(null)
  const [pendingEntities, setPendingEntities] = useState([])
  const [isLastEntity, setIsLastEntity] = useState(false)
  const [allFilesUploaded, setAllFilesUploaded] = useState(false)
  const [requiredEntities, setRequiredEntities] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [fileToDelete, setFileToDelete] = useState({ index: null, name: "" })

  useEffect(() => {
    console.log(source?.source?.imgUrl, target?.target?.imgUrl)
  }, [source, target])

  // Handle dependent field selection through the UI

  const handleConfirm = () => {
    const isCSVSource = source?.source?.name?.toLowerCase() === "csv"

    // Always set the required entities (main entity + dependent fields)
    const required = [selectedObjectName, ...dependentFields];
    setRequiredEntities(required);

    if (isCSVSource) {
      // Check if we already have all required files uploaded
      const uploadedEntityNames = [...new Set(uploadedFiles.map(file => file.entity))];
      const allUploaded = required.every(entity => uploadedEntityNames.includes(entity));

      setAllFilesUploaded(allUploaded);

      if (allUploaded) {
        // If all files are already uploaded, proceed to confirmation
        setConfirm(true);
      } else {
        // Determine which entity to upload next
        // Start with the main entity if it's not uploaded yet
        if (!uploadedEntityNames.includes(selectedObjectName)) {
          setCurrentUploadEntity(selectedObjectName);

          // Set up pending entities (dependent fields that need files)
          const pendingEnts = selectedObject?.dependent
            ?.filter((val) => dependentFields.includes(val) && !uploadedEntityNames.includes(val))
            ?.map((entityName) => entityName) || [];

          setPendingEntities(pendingEnts);
          setIsLastEntity(pendingEnts.length === 0);
        } else {
          // Main entity is uploaded, find the first dependent field that needs a file
          const pendingEnts = dependentFields.filter(dep => !uploadedEntityNames.includes(dep));

          if (pendingEnts.length > 0) {
            setCurrentUploadEntity(pendingEnts[0]);
            setPendingEntities(pendingEnts.slice(1));
            setIsLastEntity(pendingEnts.length <= 1);
          } else {
            // All entities have files, proceed to confirmation
            setAllFilesUploaded(true);
            setConfirm(true);
            return;
          }
        }

        // Show the upload dialog
        setShowUploadCSV(true);
      }
    } else {
      // For non-CSV sources, just confirm
      setConfirm(true);
    }
  }

  // Helper function to ensure main entity is always at index 0
  const sortUploadedFiles = (files) => {
    if (!files || files.length === 0) return [];

    // Create a copy of the array to avoid mutating the original
    const sortedFiles = [...files];

    // Find the index of the main entity file
    const mainEntityIndex = sortedFiles.findIndex(file => file.entity === selectedObjectName);

    // If main entity file exists and it's not already at index 0, move it there
    if (mainEntityIndex > 0) {
      // Remove the main entity file from its current position
      const mainEntityFile = sortedFiles.splice(mainEntityIndex, 1)[0];
      // Insert it at the beginning of the array
      sortedFiles.unshift(mainEntityFile);
    }

    return sortedFiles;
  };

  const handleFileUploaded = (fileData) => {
    const fileWithEntity = {
      ...fileData,
      entity: currentUploadEntity,
      uploadedAt: new Date().toISOString()
    }

    // Check if we already have a file for this entity
    const existingFileIndex = uploadedFiles.findIndex(file => file.entity === currentUploadEntity);

    let newUploadedFiles;
    if (existingFileIndex !== -1) {
      // Replace the existing file
      const oldFile = uploadedFiles[existingFileIndex];

      // Delete the old file from Azure if it exists
      if (oldFile.azureBlobUrl) {
        // We don't await this since we don't want to block the UI
        // It's okay if this fails as it's just cleanup
        deleteFileFromAzureBlob(oldFile.azureBlobUrl).catch(err => {
          console.error("Error deleting old file from Azure:", err);
        });
      }

      // Create a new array with the replaced file
      newUploadedFiles = [...uploadedFiles];
      newUploadedFiles[existingFileIndex] = fileWithEntity;
    } else {
      // Add the new file
      newUploadedFiles = [...uploadedFiles, fileWithEntity];
    }

    // Sort the files to ensure main entity is at index 0
    const sortedFiles = sortUploadedFiles(newUploadedFiles);

    // Update the state with the sorted files array
    setUploadedFiles(sortedFiles);
    console.log("Uploaded files:", sortedFiles);
    setUploadedFileData(fileWithEntity);

    // Close the upload dialog
    setShowUploadCSV(false);

    // Check if all required entities have files
    const uploadedEntityNames = [...new Set(newUploadedFiles.map(file => file.entity))];
    const allUploaded = requiredEntities.every(entity => uploadedEntityNames.includes(entity));
    setAllFilesUploaded(allUploaded);

    // Display a success message
    const action = existingFileIndex !== -1 ? "replaced" : "uploaded";
    toast.success(`File for ${currentUploadEntity} ${action} successfully!`, {
      position: "top-right",
    });

    // If this is the last entity in the current batch or all files are uploaded, show confirmation
    if (isLastEntity || allUploaded) {
      setConfirm(true);
    } else {
      // If there are more entities to upload, prepare for the next one
      if (pendingEntities.length > 0) {
        const nextEntity = pendingEntities[0];
        const remainingEntities = pendingEntities.slice(1);
        setTimeout(() => {
          setCurrentUploadEntity(nextEntity);
          setPendingEntities(remainingEntities);
          setIsLastEntity(remainingEntities.length === 0);
          setShowUploadCSV(true);
        }, 100);
      }
    }

    console.log("File uploaded for entity:", currentUploadEntity, fileData);
  }

  const handleDeleteConfirmation = (index, fileName) => {
    setFileToDelete({ index, name: fileName })
    setShowDeleteConfirmation(true)
  }

  const handleDeleteFile = async () => {
    if (fileToDelete.index !== null) {
      const fileToRemove = uploadedFiles[fileToDelete.index]
      const entityToRemove = fileToRemove.entity

      try {
        // Delete from Azure Blob Storage
        if (fileToRemove.azureBlobUrl) {
          await deleteFileFromAzureBlob(fileToRemove.azureBlobUrl)
        }

        // Create a new array without the deleted file
        const updatedFiles = [...uploadedFiles];
        updatedFiles.splice(fileToDelete.index, 1);

        // Sort the files to ensure main entity is at index 0 (if it exists)
        const sortedFiles = sortUploadedFiles(updatedFiles);

        // Update the state with the sorted array
        setUploadedFiles(sortedFiles);
        console.log("Files after deletion and sorting:", sortedFiles);

        // Update allFilesUploaded state based on the updated files
        const updatedEntities = [...new Set(updatedFiles.map(file => file.entity))];
        const allUploaded = requiredEntities.every(entity => updatedEntities.includes(entity));
        setAllFilesUploaded(allUploaded);

        // Close the confirmation dialog
        setShowDeleteConfirmation(false)

        // Display a success message
        toast.success(`File for ${entityToRemove} deleted successfully!`, {
          position: "top-right",
        });

        // Set the current entity for potential re-upload
        setCurrentUploadEntity(entityToRemove)

        // Automatically show the upload dialog for the deleted entity
        setShowUploadCSV(true)
      } catch (error) {
        console.error("Error deleting file:", error)
        toast.error("Failed to delete file. Please try again.")
      }
    }
  }

  const closeUploadDialog = () => {
    // Just close the upload dialog without changing any other state
    setShowUploadCSV(false)
  }

  const handleUploadForEntity = (entity) => {
    setCurrentUploadEntity(entity)
    setShowUploadCSV(true)
  }

  useEffect(() => {
    console.log(uploadedFileData)
  }, [uploadedFileData])

  useEffect(() => {
    if (dataTypeData) {
      setChecked(dataTypeData.checked)
      setSelectedObjectName(dataTypeData.selectedObjectName)
      setSelectedObject(dataTypeData.selectedObject)
      setSelectedObjectIndex(dataTypeData.selectedObjectIndex)
      setEntityData(dataTypeData.entityData)
      setObjectData(dataTypeData.objectData)
      setNumberOfRecords(dataTypeData.numberOfRecords)
      setDependentFields(dataTypeData.dependentFields)
      setConfirm(dataTypeData.confirm)
      if (dataTypeData.uploadedFiles) {
        // Sort the files to ensure main entity is at index 0
        const sortedFiles = sortUploadedFiles(dataTypeData.uploadedFiles);
        setUploadedFiles(sortedFiles);
      }

      // Set requiredEntities based on the loaded data
      if (dataTypeData.selectedObjectName && dataTypeData.dependentFields) {
        setRequiredEntities([dataTypeData.selectedObjectName, ...dataTypeData.dependentFields]);
      }
    }
  }, [dataTypeData])

  // Check if all required files are uploaded whenever uploadedFiles or requiredEntities change
  useEffect(() => {
    if (uploadedFiles.length > 0 && requiredEntities.length > 0) {
      const uploadedEntityNames = [...new Set(uploadedFiles.map(file => file.entity))];
      const allUploaded = requiredEntities.every(entity => uploadedEntityNames.includes(entity));
      setAllFilesUploaded(allUploaded);
    } else {
      setAllFilesUploaded(false);
    }
  }, [uploadedFiles, requiredEntities])

  // Ensure files are sorted when selectedObjectName changes
  useEffect(() => {
    if (uploadedFiles.length > 0 && selectedObjectName) {
      // Sort the files to ensure main entity is at index 0
      const sortedFiles = sortUploadedFiles(uploadedFiles);

      // Only update if the order has changed
      const hasOrderChanged = JSON.stringify(sortedFiles) !== JSON.stringify(uploadedFiles);

      if (hasOrderChanged) {
        console.log("Reordering files to put main entity first:", selectedObjectName);
        setUploadedFiles(sortedFiles);
      }
    }
  }, [selectedObjectName])

  const executeFun = async (exe) => {
    try {
      const res = await executeApi(exe)
      return res
    } catch (e) {
      throw e
    }
  }

  const updateQueryParams = (executor, source) => {
    if (!executor) return

    const updateOrAddParam = (key, value) => {
      const index = executor.queryParams.findIndex((param) => param.key === key)
      if (index > -1) {
        executor.queryParams[index].value = value
      } else {
        executor.queryParams.push({ key, value, description: "", req: false })
      }
    }

    if (source.username && source.password) {
      updateOrAddParam("username", source.username)
      updateOrAddParam("password", source.password)
    }
    addQueryParamsToSource(executor)
  }

  const updateTargetQueryParameter = (targetMappingExecutor, target) => {
    Object.entries(target.formData).forEach(([key, value]) => {
      if (key === "instance_url") {
        updateOrAddParam(
          targetMappingExecutor,
          "domainUrl",
          target.domainType === "sourceDomain" ? extractSourceDomain(value) : extractSubdomain(value),
        )
        updateOrAddParam(
          targetMappingExecutor,
          "domain",
          target.domainType === "sourceDomain" ? extractSourceDomain(value) : extractSubdomain(value),
        )
      } else {
        updateOrAddParam(targetMappingExecutor, key, value)
      }
    })
  }

  const updateOrAddParam = (executor, key, value, description, req) => {
    executor.queryParams = executor.queryParams || []

    const param = executor.queryParams.find((p) => p.key === key)
    param ? (param.value = value) : executor.queryParams.push({ key, value, description, req })
  }

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedObjectData) return

      setIsLoading(true)

      const { sourceExecutor, targetMappingExecutor, sourceResponseAttributesExecutor, sourceMappingExecutor } =
        selectedObjectData

      const promises = []
      updateQueryParams(sourceExecutor, source?.connDetails)

      if (targetMappingExecutor) {
        updateTargetQueryParameter(targetMappingExecutor, target)
        const targetPromise = executeFun(targetMappingExecutor).then((result) => ({
          key: "targetExeRes",
          result,
        }))
        promises.push(targetPromise)
      }

      if (sourceResponseAttributesExecutor) {
        updateQueryParams(sourceResponseAttributesExecutor, source?.connDetails)
        const responsePromise = executeApi(sourceResponseAttributesExecutor).then((result) => ({
          key: "sourceResAtt",
          result: result.response,
        }))
        promises.push(responsePromise)
      }

      if (sourceMappingExecutor) {
        updateQueryParams(sourceMappingExecutor, source?.connDetails)
        const mappingPromise = executeApi(sourceMappingExecutor).then((result) => ({
          key: "sourceMapRes",
          result,
        }))
        promises.push(mappingPromise)
      }

      try {
        const results = await Promise.all(promises)

        results.forEach(({ key, result }) => {
          if (key === "targetExeRes" || key === "sourceResAtt" || key === "sourceMapRes") {
            setMigrationState((prev) => ({
              ...prev,
              [key]: result,
            }))
          }
        })
        setMigrationState((prev) => {
          if (prev.apiCallsPending && prev.apiCallsCompleteCallback) {
            prev.apiCallsCompleteCallback()
            return {
              ...prev,
              apiCallsPending: false,
              apiCallsCompleteCallback: null,
            }
          }
          return prev
        })
      } catch (error) {
        console.error("Error executing APIs:", error)
        setMigrationState((prev) => {
          if (prev.apiCallsPending && prev.apiCallsCompleteCallback) {
            prev.apiCallsCompleteCallback()
            return {
              ...prev,
              apiCallsPending: false,
              apiCallsCompleteCallback: null,
            }
          }
          return prev
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (selectedObjectData) {
      fetchData()
    }
  }, [selectedObjectData])

  useEffect(() => {
    console.log(selectedObjectData, "selectedObjectData")
  }, [selectedObjectData])

  const moveToNext = async () => {
    try {
      setIsLoading(true)

      const obj = {
        selectedObject: selectedData,
        selectedObjectName: selectedObjectName,
        checked: checked,
        selectedObjectIndex: selectedObjectIndex,
        entityData: entityData,
        objectData: objectData,
        numberOfRecords: numberOfRecords,
        dependentFields: dependentFields,
        confirm: confirm,
        uploadedFiles: uploadedFiles,
      }

      setMigrationState((prevState) => ({
        ...prevState,
        dataTypeData: obj,
      }))

      await saveMigration(obj)

      if (source.type === "api") {
        setMigrationState((prevState) => ({
          ...prevState,
          selectedEntityData: entityData.migrationMapping[selectedObjectIndex],
          selectedObjectData: objectData?.migration_objects?.find((obj) => obj.migrationEntity === selectedObjectName),
        }))
      } else {
        setMigrationState((prevState) => ({
          ...prevState,
          selectedEntityData: entityData.migrationMapping[selectedObjectIndex],
          selectedObjectData: objectData?.migration_objects[0],
        }))
      }

      await new Promise((resolve) => setTimeout(resolve, 100))

      const updatedObjectData =
        source.type === "api"
          ? objectData?.migration_objects?.find((obj) => obj.migrationEntity === selectedObjectName)
          : objectData?.migration_objects[0]

      if (updatedObjectData) {
        const apiCallsPromise = new Promise((resolve) => {
          setMigrationState((prev) => ({
            ...prev,
            apiCallsPending: true,
            apiCallsCompleteCallback: resolve,
          }))
        })

        await apiCallsPromise
      }

      let res;
      if (source.type === "api") {
        res = await validateData()
      } else {
        res = true
      }

      if (res) {
        setSelectedTab("4")
      }
    } catch (error) {
      console.error("Error during moveToNext:", error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const fetchUniqueValues = async () => {
      if (uploadedFiles && uploadedFiles.length > 0) {
        // Find the main entity file (should be at index 0 after our sorting)
        const mainEntityFile = uploadedFiles.find(file => file.entity === selectedObjectName) || uploadedFiles[0];

        const payload = {
          columns: mainEntityFile.headers,
          file: mainEntityFile.azureBlobUrl,
        }

        try {
          const res = await getUniqueValuesFromCSV(payload)
          setMigrationState((prev) => ({
            ...prev,
            sourceObjData: {
              ...prev.sourceObjData,
              uniqueSourceValues: res,
              lengthOfCSV: mainEntityFile.length,
            },
          }))
        } catch (error) {
          console.error("Error fetching unique values:", error)
        }
      }
    }

    fetchUniqueValues()
  }, [uploadedFiles, selectedObjectName])

  const saveMigration = async (obj) => {
    try {
      const payload = {
        plan_name: templateName,
        migration_objects: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        email_id: email,
        additional_details: {
          sourceObjData: source,
          targetData: target,
          dataTypeData: obj,
          dataMappingData: migrationState.dataMappingData,
          selectedObjectData: migrationState.selectedObjectData,
          selectedEntityData: migrationState.selectedEntityData,
          sourceExeRes: migrationState.sourceExeRes,
          targetExeRes: migrationState.targetExeRes,
        },
      }
      if (planId) {
        payload["plan_id"] = planId
        payload["action"] = "update"
        if (payload.createdAt) delete payload.createdAt
      } else {
        payload["action"] = "create"
      }
      const res = await saveMigrationPlan(payload)

      if (res?.message === "plan created successfully" && res?.response?.id) {
        setPlanId(res.response.id)
      }
    } catch (error) {
      toast.error(error?.response.data.message || "Something went wrong!", {
        position: "top-right",
      })
    }
  }

  // Helper function to parse JSON-stringified keys in an object
  const parseJsonFields = (obj) => {
    if (!obj || typeof obj !== 'object') return obj;

    const parsed = { ...obj };

    Object.keys(parsed).forEach(key => {
      const value = parsed[key];

      // Check if the value is a string that looks like JSON
      if (typeof value === 'string' && value.trim()) {
        // Check if it starts with { or [ (common JSON indicators)
        const trimmed = value.trim();
        if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
          (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
          try {
            parsed[key] = JSON.parse(value);
          } catch (error) {
            // If parsing fails, keep the original string value
            console.warn(`Failed to parse JSON for key "${key}":`, error);
          }
        }
      }
    });

    return parsed;
  };

  useEffect(() => {
    const fetchMigrationEntityData = async () => {
      try {
        let response = await getMigrationEntities(source.source.name, target.target.name)
        // Process each item to parse JSON fields and fetch target objects
        for (const item of response) {
          item.sourceField = item.sourceField ? JSON.parse(item.sourceField) : [];
          item.targetField = item.targetField ? JSON.parse(item.targetField) : [];
          item.sourceExtender = item.sourceExtender ? JSON.parse(item.sourceExtender) : [];
          // Fetch and replace mainTarget
          if (item.mainTarget) {
            try {
              const mainTargetData = await getMigrationTargetById(item.mainTarget);
              const parsedMainTargetData = parseJsonFields(mainTargetData);
              item.mainTarget = [parsedMainTargetData];
            } catch (error) {
              console.error(`Error fetching mainTarget ${item.mainTarget}:`, error);
              item.mainTarget = [];
            }
          } else {
            item.mainTarget = [];
          }

          // Fetch and replace dependentTargets
          if (item.dependentTargets && Array.isArray(item.dependentTargets)) {
            const dependentTargetsData = [];
            for (const targetId of item.dependentTargets) {
              try {
                const targetData = await getMigrationTargetById(targetId);
                const parsedTargetData = parseJsonFields(targetData);
                dependentTargetsData.push(parsedTargetData);
              } catch (error) {
                console.error(`Error fetching dependentTarget ${targetId}:`, error);
              }
            }
            item.dependentTargets = dependentTargetsData;
          } else {
            item.dependentTargets = [];
          }

          // Fetch and replace mappingTargets
          if (item.mappingTargets && Array.isArray(item.mappingTargets)) {
            const mappingTargetsData = [];
            for (const targetId of item.mappingTargets) {
              try {
                const targetData = await getMigrationTargetById(targetId);
                const parsedTargetData = parseJsonFields(targetData);
                mappingTargetsData.push(parsedTargetData);
              } catch (error) {
                console.error(`Error fetching mappingTarget ${targetId}:`, error);
              }
            }
            item.mappingTargets = mappingTargetsData;
          } else {
            item.mappingTargets = [];
          }
        }

        response = {
          migrationMapping: response,
          source: source.source.name,
          target: target.target.name
        }
        setEntityData(response)
        const entities =
          response?.migrationMapping?.flatMap(
            (mapping) =>
              mapping.sourceField?.map((field) => ({
                name: field.entity,
                dependent: field.dependentEntities || [],
              })) || [],
          ) || []
        setSelectedData(entities)
      } catch (error) {
        console.error("Error fetching migration entities:", error)
        setSelectedData([])
      }
    }

    const fetchMigrationObjectsData = async () => {
      try {
        const response = await getMigrationObjects(source.source.name, target.target.name)
        response.forEach(item => {
          item.sourceMappingExecutor = JSON.parse(item.sourceMappingExecutor);
          item.sourceExecutor = JSON.parse(item.sourceExecutor);
          item.sourceProvider = JSON.parse(item.sourceProvider);
          item.sourceQueryParamsOptions = JSON.parse(item.sourceQueryParamsOptions);
          item.sourceResponseAttributesExecutor = JSON.parse(item.sourceResponseAttributesExecutor);
          item.targetMappingExecutor = JSON.parse(item.targetMappingExecutor);
          item.paginationFields = JSON.parse(item.paginationFields);
        });
        const formattedResponse = {
          migration_objects: response,
          validation_rules: []
        }
        setObjectData(formattedResponse)
      } catch (error) {
        console.error("Error fetching migration objects:", error)
      }
    }
    fetchMigrationEntityData()
    fetchMigrationObjectsData()
  }, [])

  const addQueryParamsToSource = (data) => {
    replaceByKey(data.queryParams, "authorization", {
      key: "authorization",
      value: source.connDetails.username ? source.connDetails.username : source.connDetails.apikey,
      description: "",
      req: false,
    })
    replaceByKey(data.queryParams, "apikey", {
      key: "apikey",
      value: source.connDetails.apikey,
      description: "",
      req: false,
    })
    replaceByKey(data.queryParams, "domainUrl", {
      key: "domainUrl",
      value: extractSourceDomain(source.connDetails.instance_url),
      description: "",
      req: false,
    })
    if (source.queryParam && Object.keys(source.queryParam).length > 0) {
      updateQueryFilterParams(data.queryParams, source.queryParam)
    }
  }

  const updateQueryFilterParams = (source, target) => {
    if (source && target) {
      Object.keys(target).forEach((key) => {
        const index = source.findIndex((item) => item.key === key)

        if (index !== -1) {
          const item = source[index]
          item.value = target[key]
          if (!item.hasOwnProperty("description")) {
            item.description = ""
          }
          if (!item.hasOwnProperty("req")) {
            item.req = false
          }
        } else {
          source.push({
            key,
            value: target[key],
            description: "",
            req: false,
          })
        }
      })
    }
  }

  const extractSubdomain = (url) => {
    const subdomainMatch = url.match(/^(?:https?:\/\/)?([^./]+)\./)
    return subdomainMatch ? subdomainMatch[1] : null
  }
  const extractSourceDomain = (url) => {
    try {
      const trimmedUrl = url.trim()
      const parsedUrl = new URL(trimmedUrl)
      return parsedUrl.hostname
    } catch (error) {
      console.error("Invalid URL:", error)
      return null
    }
  }

  const validateData = async () => {
    const data = objectData?.migration_objects?.find((obj) => obj.migrationEntity === selectedObjectName)
    updateQueryParams(data.sourceExecutor, source?.connDetails)
    const queryParams = { queryParams: data.sourceExecutor.queryParams }

    try {
      const res = await validateSourceData(source.source.name, target.target.name, selectedObjectName, queryParams)

      return res.hasData
    } catch (error) {
      return false
    }
  }

  const handleContinueClick = async () => {
    // Check if batch size/number of records is entered
    if (!numberOfRecords || numberOfRecords.trim() === "") {
      toast.error(
        `${source?.source?.name?.toLowerCase() === "csv" ? "Batch size" : "Number of records"} is required!`,
        {
          position: "top-right",
        },
      )
      return
    }

    // For CSV source, check if all required files are uploaded
    if (source?.source?.name?.toLowerCase() === "csv") {
      const required = [selectedObjectName, ...dependentFields];
      const uploadedEntityNames = [...new Set(uploadedFiles.map(file => file.entity))];
      const allUploaded = required.every(entity => uploadedEntityNames.includes(entity));

      if (!allUploaded) {
        toast.error("Please upload all required files before continuing.", {
          position: "top-right",
        });
        return;
      }
    }

    await moveToNext()
  }

  const replaceByKey = (data, key, newValue) => {
    if (data) {
      const index = data.findIndex((item) => item.key === key)
      if (index !== -1) {
        data[index] = newValue
      }
    }
  }

  const handleObject = (item, name, index) => {
    setSelectedObjectIndex(index)
    setDependentFields([])
    setSelectedObject(item)
    setSelectedObjectName(name)
  }

  const handleDependent = (dep) => {
    setDependentFields((prevState) => {
      if (prevState.includes(dep)) {
        return prevState.filter((field) => field !== dep)
      } else {
        return [...prevState, dep]
      }
    })
  }

  const [showBackConfirmation, setShowBackConfirmation] = useState(false)

  const closeSelectedObject = () => {
    // If there are uploaded files, show a confirmation dialog
    if (uploadedFiles.length > 0) {
      setShowBackConfirmation(true)
    } else {
      // If no files are uploaded, just go back
      setConfirm(false)
    }
  }

  const confirmGoBack = () => {
    setShowBackConfirmation(false)
    setConfirm(false)
  }

  const cancelGoBack = () => {
    setShowBackConfirmation(false)
  }

  const handleNumberOfRecordsChange = (event) => {
    setNumberOfRecords(event.target.value)
  }

  return (
    <div>
      {showUploadCSV && (
        <UploadCSV
          onFileUploaded={handleFileUploaded}
          onClose={closeUploadDialog}
          entityName={currentUploadEntity}
          isLastEntity={isLastEntity}
          previouslyUploadedFiles={uploadedFiles.filter(file => file.entity === currentUploadEntity)}
        />
      )}
      {isLoading ? (
        <LoaderSpinner />
      ) : (
        <div>
          <div className={styles.dFlex}>
            <div className={styles.section} style={{ width: "35%" }}>
              <div className={styles.sourceTargetGraphic}>
                <div className={styles.sourceTargetContainer}>
                  <img
                    src="/assets/Sourcetargetconnector.png"
                    alt="Source Target Connector"
                    className={styles.backgroundImage}
                  />

                  {source && target && (
                    <div className={styles.logoOverlay}>
                      <div className={styles.sourceLogoContainer}>
                        <img
                          src={source.source?.imgUrl || "/assets/default-source.png"}
                          alt={source.source?.name || "Source"}
                          className={styles.connectionLogo}
                        />
                      </div>

                      <div className={styles.connectionArrow}>
                        <img src="/assets/Arrow.png" alt="Connection Arrow" className={styles.arrowImage} />
                      </div>

                      <div className={styles.targetLogoContainer}>
                        <img
                          src={target.target?.imgUrl || "/assets/default-target.png"}
                          alt={target.target?.name || "Target"}
                          className={styles.connectionLogo}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className={styles.section} style={{ width: "65%" }}>
              {!confirm ? (
                <div>
                  <div className={globalStyles.selectionName}>SELECT THE OBJECTS YOU WANT TO MIGRATE</div>
                  {selectedData.map((item, index) => (
                    <div className={styles.entityRow} key={`entity-row-${index}`}>
                      <div
                        key={index}
                        className={`${styles.entityBox} ${index === selectedObjectIndex ? styles.selectedEntityBox : ""}`}
                        onClick={() => handleObject(item, item.name, index)}
                      >
                        <div className={styles.entityContent}>
                          <img src="/assets/Tickets.jpg" alt="Ticket" className={styles.entityImage} />
                        </div>
                        <div className={styles.entityName}>{item.name}</div>
                      </div>
                      {item.dependent.length > 0 && index === selectedObjectIndex && (
                        <div className={styles.dependentContainer} key={`dep-container-${index}`}>
                          {item.dependent.map((dep, i) => (
                            <div
                              key={`dep-${i}`}
                              className={`${styles.entityBox} ${dependentFields.includes(dep) ? styles.selectedEntityBox : ""}`}
                              onClick={() => handleDependent(dep)}
                            >
                              <div className={styles.entityContent}>
                                <img
                                  src="/assets/Conversations.png"
                                  alt="Conversation"
                                  className={styles.entityImage}
                                />
                              </div>
                              <div className={styles.entityName}>{dep}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                  <button className={`${globalStyles.mainButton} ${styles.floatingButton}`} onClick={handleConfirm}>
                    Confirm
                  </button>
                </div>
              ) : (
                <div>
                  {selectedObject?.name === selectedObjectName ? (
                    <div>
                      <div className={styles.dFlex} style={{ justifyContent: "space-between", alignItems: "center" }}>
                        <span className={globalStyles.selectionName}>OBJECTS SELECTED FOR MIGRATION</span>
                        <HiXMark className={styles.closeIcon} onClick={closeSelectedObject} />
                      </div>
                      <div style={{ display: "flex", gap: "10px" }}>
                        <div className={styles.selectedEntityCard}>
                          <img src="/assets/Tickets.jpg" alt="Ticket" className={styles.selectedEntityImage} />
                          <div className={styles.selectedEntityName}>{selectedObjectName}</div>
                        </div>

                        {dependentFields.length > 0 &&
                          dependentFields.map((dep, index) => (
                            <div className={styles.selectedEntityCard} key={index}>
                              <img
                                src="/assets/Conversations.png"
                                alt="Conversation"
                                className={styles.selectedEntityImage}
                              />
                              <div className={styles.selectedEntityName}>{dep}</div>
                            </div>
                          ))}
                      </div>

                      <div className={styles.dFlex} style={{ marginTop: "30px" }}>
                        <div className={globalStyles.poppinsHeaderStyle}>
                          {source?.source?.name?.toLowerCase() === "csv"
                            ? "Enter batch size"
                            : "Enter total number of records"}
                        </div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginLeft: "auto",
                          }}
                          onClick={() => {
                            displayArticle("What are records?")
                          }}
                        >
                          <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                          <span className={globalStyles.guideName}>What are records?</span>
                        </div>
                      </div>
                      <div className="form-group">
                        <input
                          className="form-control"
                          placeholder={
                            source?.source?.name?.toLowerCase() === "csv" ? "Batch size*" : "Number of records*"
                          }
                          value={numberOfRecords}
                          onChange={handleNumberOfRecordsChange}
                        />
                      </div>
                      <div className={globalStyles.guideName}>
                        {source?.source?.name?.toLowerCase() === "csv"
                          ? ""
                          : "Enter your live migration count"}{" "}
                      </div>

                      {uploadedFiles.length > 0 && (
                        <div style={{ marginTop: "30px" }}>
                          <div className={globalStyles.poppinsHeaderStyle} style={{ marginBottom: "10px" }}>
                            Uploaded Files:
                          </div>

                          {/* Group files by entity */}
                          {(() => {
                            // Get unique entities from uploaded files
                            const entities = [...new Set(uploadedFiles.map(file => file.entity))];

                            return entities.map(entity => (
                              <div key={entity} style={{ marginBottom: "15px" }}>
                                <div style={{
                                  fontWeight: "500",
                                  marginBottom: "5px",
                                  color: "#333",
                                  display: "flex",
                                  alignItems: "center",
                                  fontFamily: "Inter",
                                  fontSize: "14px"
                                }}>
                                  <span style={{
                                    display: "inline-block",
                                    width: "8px",
                                    height: "8px",
                                    backgroundColor: "#EA5822",
                                    borderRadius: "50%",
                                    marginRight: "8px"
                                  }}></span>
                                  <span style={{ flex: 1 }}>{entity}</span>
                                </div>
                                <ul style={{ listStyleType: "none", paddingLeft: "16px", margin: "0" }}>
                                  {uploadedFiles
                                    .filter(file => file.entity === entity)
                                    .map((file, index) => {
                                      // Find the actual index in the overall uploadedFiles array
                                      const actualIndex = uploadedFiles.findIndex(f =>
                                        f.name === file.name && f.entity === entity
                                      );

                                      return (
                                        <li
                                          key={index}
                                          style={{
                                            marginBottom: "8px",
                                            color: "#514742",
                                            fontSize: "15px",
                                            fontFamily: "Inter, sans-serif",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "space-between",
                                            padding: "10px",
                                            backgroundColor: "#f9f9f9",
                                            borderRadius: "4px",
                                          }}
                                        >
                                          <div style={{ display: "flex", alignItems: "center" }}>
                                            <HiDocument style={{ width: "20px", height: "20px", marginRight: "10px" }} />
                                            {file.name || "File uploaded"}
                                            <span style={{
                                              fontSize: "12px",
                                              color: "#777",
                                              marginLeft: "10px"
                                            }}>
                                              {file.size}
                                            </span>
                                          </div>
                                          <HiTrash
                                            className={styles.deleteIcon}
                                            onClick={() => handleDeleteConfirmation(actualIndex, file.name)}
                                          />
                                        </li>
                                      );
                                    })}
                                </ul>
                              </div>
                            ));
                          })()}

                          {/* Show which entities still need files */}
                          {(() => {
                            const required = [selectedObjectName, ...dependentFields];
                            const uploadedEntityNames = [...new Set(uploadedFiles.map(file => file.entity))];
                            const missingEntities = required.filter(entity => !uploadedEntityNames.includes(entity));

                            if (missingEntities.length > 0) {
                              return (
                                <div style={{ marginTop: "15px" }}>
                                  <div style={{
                                    fontWeight: "500",
                                    color: "#f44336",
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    fontFamily: "Inter",
                                    color: "#EA5822"
                                  }}>
                                    <span style={{ fontFamily: "Inter", fontSize: "14px", fontFamily: "Inter" }}>Files still needed for:</span>
                                    {/* <button
                                      onClick={() => {
                                        const required = [selectedObjectName, ...dependentFields];
                                        const uploadedEntityNames = [...new Set(uploadedFiles.map(file => file.entity))];
                                        const missingEntities = required.filter(entity => !uploadedEntityNames.includes(entity));
                                        if (missingEntities.length > 0) {
                                          handleUploadForEntity(missingEntities[0]);
                                        }
                                      }}
                                      style={{
                                        backgroundColor: "#EA5822",
                                        color: "white",
                                        border: "none",
                                        borderRadius: "4px",
                                        padding: "4px 10px",
                                        fontSize: "12px",
                                        cursor: "pointer"
                                      }}
                                    >
                                      Upload Next File
                                    </button> */}
                                  </div>
                                  <ul style={{
                                    listStyleType: "none",
                                    paddingLeft: "16px",
                                    margin: "10px 0",
                                    fontFamily: "Inter"
                                  }}>
                                    {missingEntities.map((entity, index) => (
                                      <li key={index} style={{
                                        marginBottom: "5px",
                                        display: "flex",
                                        alignItems: "center",
                                        fontFamily: "Inter"
                                      }}>
                                        <span style={{
                                          display: "inline-block",
                                          width: "8px",
                                          height: "8px",
                                          backgroundColor: "#f44336",
                                          borderRadius: "50%",
                                          marginRight: "8px",
                                          fontFamily: "Inter"
                                        }}></span>
                                        <span style={{ flex: 1, fontFamily: "Inter" }}>{entity}</span>
                                        <button
                                          onClick={() => handleUploadForEntity(entity)}
                                          style={{
                                            backgroundColor: "#EA5822",
                                            color: "white",
                                            border: "none",
                                            borderRadius: "4px",
                                            padding: "2px 8px",
                                            fontSize: "12px",
                                            cursor: "pointer",
                                            marginLeft: "10px",
                                            fontFamily: "Inter"
                                          }}
                                        >
                                          Upload
                                        </button>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              );
                            }
                            return null;
                          })()}
                        </div>
                      )}

                      <button
                        className={globalStyles.mainButton}
                        style={{
                          width: "100%",
                          marginTop: "20px",
                          marginBottom: "50px",
                          opacity: source?.source?.name?.toLowerCase() === "csv" && !allFilesUploaded ? "0.7" : "1"
                        }}
                        disabled={source?.source?.name?.toLowerCase() === "csv" && !allFilesUploaded}
                        onClick={handleContinueClick}
                      >
                        {source?.source?.name?.toLowerCase() === "csv" && !allFilesUploaded
                          ? "Upload All Required Files First"
                          : "Confirm & Continue"}
                      </button>
                    </div>
                  ) : (

                    <div>

                      <div className={styles.dFlex} style={{ marginTop: "30px", justifyContent: "space-between", alignItems: "center" }}>
                        <span className={globalStyles.selectionName}>OBJECTS SELECTED FOR MIGRATION</span>
                        <div style={{ display: "flex", alignItems: "center", gap: "15px" }}>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              cursor: "pointer"
                            }}
                            onClick={() => {
                              displayArticle("What are objects?")
                            }}
                          >
                            <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                            <span className={globalStyles.guideName}>What are objects?</span>
                          </div>
                          <HiXMark className={styles.closeIcon} onClick={closeSelectedObject} />
                        </div>
                      </div>

                      <div style={{ display: "flex", gap: "10px" }}>
                        <div className={styles.selectedEntityCard}>
                          <img src="/assets/Tickets.jpg" alt="Ticket" className={styles.selectedEntityImage} />
                          <div className={styles.selectedEntityName}>{selectedObjectName}</div>
                        </div>

                        {dependentFields.length > 0 &&
                          dependentFields.map((dep, index) => (
                            <div className={styles.selectedEntityCard} key={index}>
                              <img
                                src="/assets/Conversations.png"
                                alt="Conversation"
                                className={styles.selectedEntityImage}
                              />
                              <div className={styles.selectedEntityName}>{dep}</div>
                            </div>
                          ))}
                      </div>

                      <div className={styles.dFlex} style={{ marginTop: "50px" }}>
                        <div className={globalStyles.poppinsHeaderStyle}>
                          {source?.source?.name?.toLowerCase() === "csv"
                            ? "Enter batch size"
                            : "Enter total number of records"}
                        </div>
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginLeft: "auto",
                          }}
                          onClick={() => {
                            displayArticle("What are records?")
                          }}
                        >
                          <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} />
                          <span className={globalStyles.guideName}>What are records?</span>
                        </div>
                      </div>
                      <div className="form-group">
                        <input
                          className="form-control"
                          placeholder={
                            source?.source?.name?.toLowerCase() === "csv" ? "Batch size*" : "Number of records*"
                          }
                          value={numberOfRecords}
                          onChange={handleNumberOfRecordsChange}
                        />
                      </div>
                      <div className={globalStyles.guideName}>
                        {source?.source?.name?.toLowerCase() === "csv" ? "" : ""}{" "}
                      </div>

                      {uploadedFiles.length > 0 && (
                        <div style={{ marginTop: "30px" }}>
                          <div className={globalStyles.poppinsHeaderStyle} style={{ marginBottom: "10px", fontSize: "16px" }}>
                            Uploaded Files:
                          </div>

                          {/* Group files by entity */}
                          {(() => {
                            // Get unique entities from uploaded files
                            const entities = [...new Set(uploadedFiles.map(file => file.entity))];

                            return entities.map(entity => (
                              <div key={entity} style={{ marginBottom: "15px" }}>
                                <div style={{
                                  fontWeight: "500",
                                  marginBottom: "5px",
                                  color: "#333",
                                  display: "flex",
                                  alignItems: "center",
                                  fontFamily: "Inter",
                                  fontSize: "14px"
                                }}>
                                  <span style={{
                                    display: "inline-block",
                                    width: "8px",
                                    height: "8px",
                                    backgroundColor: "#EA5822",
                                    borderRadius: "50%",
                                    marginRight: "8px",
                                    fontFamily: "Inter, sans-serif"
                                  }}></span>
                                  <span style={{ flex: 1 }}>{entity}</span>
                                  <button
                                    onClick={() => handleUploadForEntity(entity)}
                                    style={{
                                      backgroundColor: "#EA5822",
                                      color: "white",
                                      border: "none",
                                      borderRadius: "4px",
                                      padding: "2px 8px",
                                      fontSize: "12px",
                                      cursor: "pointer",
                                      marginLeft: "10px",
                                      fontFamily: "Inter, sans-serif"
                                    }}
                                  >
                                    Replace
                                  </button>
                                </div>
                                <ul style={{ listStyleType: "none", paddingLeft: "16px", margin: "0" }}>
                                  {uploadedFiles
                                    .filter(file => file.entity === entity)
                                    .map((file, index) => {
                                      // Find the actual index in the overall uploadedFiles array
                                      const actualIndex = uploadedFiles.findIndex(f =>
                                        f.name === file.name && f.entity === entity
                                      );

                                      return (
                                        <li
                                          key={index}
                                          style={{
                                            marginBottom: "8px",
                                            color: "#514742",
                                            fontSize: "15px",
                                            fontFamily: "Inter, sans-serif",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "space-between",
                                            padding: "10px",
                                            backgroundColor: "#f9f9f9",
                                            borderRadius: "4px",
                                          }}
                                        >
                                          <div style={{ display: "flex", alignItems: "center" }}>
                                            <HiDocument style={{ width: "20px", height: "20px", marginRight: "10px" }} />
                                            {file.name || "File uploaded"}
                                            <span style={{
                                              fontSize: "12px",
                                              color: "#777",
                                              marginLeft: "10px"
                                            }}>
                                              {file.size}
                                            </span>
                                          </div>
                                          <HiTrash
                                            className={styles.deleteIcon}
                                            onClick={() => handleDeleteConfirmation(actualIndex, file.name)}
                                          />
                                        </li>
                                      );
                                    })}
                                </ul>
                              </div>
                            ));
                          })()}

                          {/* Show which entities still need files */}
                          {(() => {
                            const required = [selectedObjectName, ...dependentFields];
                            const uploadedEntityNames = [...new Set(uploadedFiles.map(file => file.entity))];
                            const missingEntities = required.filter(entity => !uploadedEntityNames.includes(entity));

                            if (missingEntities.length > 0) {
                              return (
                                <div style={{ marginTop: "15px" }}>
                                  <div style={{
                                    fontWeight: "500",
                                    color: "#f44336",
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    fontFamily: "Inter"
                                  }}>
                                    <span>Files still needed for:</span>
                                  </div>
                                  <ul style={{
                                    listStyleType: "none",
                                    paddingLeft: "16px",
                                    margin: "10px 0",
                                    fontFamily: "Inter"
                                  }}>
                                    {missingEntities.map((entity, index) => (
                                      <li key={index} style={{
                                        marginBottom: "5px",
                                        display: "flex",
                                        alignItems: "center",
                                        fontFamily: "Inter"
                                      }}>
                                        <span style={{
                                          display: "inline-block",
                                          width: "8px",
                                          height: "8px",
                                          backgroundColor: "#f44336",
                                          borderRadius: "50%",
                                          marginRight: "8px",
                                          fontFamily: "Inter"
                                        }}></span>
                                        <span style={{ flex: 1 }}>{entity}</span>
                                        <button
                                          onClick={() => handleUploadForEntity(entity)}
                                          style={{
                                            backgroundColor: "#f44336",
                                            color: "white",
                                            border: "none",
                                            borderRadius: "4px",
                                            padding: "2px 8px",
                                            fontSize: "12px",
                                            cursor: "pointer",
                                            marginLeft: "10px",
                                            fontFamily: "Inter"
                                          }}
                                        >
                                          Upload
                                        </button>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              );
                            }
                            return null;
                          })()}
                        </div>
                      )}

                      <button
                        className={globalStyles.mainButton}
                        style={{
                          width: "100%",
                          marginTop: "20px",
                          marginBottom: "50px",
                          opacity: source?.source?.name?.toLowerCase() === "csv" && !allFilesUploaded ? "0.7" : "1"
                        }}
                        disabled={source?.source?.name?.toLowerCase() === "csv" && !allFilesUploaded}
                        onClick={handleContinueClick}
                      >
                        {source?.source?.name?.toLowerCase() === "csv" && !allFilesUploaded
                          ? "Upload All Required Files First"
                          : "Confirm & Continue"}
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {showDeleteConfirmation && (
        <div className={styles.modalOverlay}>
          <div className={styles.modalContent}>
            <div className={styles.modalBody}>
              <p style={{ fontFamily: "Inter, sans-serif" }}>Are you sure you want to remove this file?</p>
            </div>
            <div className={styles.modalFooter}>
              <button className={styles.okButton} onClick={handleDeleteFile}>
                OK
              </button>
              <button className={styles.cancelButton} onClick={() => setShowDeleteConfirmation(false)}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {showBackConfirmation && (
        <div className={styles.modalOverlay}>
          <div className={styles.modalContent}>
            <div className={styles.modalBody}>
              <p style={{ fontFamily: "Inter" }}>Are you sure you want to go back to object selection?</p>
              <p style={{ fontSize: "14px", color: "#666", fontFamily: "Inter" }}>Your uploaded files will be preserved, but you'll need to reselect objects.</p>
            </div>
            <div className={styles.modalFooter}>
              <button className={styles.okButton} onClick={confirmGoBack}>
                Go Back
              </button>
              <button className={styles.cancelButton} onClick={cancelGoBack}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
        toastStyle={{ backgroundColor: "#000", color: "#fff" }}
      />
    </div>
  )
}
