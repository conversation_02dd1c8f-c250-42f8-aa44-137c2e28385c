import { useState, useEffect } from "react"
import styles from "./uploadcsv.module.css"
import globalStyles from "../../globalStyles.module.css"
import { HiXMark, HiInformationCircle, HiDocument } from "react-icons/hi2"
import { <PERSON><PERSON><PERSON> } from "buffer";
import { uploadToAzureBlob } from "../../apiService";

// Ensure Buffer is available globally
if (!window.Buffer) {
  window.Buffer = Buffer;
}

const parseCSVHeaders = (file) => {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject("No file provided")
      return
    }

    const reader = new FileReader()

    reader.onload = (event) => {
      try {
        const content = event.target.result;
        const lines = content.split("\n").map(line => line.trim()).filter(line => line !== "");
        const firstLine = content.split("\n")[0]
        const headers = firstLine.split(",").map((header) => header.trim());
        const numberOfLines = lines.length - 1;
        resolve({ headers, numberOfLines })
      } catch (error) {
        reject(`Error parsing CSV headers: ${error.message}`)
      }
    }

    reader.onerror = (error) => {
      reject(`Error reading file: ${error}`)
    }

    reader.readAsText(file)
  })
}

// const uploadToAzureBlob = async (file) => {
//   if (!file) throw new Error("No file provided");

//   // Replace connection string with SAS URL
//   const SAS_URL = "https://saasgeniestorage.blob.core.windows.net/?sv=2024-11-04&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2025-12-31T13:47:32Z&st=2025-06-02T05:47:32Z&spr=https,http&sig=3lxpy88Cx8o0JfTZDtYk54ESpIZElAmlCTmeZKkwh6s%3D";

//   const blobServiceClient = new BlobServiceClient(SAS_URL);
//   const containerClient = blobServiceClient.getContainerClient("migrategenie-v2-storage");

//   const blobName = `${Date.now()}-${file.name}`;
//   const blockBlobClient = containerClient.getBlockBlobClient(blobName);

//   const uploadBlobResponse = await blockBlobClient.uploadBrowserData(file);
//   console.log(`Upload successfully`, uploadBlobResponse);

//   return {
//     azureBlobURL: blockBlobClient.url,
//     requestId: uploadBlobResponse.requestId,
//   };
// };

const UploadCSV = ({ onFileUploaded, onClose, entityName = "CSV", isLastEntity = true, previouslyUploadedFiles = [] }) => {
  const [file, setFile] = useState(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadStatus, setUploadStatus] = useState("idle")
  const [isUploading, setIsUploading] = useState(false)
  const [uploadError, setUploadError] = useState(null)
  const [successMessage, setSuccessMessage] = useState(null)

  // Sample CSV file URLs for different entities
  const sampleCSVUrls = {
    "Tickets": "https://saasgeniestorage.blob.core.windows.net/migrategenie-v2-storage/Sample%20CSVs/Tickets%20Sample.csv",
    "Conversations": "https://saasgeniestorage.blob.core.windows.net/migrategenie-v2-storage/Sample%20CSVs/Conversations%20Sample.csv",
  }

  // Show progress animation when uploading
  useEffect(() => {
    if (uploadStatus === "uploading") {
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 100) {
            clearInterval(interval)
            setUploadStatus("uploaded")
            return 100
          }
          return prev + 10
        })
      }, 300)
      return () => clearInterval(interval)
    }
  }, [uploadStatus])

  // Reset error when file changes
  useEffect(() => {
    if (file) {
      setUploadError(null)
    }
  }, [file]);

  // Function to handle downloading sample CSV files
  const handleDownloadSampleCSV = (entity) => {
    try {
      // Get the URL for the entity, or use a default message if not found
      const sampleUrl = sampleCSVUrls[entity];

      if (sampleUrl) {
        // Create a temporary link element
        const link = document.createElement('a');
        link.href = sampleUrl;
        link.target = '_blank';
        link.download = `${entity} Sample.csv`;

        // Append to the document, click it, and remove it
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clear any existing errors and show a temporary success message
        setUploadError(null);
        setSuccessMessage(`Sample CSV for ${entity} is being downloaded`);

        // Clear success message after 3 seconds
        setTimeout(() => setSuccessMessage(null), 3000);
      } else {
        // If no sample CSV is available for this entity, show an error
        setUploadError(`No sample CSV available for ${entity}. Please contact support.`);
        setTimeout(() => setUploadError(null), 5000); // Clear error after 5 seconds
      }
    } catch (error) {
      console.error("Error downloading sample CSV:", error);
      setUploadError(`Failed to download sample CSV. ${error.message}`);
      setTimeout(() => setUploadError(null), 5000); // Clear error after 5 seconds
    }
  };

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0]
    if (selectedFile) {
      // Validate file type
      if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
        setUploadError("Please select a CSV file")
        return
      }

      // Validate file size (50MB max)
      if (selectedFile.size > 50 * 1024 * 1024) {
        setUploadError("File size exceeds 50MB limit")
        return
      }

      setFile(selectedFile)
      setUploadProgress(0)
      setUploadStatus("uploading")
    }
  }

  const handleDrop = (event) => {
    event.preventDefault()
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      // Validate file type
      if (!droppedFile.name.toLowerCase().endsWith('.csv')) {
        setUploadError("Please select a CSV file")
        return
      }

      // Validate file size (50MB max)
      if (droppedFile.size > 50 * 1024 * 1024) {
        setUploadError("File size exceeds 50MB limit")
        return
      }

      setFile(droppedFile)
      setUploadProgress(0)
      setUploadStatus("uploading")
    }
  }

  const handleConfirm = async () => {
    if (!file) {
      setUploadError("Please select a file to upload")
      return
    }

    setIsUploading(true)
    setUploadError(null)

    try {
      const { headers, numberOfLines } = await parseCSVHeaders(file);

      // Validate that the file has content
      if (numberOfLines <= 0) {
        throw new Error("The CSV file appears to be empty")
      }

      const response = await uploadToAzureBlob(file);

      if (response) {
        const fileData = {
          name: file.name,
          type: file.type,
          size: `${Math.round(file.size / 1024)}KB`,
          file: file,
          azureBlobUrl: response.azureBlobURL,
          headers: headers,
          length: numberOfLines,
          uploadedAt: new Date().toISOString(),
        };

        setUploadStatus("uploaded")
        setUploadProgress(100)

        if (onFileUploaded) {
          onFileUploaded(fileData)
        }
      }
    } catch (error) {
      console.error("Error processing file:", error)
      setUploadStatus("error")
      setUploadError(error.message || "Failed to upload file. Please try again.")
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className={styles.modal}>
      <div className={styles.container}>
        <button className={styles.closeButton} onClick={onClose}>
          <HiXMark className={globalStyles.closeIcon} />
        </button>
        <h2 className={styles.title}>
          Upload <span style={{ fontWeight: "bold" }}>{entityName}</span> file
        </h2>

        {previouslyUploadedFiles && previouslyUploadedFiles.length > 0 && (
          <div style={{
            backgroundColor: "rgba(76, 175, 80, 0.1)",
            padding: "10px",
            borderRadius: "4px",
            marginBottom: "15px",
            border: "1px solid #4CAF50"
          }}>
            <p style={{
              margin: "0 0 5px 0",
              color: "#4CAF50",
              fontWeight: "400",
              fontFamily: "Inter"
            }}>
              Previously uploaded file(s) for {entityName}:
            </p>
            <ul style={{
              listStyleType: "none",
              margin: "0",
              padding: "0"
            }}>
              {previouslyUploadedFiles.map((file, index) => (
                <li key={index} style={{
                  fontSize: "14px",
                  marginBottom: "3px",
                  display: "flex",
                  alignItems: "center",
                  fontFamily: "Inter"
                }}>
                  <HiDocument style={{ marginRight: "5px" }} />
                  {file.name} ({file.size})
                </li>
              ))}
            </ul>
            <p style={{
              margin: "5px 0 0 0",
              fontSize: "13px",
              fontStyle: "bold",
              fontFamily: "Inter"
            }}>
              Uploading a new file will replace the existing one.
            </p>
          </div>
        )}

        <p className={styles.subtitle}>
          Ensure your file follows the required format for seamless integration and automated mapping.
        </p>
        <div className={styles.buttonRow}>
          <label className={styles.browseButton}>
            <img src="/assets/file-search.png" alt="File Search Icon" className={styles.browseIcon} />
            <span style={{ fontFamily: "Inter" }}>Browse file</span>
            <input type="file" accept=".csv" onChange={handleFileChange} style={{ display: "none" }} />
          </label>
          <button
            className={styles.downloadButton}
            onClick={() => handleDownloadSampleCSV(entityName)}
          >
            Download sample CSV
          </button>
        </div>
        <div
          className={`${styles.dropArea} ${uploadError ? styles.dropAreaError : ''}`}
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
        >
          {!file && (
            <div className={styles.uploadContainer}>
              <img
                src="/assets/file-upload.png"
                alt="Upload Icon"
                className={styles.uploadIcon}
                onClick={() => document.querySelector("input[type=file]").click()}
              />
              <p className={styles.dragText}>Drag and drop your file</p>
            </div>
          )}
          <p className={styles.fileFormat}>
            Accepted format: <strong>your-file.csv</strong> | Max file size: <strong>50MB</strong>
          </p>
          {file && (
            <div className={styles.progressContainer}>
              <div className={styles.progressBar}>
                <div
                  className={`${styles.progress} ${uploadStatus === "error" ? styles.progressError : ""}`}
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className={styles.uploadingText}>
                {uploadStatus === "uploading" ? "Processing..." :
                  uploadStatus === "error" ? "Error processing file" : "Processed"}
              </p>
            </div>
          )}

          {/* Display error message if there is one */}
          {uploadError && (
            <div className={styles.errorMessage}>
              <HiInformationCircle style={{ marginRight: "5px" }} />
              {uploadError}
            </div>
          )}

          {/* Display success message if there is one */}
          {successMessage && (
            <div className={styles.successMessage}>
              <HiInformationCircle style={{ marginRight: "5px", color: "#4CAF50" }} />
              {successMessage}
            </div>
          )}
        </div>

        <div className={styles.buttonContainer}>
          <button
            style={{ width: "100%" }}
            className={globalStyles.mainButton}
            disabled={!file || isUploading || uploadStatus === "error"}
            onClick={handleConfirm}
          >
            {isUploading ? "Uploading..." : isLastEntity ? "Confirm" : "Next"}
          </button>
        </div>
      </div>
    </div>
  )
}

export default UploadCSV