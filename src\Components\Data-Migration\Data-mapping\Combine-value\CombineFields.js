import React, {useEffect, useState, useRef} from "react";
import styles from "./CombineFields.module.css";
import globalStyles from "../../../globalStyles.module.css"
import { HiXMark } from "react-icons/hi2";
import { PlusIcon } from "@heroicons/react/solid";
import { ChevronDownIcon } from "@heroicons/react/solid";

export default function CombineFields({
  close,
  attribute,
  target,
  onSave,
  sourceFields
}) {
  const [selectedFields, setSelectedFields] = useState([]);
  const [currentSelection, setCurrentSelection] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  const handleAddField = () => {
    if (currentSelection && !selectedFields.includes(currentSelection)) {
      setSelectedFields([...selectedFields, currentSelection]);
      setCurrentSelection("");
    }
  };

  const handleRemoveField = (field) => {
    setSelectedFields(selectedFields.filter(f => f !== field));
  };

  const handleSave = () => {
    onSave(selectedFields);
    close();
  };
    useEffect(() => {
        if (attribute && attribute?.combinedFields?.length > 0) {
            setSelectedFields(attribute.combinedFields);
        }
    }, [attribute]);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className={styles.combineFieldsContainer}>
      <div className={styles.header}>
        <div className={styles.titleContainer}>
          <h2 className={styles.title}>Combine Fields</h2>
          <HiXMark className={styles.closeIcon} onClick={close} />
        </div>
        <div className={styles.mappingGuide}>
          <span className={styles.guideLabel}>MAPPING GUIDE</span>
          <span className={styles.guideText}>Combine multiple source fields to have one target data</span>
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.formRow}>
          <div className={styles.label}>Data type</div>
          <div className={styles.value}>{target?.name || "Tickets"}</div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.label}>Select source multiple fields</div>
          <div className={styles.fieldSelectionContainer}>
            <div className={styles.selectWithButton}>
              <div className={styles.customDropdown} ref={dropdownRef}>
                <button
                  type="button"
                  className={styles.dropdownButton}
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                >
                  <span className={globalStyles.interStyle}>
                    {currentSelection || "Select Source field"}
                  </span>
                  <ChevronDownIcon width={16} height={16} />
                </button>

                {isDropdownOpen && (
                  <div className={styles.dropdownOptions}>
                    {sourceFields?.filter(field => !selectedFields.includes(field)).map((field, index) => (
                      <div
                        key={index}
                        className={styles.dropdownOption}
                        onClick={() => {
                          setCurrentSelection(field);
                          setIsDropdownOpen(false);
                        }}
                      >
                        {field}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <button
                className={styles.addButton}
                onClick={handleAddField}
                disabled={!currentSelection}
              >
                <img
                  src="/assets/add.png"
                  alt="Add"
                  className={styles.plusIcon}
                />
              </button>
            </div>
          </div>
        </div>

        {selectedFields.length > 0 && (
          <div className={styles.selectedFieldsContainer}>
            {selectedFields.map((field, index) => (
              <div key={index} className={`${styles.selectedField} ${globalStyles.interStyle}`}>
                <span>{field}</span>
                <HiXMark
                  className={styles.removeFieldIcon}
                  onClick={() => handleRemoveField(field)}
                />
              </div>
            ))}
          </div>
        )}

        <div className={styles.buttonContainer}>
            <button
                className={styles.combineButton}
                onClick={handleSave}

            >
            <img
                src="/assets/check list.png"
                alt="Checklist Icon"
                className={styles.buttonIcon}
            />
            Combine Source fields
            </button>
        </div>
      </div>
    </div>
  );
}
