import React, { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { HiXMark } from "react-icons/hi2";
import globalStyles from "../../../globalStyles.module.css"
import styles from "./MigrationScheduler.module.css"
import {HiChevronRight} from "react-icons/hi";
import {FormControl, MenuItem, Select} from "@mui/material";

const MigrationScheduler = ({setMigrationDate, onClose}) => {
    const defaultDate = new Date();
    defaultDate.setHours(10, 0, 0, 0);

    const [selectedDate, setSelectedDate] = useState(defaultDate);
    const [selectedTimeZone, setSelectedTimeZone] = useState("EST");
    const [selectedTime, setSelectedTime] = useState("10:00");
    const [selectedPeriod, setSelectedPeriod] = useState("AM");

    const minDate = new Date();
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 14);

    const formatDate = (date) => {
        return `${date.getDate().toString().padStart(2, '0')}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getFullYear()}`;
    };

    const CustomHeader = ({date,
                              changeYear,
                              changeMonth,
                              decreaseMonth,
                              increaseMonth,
                              prevMonthButtonDisabled,
                              nextMonthButtonDisabled,
                          }) => {
        const currentYear = new Date().getFullYear();
        const years = Array.from({ length: 2 }, (_, i) => currentYear + i);

        const months = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ];

        return (
            <div className={styles.headerStyle}>
                <div className={styles.dFlex} style={{justifyContent: "space-between", width: "100%"}}>

                    <FormControl>
                        <Select
                            value={date.getFullYear()}
                            onChange={(e) => changeYear(parseInt(e.target.value))}
                            displayEmpty
                            inputProps={{
                                sx: {
                                    padding: "0 32px 0 0 !important",
                                    color: '#E37B52',
                                },
                            }}
                            sx={{
                                backgroundColor: 'transparent',
                                color: 'white',
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                            }}
                        >
                            <MenuItem value="" disabled>
                                Year
                            </MenuItem>
                            {years.map((year) => (
                                <MenuItem
                                    key={year}
                                    value={year}
                                    // sx={{ backgroundColor: '#333333', color: '#E37B52' }}
                                >
                                    {year}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>


                    <div>
                        <FormControl >
                            <Select
                                value={months[date.getMonth()]}
                                onChange={({ target: { value } }) => {
                                    const monthIndex = months.indexOf(value);
                                    changeMonth(monthIndex);
                                }}
                                displayEmpty
                                inputProps={{
                                    sx: {
                                        padding: "0 32px 0 0 !important",
                                        color: '#E37B52',
                                        textTransform: 'uppercase',
                                    },
                                }}
                                sx={{
                                    backgroundColor: 'transparent',
                                    color: '#E37B52',
                                    textTransform: 'uppercase',
                                    '& .MuiOutlinedInput-notchedOutline': {
                                        borderColor: '#514742',
                                    },
                                    '&:hover .MuiOutlinedInput-notchedOutline': {
                                        borderColor: '#514742',
                                    },
                                }}
                            >
                                <MenuItem value="" disabled>
                                    Month
                                </MenuItem>
                                {months.map((month) => (
                                    <MenuItem
                                        key={month}
                                        value={month}
                                        // sx={{ backgroundColor: '#333333', color: '#E37B52', textTransform: 'uppercase' }}
                                    >
                                        {month}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>

                    </div>
                    <button
                        onClick={increaseMonth}
                        disabled={nextMonthButtonDisabled}
                        className={styles.headerSelectStyle}
                        style={{color: "#E37B52"}}
                    >
                        <HiChevronRight style={{color: "#E37B52"}}/>
                    </button>
                </div>
            </div>
        );
    };

    return (
        <div className={styles.container}>
            <style>
                {`
          .react-datepicker {
            background-color: #170903 !important;
            color: white !important;
            border: none !important;
            font-family: inter !important;
            width: 100% !important;
          }
          .react-datepicker__month-container {
            background-color: #170903 !important;
            width: 100% !important;
          }
          .react-datepicker__header {
            background-color: #170903 !important;
            border-bottom: none !important;
            width: 100% !important;
          }
          .react-datepicker__day-names {
            display: flex !important;
            justify-content: space-between !important;
            width: 100% !important;
            padding: 0 10px !important;
          }
          .react-datepicker__day-name {
            color: white !important;
            margin: 0 !important;
            width: 36px !important;
          }
          .react-datepicker__month {
            margin: 0 !important;
            width: 100% !important;
          }
          .react-datepicker__week {
            display: flex !important;
            justify-content: space-between !important;
            width: 100% !important;
            padding: 0 10px !important;
          }
          .react-datepicker__day {
            color: #AAAAAA !important;
            margin: 0 !important;
            width: 36px !important;
            height: 36px !important;
            line-height: 36px !important;
            display: inline-flex !important;
            justify-content: center !important;
            align-items: center !important;
          }
          .react-datepicker__day:hover {
            background-color: #333333 !important;
           
          }
          .react-datepicker__day--selected {
            background-color: #E37B52 !important;
            color: #1A1A1A !important;
            font-weight: bold !important;
          }
          .react-datepicker__day--keyboard-selected {
            background-color: #E37B52 !important;
            color: #1A1A1A !important;
            
          }
          .react-datepicker__day--disabled {
            color: #444444 !important;
            cursor: not-allowed !important;
          }
          .react-datepicker__navigation--previous, .react-datepicker__navigation--next {
            display: none !important;
          }
          .react-datepicker__triangle {
            display: none !important;
          }
          .react-datepicker-popper {
            background-color: #1A1A1A !important;
            width: 100% !important;
          }
        `}
            </style>
            <button className={styles.closeButton}>
                <HiXMark onClick={onClose} style={{cursor: "pointer"}}/>
            </button>
            <div className={globalStyles.headerStyle} style={{padding: "0", color: "#E37B52"}}>Schedule Your Live Migration</div>

            <div className={styles.note}>Choose a slot between now and the next 2 weeks</div>

            <div style={{marginBottom: "20px", width: "100%"}}>
                <DatePicker
                    selected={selectedDate}
                    onChange={date => setSelectedDate(date)}
                    inline
                    renderCustomHeader={CustomHeader}
                    calendarClassName="dark-calendar"
                    fixedHeight
                    minDate={minDate}
                    maxDate={maxDate}
                    dayClassName={() => "custom-day"}
                    monthClassName={() => "custom-month"}
                    weekDayClassName={() => "custom-weekday"}
                    className="full-width-datepicker"
                />
            </div>

            <div className={styles.note2}>Select a time slot</div>

            <div className={styles.dFlex} style={{marginTop: "20px", gap: "10px"}}>
                <div className={styles.dropdownContainer} style={{flex: 1}}>
                    <FormControl fullWidth >
                        <Select
                            value={selectedTimeZone}
                            onChange={(e) => {
                                setSelectedTimeZone(e.target.value);

                            }}
                            displayEmpty
                            inputProps={{
                                sx: {
                                    padding: '10px !important',
                                    color: 'white',
                                },
                            }}
                            sx={{
                                backgroundColor: 'transparent',
                                color: 'white',
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                            }}
                        >
                            <MenuItem value="" disabled>
                                Zone
                            </MenuItem>
                            <MenuItem value="PST">PST (UTC-8)</MenuItem>
                            <MenuItem value="EST">EST (UTC-5)</MenuItem>
                            <MenuItem value="GMT">GMT (UTC ±0)</MenuItem>
                            <MenuItem value="CET">CET (UTC +1)</MenuItem>
                            <MenuItem value="GST">GST (UTC +4)</MenuItem>
                            <MenuItem value="IST">IST (UTC +5:30)</MenuItem>
                            <MenuItem value="SGT">SGT (UTC +8)</MenuItem>
                            <MenuItem value="JST">JST (UTC +9)</MenuItem>
                            <MenuItem value="AEST">AEST (UTC +11)</MenuItem>
                        </Select>
                    </FormControl>

                </div>

                <div className={styles.dropdownContainer1} style={{flex: 2}}>
                    <FormControl fullWidth>
                        <Select
                            value={selectedTime}
                            onChange={(e) => {
                                setSelectedTime(e.target.value);

                            }}
                            displayEmpty
                            inputProps={{
                                sx: {
                                    padding: '10px !important',
                                    color: 'white',
                                },
                            }}
                            sx={{
                                backgroundColor: 'transparent',
                                color: 'white',
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                            }}
                        >
                            <MenuItem value="" disabled>
                                Select time
                            </MenuItem>
                            {Array.from({ length: 12 }, (_, i) => (
                                <MenuItem key={i + 1} value={`${i + 1}:00`}>
                                    {i + 1}:00
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                </div>

                <div className={styles.dropdownContainer} style={{flex: 1}}>
                    <FormControl fullWidth>
                        <Select
                            value={selectedPeriod}
                            onChange={(e) => {
                                setSelectedPeriod(e.target.value);

                            }}
                            displayEmpty
                            inputProps={{
                                sx: {
                                    padding: '10px !important',
                                    color: 'white',
                                },
                            }}
                            sx={{
                                backgroundColor: 'transparent',
                                color: 'white',
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: '#514742',
                                },
                            }}
                        >
                            <MenuItem value="" disabled>
                                AM/PM
                            </MenuItem>
                            <MenuItem value="AM">AM</MenuItem>
                            <MenuItem value="PM">PM</MenuItem>
                        </Select>
                    </FormControl>

                </div>
            </div>

            <div className={styles.migrationMessage} style={{backgroundColor: "#333333", padding: "15px", borderRadius: "5px", marginTop: "20px"}}>
                <span>Your migration is scheduled for </span>
                <span className={styles.migrationTime} style={{color: "#E37B52"}}>
                    {formatDate(selectedDate)} {selectedTime} {selectedPeriod}, {selectedTimeZone}
                </span>
            </div>

            <button className={globalStyles.mainButton} style={{width: "100%"}}
                    onClick={() => {
                        setMigrationDate(`${formatDate(selectedDate)} ${selectedTime} ${selectedPeriod}, ${selectedTimeZone}`);
                    onClose()}
                    }>
                Confirm date & time
            </button>
        </div>
    );
};

export default MigrationScheduler;
