import React, {useEffect, useState} from 'react';
import styles from "./Pay.module.css";
import globalStyles from "../../../globalStyles.module.css";
import {Dialog, DialogContent, FormControl, MenuItem, Select} from "@mui/material";
import {HiCalendarDays, HiMapPin, HiPercentBadge, HiXMark} from "react-icons/hi2";
import MigrationScheduler from "../MigrationScheduler/MigrationScheduler";
import {useNavigate} from "react-router-dom";

const slabPricing = [
    { limit: 1000, pro: 900, essential: 750 },
    { limit: 5000, pro: 1500, essential: 1250 },
    { limit: 10000, pro: 2100, essential: 1750 },
    { limit: 30000, pro: 3300, essential: 2750 },
    { limit: 50000, pro: 4200, essential: 3500 },
    { limit: 100000, pro: 5700, essential: 4750 },
    { limit: 250000, pro: 7800, essential: 6500 },
    { limit: 500000, pro: 10200, essential: 8500 },
    { limit: 1000000, pro: 14100, essential: 11750 },
    { limit: 2500000, pro: 22500, essential: 18750 },
    { limit: 5000000, pro: 30000, essential: 25000 },
    { limit: 20000000, pro: 63000, essential: 52500 },
];

const formatCurrency = (amount) => {
    return parseFloat(amount).toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
};

const Pay = () => {
    const [selectedPlan, setSelectedPlan] = useState("");
    const [currency, setCurrency] = useState("USD");
    const [showDialog, setShowDialog] = useState(false);
    const [showWhiteGloveDialog, setShowWHiteGloveDialog] = useState(false);
    const currencySymbol = '$';
    const navigate = useNavigate();


    const convertPrice = (price, currencyCode) => {
        const rate = exchangeRates[currencyCode] || 1;
        return (price * rate).toFixed(2);
    };

    useEffect(() => {
        calPrice();
    }, [selectedPlan, currency]);

    const numberToWords = (num) => {
        const a = ["", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten",
            "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"];
        const b = ["", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"];

        if (num === 0) return "Zero";
        if (num < 20) return a[num];
        if (num < 100) return b[Math.floor(num / 10)] + (num % 10 ? " " + a[num % 10] : "");
        if (num < 1000) return a[Math.floor(num / 100)] + " Hundred " + (num % 100 !== 0 ? numberToWords(num % 100) : "");
        if (num < 1000000) return numberToWords(Math.floor(num / 1000)) + " Thousand " + (num % 1000 !== 0 ? numberToWords(num % 1000) : "");

        return num;
    };




    const exchangeRates = {
        USD: 1,
        EUR: 0.96,
        GBP: 0.79,
    };

    const currencySymbols = {
        USD: "$",
        EUR: "€",
        GBP: "£"
    };

    const availPlans = {
        essential: {
            totalRecord: 500,
            totalPrice: 145,
            taxCharges: 159,
        },
        pro: {
            totalRecord: 500,
            totalPrice: 300,
            taxCharges: 330,
        }
    };

    const plans = {
        essential: {
            name: 'Essential Migration',
            features: {
                'Migration Volume': '10,000 records',
                'Support weekdays': '8 hours X 5 days (US EST)',
                'Support weekends': 'Unavailable',
                'Support method': 'Email only',
                'Assigned migration engineer': 'Unavailable',
                'Sample migrations': 'One sample migration per template',
                'Templates': 'Up to 2 templates',
                'Advanced data filtering': 'Unavailable',
                'Custom mapping': 'Unavailable',
                'Reports': 'Basic Summaries'
            },
            pricePerRecord: 0.5
        },
        pro: {
            name: 'Pro Migration',
            features: {
                'Migration Volume': '1,000,000 records',
                'Support weekdays': '8 AM to 8 PM (US EST)',
                'Support weekends': '9 AM to 5 PM (US EST)',
                'Support method': 'Email and Chat',
                'Assigned migration engineer': 'Available',
                'Sample migrations': 'Three sample migrations per template',
                'Templates': 'Up to 5 templates',
                'Advanced data filtering': 'Available',
                'Custom mapping': 'Available',
                'Reports': 'Detailed Analytics'
            },
            pricePerRecord: 0.6
        },
    };

    const [totalRecords, setTotalRecords] = useState(500);
    const [basePrice, setBasePrice] = useState(0);
    const [totalPrice, setTotalPrice] = useState(0);


const calPrice = () => {
    if (!selectedPlan) return;
    const validRecords = Math.max(100, Math.min(1000000, totalRecords));
    const slab = slabPricing.find(s => validRecords <= s.limit);
    if (!slab) return;
    const currencyCode = currency.split(' ')[0];
    const rawPrice = slab[selectedPlan];
    const convertedPrice = convertPrice(rawPrice, currencyCode);
    const finalPrice = (parseFloat(convertedPrice) * 1.0).toFixed(2);
    setBasePrice(convertedPrice);
    setTotalPrice(finalPrice);
};

    const ConfirmPayment = ({close}) => {
        return (
            <div className={styles.confirmDialog}>
                <div style={{display: "flex", justifyContent: "flex-end"}}>
                    <HiXMark className={globalStyles.closeIcon} style={{color: "#F8F8F7", cursor: "pointer"}} onClick={close}/>
                </div>
                <div className={globalStyles.selectionName}>CONFIRMATION</div>
                <div className={globalStyles.interSummaryStyle} style={{color: "#F8F8F7", marginTop: "20px", lineHeight: "1.9rem"}}>I understand that once the payment is made, data mapping & transformation and migration plan & settings will be locked and no further changes can be made.
                </div>
                <button className={globalStyles.mainButton} style={{width: "100%", marginTop: "30px"}} onClick={(event) =>
                {
                    navigate('/data-migration', {
                        state: {
                            paymentSuccess: true,
                            tab: "5",
                            forceStep5: true
                        }
                    });
                    close();
                }}
                >I Understand & Proceed</button>
            </div>

        );
    }

    const WhiteGlove = ({close}) => {
        const timeZones = [
            { code: "PST", name: "UTC-8" },
            { code: "EST", name: "UTC-5" },
            { code: "GMT", name: "UTC+0" },
            { code: "CET", name: "UTC+1" },
            { code: "GST", name: "UTC+4" },
            { code: "IST", name: "UTC+5:30" },
            { code: "SGT", name: "UTC+8" },
            { code: "JST", name: "UTC+9" },
            { code: "AEST", name: "UTC+11" }
        ];
        const countryCodes = [
            { code: "+91", name: "India" },
            { code: "+1", name: "United States" },
            { code: "+44", name: "United Kingdom" },
            { code: "+61", name: "Australia" },
            { code: "+1", name: "Canada" },
        ];
        const timeSlot = [
            {slot: "8AM - 11AM"},
            {slot: "11AM - 1PM"},
            {slot: "1PM - 4PM"},
            {slot: "4PM - 8PM"},
        ]
        const details = {
            source: "Zendesk",
            target: "Freshservice",
            records: "500",
        }
        return (
            <div className={styles.confirmDialog} style={{height: "520px"}}>
                <div style={{display: "flex", justifyContent: "flex-end"}}>
                    <HiXMark className={globalStyles.closeIcon} style={{color: "#F8F8F7", cursor: "pointer"}}
                             onClick={close}/>
                </div>
                <div className={globalStyles.selectionName}>THANK YOU FOR CHOOSING WHITE-GLOVE MIGRATION!</div>
                <div className={globalStyles.interSummaryStyle}
                     style={{color: "#F8F8F7", marginTop: "5px"}}>We need a few more details and our migration team will
                    contact you soon.
                </div>
                <div className={globalStyles.poppinsHeaderStyle} style={{color: "#97908E", marginTop: "20px"}}>Your
                    contact details
                </div>
                <div style={{display: "flex", gap: "10px"}}>
                    <FormControl
                        className={globalStyles.customDropdownContainer}
                        style={{width: "25%"}}>
                        <Select displayEmpty
                                className={globalStyles.customDropdownSelect}
                                renderValue={(selected) => (
                                    <span className={globalStyles.guideName}
                                          style={{fontSize: "14px"}}>
                                                            {selected || "Select Code"}
                                                          </span>
                                )}>
                            {countryCodes.map((country) => (
                                <MenuItem key={country.code}
                                          value={country.code}>
                                    {country.code} ({country.name})
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <input
                        className="form-control"
                        style={{width: "70%", marginTop: "12px"}}
                        placeholder="Enter the phone number*"
                    />
                </div>
                <div style={{display: "flex", gap: "10px", alignItems: "center"}}>
                    <FormControl className={globalStyles.customDropdownContainer} style={{width: "25%"}}>
                        <Select
                            displayEmpty
                            // value={selectedZone}
                            // onChange={(e) => setSelectedZone(e.target.value)}
                            className={globalStyles.customDropdownSelect}
                            renderValue={(selected) => (
                                <span className={globalStyles.guideName} style={{fontSize: "14px"}}>
                            {selected || "Select  Zone"}
                          </span>
                            )}
                        >
                            {timeZones.map((zone) => (
                                <MenuItem key={zone.code} value={zone.code}>
                                    {zone.code} ({zone.name})
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <FormControl className={globalStyles.customDropdownContainer} style={{width: "75%"}}>
                        <Select
                            displayEmpty
                            // value={selectedZone}
                            // onChange={(e) => setSelectedZone(e.target.value)}
                            className={globalStyles.customDropdownSelect}
                            renderValue={(selected) => (
                                <span className={globalStyles.guideName} style={{fontSize: "14px"}}>
                            {selected || "Select  Slot"}
                          </span>
                            )}
                        >
                            {timeSlot.map((zone) => (
                                <MenuItem key={zone.slot} value={zone.slot}>
                                    {zone.slot}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </div>
                <div className={globalStyles.poppinsHeaderStyle} style={{color: "#97908E", marginTop: "30px"}}>
                    Your migration details
                </div>
                <div className={styles.gridContainer}>
                    <div className={globalStyles.interSummaryStyle}  style={{color: "#F8F8F7", marginTop: "5px"}}>Source Platform</div>
                    <div className={globalStyles.interSummaryStyle}  style={{color: "#F8F8F7", marginTop: "5px"}}>{details.source}</div>

                    <div className={globalStyles.interSummaryStyle}  style={{color: "#F8F8F7", marginTop: "5px"}}>Target Platform</div>
                    <div className={globalStyles.interSummaryStyle}  style={{color: "#F8F8F7", marginTop: "5px"}}>{details.target}</div>

                    <div className={globalStyles.interSummaryStyle} style={{color: "#F8F8F7", marginTop: "5px"}}>Number of Records</div>
                    <div className={globalStyles.interSummaryStyle} style={{color: "#F8F8F7", marginTop: "5px"}}>{details.records}</div>
                </div>
                <button className={globalStyles.mainButton} style={{width: "100%", marginTop: "30px"}} onClick={(event) => navigate("/home")}>Submit and get callback</button>

            </div>
        )
    }


    return (
        <div>
            <div className={styles.whiteGloveBox}>
                <div className={styles.dFlex} style={{justifyContent: "space-between"}}>
                    <div className={globalStyles.interSummaryStyle} style={{lineHeight: "1.9rem"}}>
                        Fully managed, hassle-free migration—our experts handle everything from setup to
                        <br/>execution.
                        <span className={globalStyles.interStyle}> (Suitable for Enterprises or record volume above 100,000)</span>
                    </div>
                    <button
                        className={globalStyles.mainButton}
                        onClick={() => setShowWHiteGloveDialog(!showWhiteGloveDialog)}
                    >
                        Select Whiteglove package
                    </button>
                </div>
                <Dialog
                    open={showWhiteGloveDialog}
                    PaperProps={{
                        sx: {width: "705px", maxWidth: "none"}
                    }}
                >
                    <DialogContent sx={{backgroundColor: "#170903", padding: "0"}}>
                        <WhiteGlove fullWidth maxWidth="lg"
                                    close={() => setShowWHiteGloveDialog(!showWhiteGloveDialog)}/>
                    </DialogContent>
                </Dialog>

            </div>

            <div className={globalStyles.interSummaryStyle} style={{textAlign: "center", marginBottom: "20px"}}>Or
                choose from one of the packages below
            </div>

            <div className={styles.tableContainer}>
                <table className={globalStyles.table}>
                    <thead className={globalStyles.tableHeader}>
                    <tr className={globalStyles.rowStyles}>
                        <th className={globalStyles.headerCell}>Features available</th>

                        <th
                            className={`${globalStyles.headerCell} ${
                                selectedPlan === 'essential' ? globalStyles.selectedColumn : ''
                            }`}
                        >
                            <span style={{color: selectedPlan === 'essential' ? '#170903' : 'inherit'}}> Essential Migration </span>
                        </th>
                        <th
                            className={`${globalStyles.headerCell} ${
                                selectedPlan === 'pro' ? globalStyles.selectedColumn : ''
                            }`}
                        >
                            <span style={{color: selectedPlan === 'pro' ? '#170903' : 'inherit'}}> Pro Migration </span>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    {Object.entries(plans.essential.features).map(([feature, essentialValue]) => (
                        <tr key={feature} className={globalStyles.tableRow}>
                            <td className={globalStyles.cell}>{feature}</td>
                            <td className={`${globalStyles.cell} ${selectedPlan === 'essential' ? globalStyles.selectedColumn : ''}`}>
                                {essentialValue}
                            </td>
                            <td className={`${globalStyles.cell} ${selectedPlan === 'pro' ? globalStyles.selectedColumn : ''}`}>
                                {plans.pro.features[feature]}
                            </td>
                        </tr>
                    ))}
                    <tr>
                        <td className={globalStyles.cell}></td>
                        <td className={`${globalStyles.cell} ${selectedPlan === 'essential' ? globalStyles.selectedColumn : ''}`}>
                            {selectedPlan === 'essential' ?
                                <button className={globalStyles.connectedSuccess}>Essential plan selected</button> :
                                <button className={globalStyles.mainButton}
                                        onClick={() => setSelectedPlan('essential')}>Select Essential plan</button>}
                        </td>
                        <td className={`${globalStyles.cell} ${selectedPlan === 'pro' ? globalStyles.selectedColumn : ''}`}>
                            {selectedPlan === 'pro' ?
                                <button className={globalStyles.connectedSuccess}>Pro plan selected</button> :
                                <button className={globalStyles.mainButton}
                                        onClick={() => setSelectedPlan('pro')}>Select Pro plan</button>}

                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>

            {selectedPlan && <div className={styles.dFlex}>
                <div className={styles.leftSection}>
                    <div className={styles.displayCon}>
                       <span className={globalStyles.interSummaryStyle}>TOTAL </span>
                       <span className={styles.displayPrice}>
                           {currencySymbol}{formatCurrency(totalPrice)}
                       </span>
                    </div>
                </div>


                <div className={styles.rightSection}>
                    {/* Commented Currency Selection */}
                    {/* <div>
                        <div className={styles.dFlex} style={{marginBottom: "10px"}}>
                            <div className={globalStyles.poppinsHeaderStyle}>Choose your currency</div>

                            <div className={styles.dFlex} style={{marginLeft: "auto"}}>
                                <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon}/>
                                <span className={globalStyles.guideName}>Need another currency</span>
                            </div>
                        </div>


                        <FormControl style={{width: "100%"}} className={globalStyles.customDropdownContainer}>
                            <Select
                                value={currency}
                                onChange={(e) => {
                                    setCurrency(e.target.value);
                                }}
                                displayEmpty
                                className={globalStyles.customDropdownSelect}
                            >
                                {[
                                    {code: "USD", symbol: "$"},
                                    {code: "EUR", symbol: "€"},
                                    {code: "GBP", symbol: "£"}
                                ].map((currencyOption) => (
                                    <MenuItem key={currencyOption.code} value={currencyOption.code}>
                                        {currencyOption.code} {currencyOption.symbol}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>

                    </div>
                    <div style={{display: "flex", justifyContent: "flex-end"}}>
                        <button className={styles.autoDetectButton}>
                            <HiMapPin/> Auto detect based on location
                        </button>
                    </div>*/}

                    <div>

                        <div className={styles.dFlex} style={{marginBottom: "20px"}}>
                            <div className={globalStyles.selectionName}>MIGRATION COST ESTIMATOR</div>
                            <div className={styles.dFlex} style={{marginLeft: "auto"}}>
                                <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon}/>
                                <span className={globalStyles.guideName}>Can I get discounts for bulk migrations?</span>
                            </div>

                        </div>
                        <div className={styles.dFlex} style={{justifyContent: "space-between", marginBottom: "20px"}}>
                            <div className={globalStyles.interSummaryStyle}>Plan selected</div>
                            <div className={globalStyles.interSummaryStyle}
                                 style={{fontWeight: "600"}}>{plans[selectedPlan].name}</div>
                        </div>
                        <hr className={styles.hr}/>

                        <div className={styles.dFlex} style={{justifyContent: "space-between", marginBottom: "20px"}}>
                            <div className={globalStyles.interSummaryStyle}>Total Records to be Migrated</div>
                            <div
                                className={globalStyles.interSummaryStyle}> <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                            <input
                                type="number"
                                min="100"
                                max="1000000"
                                value={totalRecords}
                                onChange={(e) => setTotalRecords(parseInt(e.target.value) || 100)}
                                className={globalStyles.passwordInput}
                            />
                            <button
                                type="button"
                                onClick={calPrice}
                                className={globalStyles.mainButton}
                                style={{ padding: '8px 16px' }}
                            >
                                Recalculate Price
                            </button>
                        </div>
                            </div>
                        </div>
                        <hr className={styles.hr}/>

                        <div className={styles.dFlex} style={{justifyContent: "space-between", marginBottom: "20px"}}>
                            <div className={globalStyles.interSummaryStyle}>Total price</div>
                            <div className={globalStyles.interSummaryStyle}>{currencySymbol}{formatCurrency(basePrice)}</div>
                        </div>
                        <hr className={styles.hr}/>
                        <div>
                            <div className={globalStyles.passwordContainer}
                                 style={{paddingTop: "7px", marginBottom: "10px", marginTop: "15px"}}>
                                <input
                                    className={globalStyles.passwordInput}
                                    placeholder="Apply discount or promo code"
                                    style={{width: "95%"}}
                                />
                                <button
                                    type="button"
                                    className={globalStyles.passwordToggle}
                                >
                                    <HiPercentBadge className={globalStyles.closeIcon}/>

                                </button>


                            </div>

                        </div>

                        <div className={styles.dFlex} style={{justifyContent: "space-between", marginBottom: "20px"}}>
                            <div className={globalStyles.interSummaryStyle}>Taxes & charges @0%</div>
                            <div className={globalStyles.interSummaryStyle}>= {currencySymbol}{formatCurrency(totalPrice)}</div>
                        </div>

                        <div className={styles.finalPriceBox}>
                            <div className={styles.dFlex} style={{justifyContent: "space-between"}}>
                                <span className={globalStyles.poppinsHeaderStyle} style={{color: "#170903"}}>Final price for migration</span>
                                <div style={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "flex-start",
                                    gap: "10px",
                                }}>
                                    <span className={globalStyles.poppinsHeaderStyle}
                                          style={{color: "#170903"}}>${formatCurrency(totalPrice)}</span>
                                    <span className={globalStyles.poppinsHeaderStyle} style={{color: "#746B68"}}>
                                        ({numberToWords(Math.floor(totalPrice))} only)
                                    </span>
                                </div>

                            </div>
                        </div>

                        <button className={globalStyles.mainButton} style={{marginTop: "10px", width: "100%"}} onClick={() => setShowDialog(!showDialog)}>
                            Make payment
                        </button>
                    </div>
                    <Dialog
                        open={showDialog}
                        PaperProps={{
                            sx: { width: "705px", maxWidth: "none" }
                        }}
                    >
                        <DialogContent sx={{backgroundColor: "#170903", padding: "0"}}>
                            <ConfirmPayment fullWidth maxWidth="lg" close={()=> setShowDialog(!showDialog)}/>
                        </DialogContent>
                    </Dialog>
                </div>

            </div>
            }


            );

        </div>
    );
};

export default Pay;
