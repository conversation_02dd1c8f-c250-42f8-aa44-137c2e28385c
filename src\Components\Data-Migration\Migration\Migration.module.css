#root{
    /*height: 100vh;*/
    overflow: scroll;
}
.mainSection {
    margin-left: 220px;
    padding: 20px 0px 20px 0px;
    flex: 1;
    transition: margin-left 0.3s ease;
    background-color: #FFFF;
    height: 100vh;
}
.mainSection.expanded {
    margin-left: 85px;
}
.dFlex {
    display: flex;
    align-items: center;

}

.progressBarContainer {
    margin: 0 45px;
    margin-bottom: 15px;
}

.contentContainer {
    padding: 0 45px;
}

.progressBar {
    height: 40px;
    background-color: #EFEEED;
    border-radius: 4px;
    overflow: hidden;
}

.progressFill {
    height: 100%;
    background-color:#746B68;
    color: #F8F8F7;
    text-align: center;
    font-family: Poppins;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    align-items: center;
    display: flex;
    justify-content: center;


}

.statsCard {
    width: 50%;
    border-radius: 10px;
    border: 1px solid #DCDAD9;
    padding: 20px;
}
.summaryCard{
    width: 50%;
    padding: 0 20px;
    margin-top: 0;
    flex: 1.2;


}
.metrics {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
}

.metricItem {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: center;
    gap: 12px;
    margin-left: 15px;
}

.metricNumber {
    font-size: 36px;
    font-weight: 600;
    color: #746B68;
    line-height: 24px;
    font-family: Poppins;
}

.metricLabel {
    display: flex;
    align-items: center;
    color: #6B7280;
    font-size: 16px;
    justify-content: flex-end;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}
.dotName{
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #170903;
}
.templateDot {
    background-color: #A43E18;
}

.migrationDot {
    background-color: #EF8963;
}

.statsCard {
    flex: 1;
}

.statsContent {
    display: flex;
    gap: 20px;
}
.statusBadge {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 15px;
    border-radius: 7px;
    width: 75px;
    height: 30px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #170903;
}

.chartContainer {
    flex: 1;
    position: relative;
}

.summaryContent {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.summaryRow {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    align-items: center;
}


.filterButton {
    padding: 6px 12px;
    border: 1px solid #DCDAD9;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    font-size: 12px;
    color: #514742;
}

.activeFilter {
    background-color: #170903;
    color: #F8F8F7;
    border-color: #333;
}


.successIcon {
    color: #27ae60;
    font-weight: bold;
}

.failedIcon {
    color: #d63031;
    font-weight: bold;
}

.validateButton{
    border: 1px solid #DCDAD9;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    color: #514742;
    background-color: #ffffff;
    box-shadow:  2px 2px 5px 0px #0000001A;
    cursor: pointer;

}
.hr{
    border: 1px solid #DCDAD9;
}
.section {
    flex: 1;
    width: 50%;
    padding: 20px;

}
.videoContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color:#DCDAD980;
    padding: 40px;
    border-radius: 8px;
    height: 250px;
    width: 70%;
    margin: 0 auto 40px auto;

}

videoContent h3 {
    margin-bottom: 20px;
    color: #333;
}

.playButton {
    width: 74px;
    height: 40px;
    border-radius: 5px;
    border: none;
    background-color: #EF8963;
    color: #514742;
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.playButton:hover {
    background-color: #e55a3c;
}
.textVideo{
    font-size: 16px;
    color: #170903;
    font-weight: 400;
    font-family: Inter;
    line-height: 24px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .progressBarContainer {
        margin: 0 20px;
        margin-bottom: 15px;
    }

    .contentContainer {
        padding: 0 20px;
    }
}

@media (max-width: 480px) {
    .progressBarContainer {
        margin: 0 15px;
        margin-bottom: 15px;
    }

    .contentContainer {
        padding: 0 15px;
    }
}


