import React, { useState } from "react";
import globalStyles from "../globalStyles.module.css";
import styles from "./BillingPayment.module.css";
import Sidebar from "../Sidebar/Sidebar";
import { CheckIcon, GlobeIcon, SearchIcon } from "@heroicons/react/solid";
import { HiArrowDownTray, HiOutlineFunnel } from "react-icons/hi2";
// import Dropdown from "../Data-Migration/Dropdown/Dropdown";
// import {CogIcon} from "@heroicons/react/outline";
// import {Dialog, DialogContent} from "@mui/material";
// import ValueMapping from "../Data-Migration/Data-mapping/Value-mapping/Value-mapping";
// import CombineFields from "../Data-Migration/Data-mapping/Combine-value/CombineFields";
// import OverrideValues from "../Data-Migration/Data-mapping/OverrideValues/OverrideValues";
// import Advanced from "../Data-Migration/Data-mapping/Advanced/Advanced";
// import DefaultValue from "../Data-Migration/Data-mapping/Default-value/DefaultValue";
import { displayArticle } from "../../Helper/helper";


export default function BillingPayment() {
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
        return localStorage.getItem('isSidebarCollapsed') === 'true';
    });
    const [showFilterDropdown, setShowFilterDropdown] = useState(false);
    const [activeFilter, setActiveFilter] = useState('all');

    const data = [
        {
            name: "Migration 1",
            type: "Essential plan",
            date: "15.03.2025",
            amount: "$45",
            method: "**** **** 4321 (Visa)",
            status: "Paid",
            invoice: "INV-2013"
        },
        {
            name: "Migration 2",
            type: "Pro plan",
            date: "09.09.2024",
            amount: "$360",
            method: "**** **** 4321 (Visa)",
            status: "Failed",
            invoice: "INV-2013"
        },
        {
            name: "Migration 3",
            type: "Pro plan",
            date: "11.08.2024",
            amount: "$120",
            method: "**** **** 4321 (Visa)",
            status: "Paid",
            invoice: "INV-2013"
        }
    ];

    const toggleFilterDropdown = () => {
        setShowFilterDropdown(!showFilterDropdown);
    };

    const handleFilterSelect = (filter) => {
        setActiveFilter(filter);
        setShowFilterDropdown(false);
    };

    const filterOptions = [
        { id: 'all', label: 'See all' },
        { id: 'paid', label: 'Paid invoice' },
        { id: 'failed', label: 'Failed invoice' }
    ];

    // Filter options to show based on active filter
    const getVisibleFilterOptions = () => {
        const allOptions = [...filterOptions];
        // Always include the active filter and other available options
        return allOptions;
    };

    return (
        <div>
            <Sidebar
                isCollapsed={isSidebarCollapsed}
                setIsCollapsed={setIsSidebarCollapsed}
            />
            <div className={`${styles["main-section"]} ${isSidebarCollapsed ? styles.expanded : ''}`}>
                <div className={styles.dFlex} style={{ justifyContent: "space-between" }}>
                    <div className={`${globalStyles.headerStyle} ${styles.headerNoLeftPadding}`}>Billing & Payments</div>
                    <div className={globalStyles.searchBarContainer} style={{ paddingRight: 0 }}>
                        <div className={globalStyles.searchBar} >
                            {/* <div className={globalStyles.searchWrapper}>
                                <SearchIcon className={globalStyles.searchIcon} />
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className={globalStyles.searchInput}
                                    onFocus={(e) => {
                                        e.target.style.width = '200px';
                                        e.target.placeholder = 'Typing...';
                                    }}
                                    onBlur={(e) => {
                                        e.target.style.width = '80px';
                                        e.target.placeholder = 'Search...';
                                    }}
                                />
                            </div> */}

                            <div className={globalStyles.searchWrapper} style={{ marginRight: "0px" }}>
                                <GlobeIcon className={globalStyles.searchIcon} />
                                <input type="text" placeholder="Eng"
                                    className={`${globalStyles.searchInput} ${globalStyles.languageSelector}`}
                                    readOnly />
                            </div>
                        </div>
                    </div>
                </div>
                <div className={styles.dFlex} style={{ justifyContent: "stretch", gap: "10px" }}>
                    <div className={styles.container}>
                        <div className={globalStyles.poppinsHeaderStyle}
                            style={{ fontSize: "36px" }}>$150
                        </div>
                        <div className={styles.boxName}>Total amount <br /> spent</div>
                    </div>
                    <div className={styles.container}>
                        <div className={globalStyles.poppinsHeaderStyle}
                            style={{ fontSize: "36px" }}>03
                        </div>
                        <div className={styles.boxName}>Invoices</div>
                    </div>
                    <div className={styles.container}>
                        <div className={globalStyles.poppinsHeaderStyle}
                            style={{ fontSize: "36px" }}>01
                        </div>
                        <div className={styles.boxName}>Failed Invoices</div>
                    </div>                </div>
                <div className={styles.dFlex} style={{ marginTop: "30px" }}>
                    <div className={styles.dFlex}>
                        <img src="/assets/help.png" alt="Help" className={globalStyles.helpIcon} onClick={() => { displayArticle("Payment related issues") }} />
                        <span className={globalStyles.guideName}>Payment related issues</span>
                    </div>
                    <div className={styles.dFlex}
                        style={{
                            marginLeft: "auto",
                            justifyContent: "flex-end",
                            alignItems: "center",
                            position: "relative"
                        }}>
                        <div className={globalStyles.searchWrapper}>
                            <SearchIcon className={globalStyles.searchIcon} />
                            <input
                                type="text"
                                placeholder="Search..."
                                className={globalStyles.searchInput}
                                onFocus={(e) => {
                                    e.target.style.width = '200px';
                                    e.target.placeholder = 'Typing...';
                                }}
                                onBlur={(e) => {
                                    e.target.style.width = '80px';
                                    e.target.placeholder = 'Search...';
                                }}
                            />
                        </div>
                        <div style={{ position: "relative" }}>
                            <button
                                className={`${styles.dFlex} ${styles.buttonStyle}`}
                                style={{ gap: "10px", marginBottom: "10px" }}
                                onClick={toggleFilterDropdown}
                            >
                                <HiOutlineFunnel className={styles.iconStyle} />
                                {filterOptions.find(option => option.id === activeFilter).label}
                            </button>

                            {showFilterDropdown && (
                                <div className={styles.filterDropdown}>
                                    {getVisibleFilterOptions().map((option) => (
                                        <div
                                            key={option.id}
                                            className={`${styles.filterOption} ${activeFilter === option.id ? styles.activeFilter : ''}`}
                                            onClick={() => handleFilterSelect(option.id)}
                                        >
                                            {option.label}
                                            {activeFilter === option.id && <CheckIcon className={styles.checkIcon} />}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
                <div className={styles.tableContainer}>
                    <table className={globalStyles.table} style={{ border: "1px solid #DCDAD9" }}>                        <thead className={globalStyles.tableHeader}>
                        <tr className={globalStyles.rowStyles}>
                            <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Migration name & ID</th>
                            <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Plan type</th>
                            <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Date</th>
                            <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Amount</th>
                            <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Payment method</th>
                            <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Status</th>
                            <th className={`${globalStyles.headerCell} ${globalStyles.centerText}`}>Invoice</th>
                        </tr>
                    </thead>
                        <tbody>
                            {data
                                .filter(invoice => {
                                    if (activeFilter === 'all') return true;
                                    if (activeFilter === 'paid') return invoice.status === 'Paid';
                                    if (activeFilter === 'failed') return invoice.status === 'Failed';
                                    return true;
                                })
                                .map((invoice, index) => (
                                    <tr key={index} className={globalStyles.tableRow}>
                                        <td className={globalStyles.cell}>
                                            <div className={globalStyles.interSummaryStyle}
                                                style={{ fontSize: "14px" }}>{invoice.name}</div>
                                        </td>
                                        <td className={globalStyles.cell}>
                                            <div className={globalStyles.interSummaryStyle}
                                                style={{ fontSize: "14px" }}>{invoice.type}</div>
                                        </td>
                                        <td className={globalStyles.cell}>
                                            <div className={globalStyles.interSummaryStyle}
                                                style={{ fontSize: "14px" }}>{invoice.date}</div>
                                        </td>
                                        <td className={globalStyles.cell}>
                                            <div className={globalStyles.interSummaryStyle}
                                                style={{ fontSize: "14px" }}>{invoice.amount}</div>
                                        </td>
                                        <td className={globalStyles.cell}>
                                            <div className={globalStyles.interSummaryStyle}
                                                style={{ fontSize: "14px" }}>{invoice.method}</div>
                                        </td>
                                        <td className={`${globalStyles.cell} ${styles.centerAlignedCell}`} >
                                            <div className={`${globalStyles.interSummaryStyle} ${styles.statusStyle} `}
                                                style={{ fontSize: "14px", backgroundColor: invoice.status === 'Paid' ? '#F3B7A3' : "#DCDAD9" }}>{invoice.status}
                                            </div>
                                        </td>
                                        <td className={globalStyles.cell}>
                                            <div className={globalStyles.interSummaryStyle}
                                                style={{ fontSize: "14px" }}>{invoice.invoice} <HiArrowDownTray className={globalStyles.closeIcon} /></div>
                                        </td>
                                    </tr>
                                ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    )
}