import React, { useState, useEffect } from 'react';
import styles from "./DefaultValue.module.css";
import globalStyles from "../../../globalStyles.module.css"

const DefaultValue = ({ 
  close, 
  attribute, 
  target, 
  onSave,
  selectedEntityData
}) => {
  const [defaultValue, setDefaultValue] = useState("");
  
  useEffect(() => {
    if (attribute && attribute.default) {

      setDefaultValue(attribute.default);
    }
  }, [attribute]);
  
  const handleAssignDefaultValue = () => {
    if (onSave && typeof onSave === 'function') {
      onSave(defaultValue);
    }

    close();
  };
  
  return (
    <div className={styles.defaultValueContainer}>
      <div className={styles.header}>
        <div className={styles.title}>Default Value</div>
        <div className={styles.closeButton} onClick={close}>×</div>
      </div>
      
      <div className={styles.content}>
        <div className={styles.mappingGuideSection}>
          <div className={styles.mappingGuideLabel}>MAPPING GUIDE</div>
          <div className={styles.mappingGuideText}>Assign default values to the target field</div>
        </div>
        
        <div className={styles.formSection}>
          <div className={styles.formRow}>
            <div className={styles.formLabel}>Data type</div>
            <div className={styles.formValue}>{target?.name || "Tickets"}</div>
          </div>
          
          <div className={styles.formRow}>
            <div className={styles.formLabel}>Source field selected</div>
            <div className={styles.formValue}>{attribute?.targetfield || "Description"}</div>
          </div>
          
          <div className={styles.formRow}>
            <div className={styles.formLabel}>Default values assigned at target</div>
            <div className={styles.formValueInput}>
              <input
                type="text"
                className={styles.inputField}
                value={defaultValue}
                onChange={(e) => setDefaultValue(e.target.value)}
                placeholder="(Show default value)"
              />
            </div>
          </div>
        </div>
        <div className={styles.actionSection}>
  <button 
    className={styles.assignButton}
    onClick={handleAssignDefaultValue}
  >
    <img 
      src="/assets/check list.png" 
      alt="Checklist Icon" 
      className={styles.buttonIcon}
    />
   <span className={styles.assignText}>Assign default value</span>
  </button>
</div>

      </div>
    </div>
  );
};

export default DefaultValue;

