import React, { useState, useEffect } from 'react';
import { XIcon } from "@heroicons/react/solid";
import Editor from '@monaco-editor/react';
import styles from './Advanced.module.css';
import globalStyles from "../../../globalStyles.module.css";
import {HiXMark} from "react-icons/hi2";

const Advanced = ({ close, attribute, target, onSave }) => {
    const [code, setCode] = useState('');


    useEffect(() => {
        const defaultCode = attribute?.value
        setCode(defaultCode);
    }, [attribute]);


    const handleEditorChange = (value) => {
        setCode(value);
    };

    const handleSave = () => {
        onSave(code);
        close();
    };

    return (
        <div style={{height: "400px", padding: "0 0 20px 0", marginBottom: "150px"}}>
            <div className={styles.header}>
                <div style={{backgroundColor: "#170903", padding: "10px 0"}}>
                    <div className={styles.dFlex} style={{alignItems: "center", justifyContent: "space-between"}}>
                        <div className={globalStyles.headerStyle} style={{paddingLeft: "45px"}}>Advanced Mapping</div>
                        <div>
                            <HiXMark style={{fontSize: "16px", cursor: "pointer", padding: "0 24px", color: "#ffffff"}}
                                     onClick={close}/>
                        </div>

                    </div>
                </div>
            </div>
            <div className={styles.content}>
                <div className={styles.mappingGuideSection}>
                    <div className={styles.mappingGuideLabel}>MAPPING GUIDE</div>
                    <div className={styles.mappingGuideText}>Make changes to the code for customization</div>
                </div>
            </div>

            <div className={styles.editorWrapper} >
                <Editor
                    height="300px"
                    language="javascript"
                    theme="vs-dark"
                    value={code}
                    onChange={handleEditorChange}
                    options={{
                        minimap: {enabled: false},
                        fontSize: 12,
                        scrollBeyondLastLine: false,
                        automaticLayout: true,
                        lineNumbers: 'on',
                    }}
                />
            </div>

            <button
                className={styles.assignButton}
                onClick={handleSave}
            >
                <img
                    src="/assets/check list.png"
                    alt="Checklist Icon"
                    className={styles.buttonIcon}
                />
                <span className={styles.assignText}>Save Changes</span>
            </button>
        </div>
    );
};

export default Advanced;
