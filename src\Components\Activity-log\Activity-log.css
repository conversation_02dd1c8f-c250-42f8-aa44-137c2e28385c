.activity-log-container {
  display: flex;
  gap: 20px;
  padding: 5px 20px;
  margin: 0 auto;
}

.section {
  flex: 1;
  width: 50%;
  padding: 20px;
  border: 1px solid #DCDAD9;
  border-radius: 12px;
  background: #FFFFFF;
}
  
.activity-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}
  
.activity-table th {
    font-family: Poppins;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    text-align: left;
    padding: 10px;
    background: #DCDAD980;
    color: #514742;
  }
  
  .activity-table td {
    font-family: Inter;
    font-weight: 400;
    color: #170903;
    font-size: 12px;
    line-height: 24px;
    padding: 10px;
    border-bottom: 1px solid #DCDAD9;
  }
  
.user-placeholder {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #EFEEED;
    display: inline-block;
  }

.header-s {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 700;
  color: #EA5822;
}

.view-buttons {
  display: flex;
  gap: 8px;
}

.view-button {
  padding: 3px 26px;
  border-radius: 7px;
  border: 1px solid #DCDAD9;
  background-color: white;
  transition: background-color 0.3s ease;
  color: #514742;
  font-family: Inter;
  font-size: 12px;
  font-weight: 400;
}

.view-button.active {
  background-color: #EFEEED;
  border-color: #EFEEED;
}

.chart-container {
  width: 100%;
  height:250px;
}
.userAvatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #efeeed;
}


