.guide-drawer {
  position: fixed;
  top: 0;
  right: -400px;
  width: 480px;
  height: 100vh;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  transition: right 0.3s ease-in-out;
  padding: 20px;
  overflow-y: auto;
  z-index: 1000;
}

.guide-drawer.open {
  right: 0; /* Slide in */
}

.guide-container {
    max-width: 480px;
    padding: 15px;
    background: #fff;
    /*border-radius: 10px;*/
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

}
.expand-icon{
  width: 20px;
  height: 20px;
}
.normal-guide{
  background: white;
  border: 1px solid #DCDAD9;
  padding: 10px 30px;
  border-radius: 7px;
  margin-bottom: 20px;
}
  
.guide-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  color: #EA5822;
  font-family: Poppins;
  font-weight: 700;
  line-height: 24px;
}

  .guide-card{
    background-color: #F8F8F7;
    padding: 10px 20px;
    margin-bottom: 15px;

  }
  
  .guide-logo {
    width: 34px;
    height: 36px;
  }
  
.guide-question {
    display: flex;
    justify-content: space-between;

  }
  
  .expanded-title{
    font-size: 14px;
    color: #514742;
    font-family: Poppins;
    font-weight: 600;
    line-height: 24px;
  }
  
  .video-placeholder {
    width: 100%;
    height: 120px;
    background: #DCDAD980;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    margin-bottom: 15px;
    margin-top: 15px;
  }
  
  .play-button {
    background: #e76f00;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
  }
  
  .guide-step {
    margin-bottom: 10px;
  }
.step-header {
  font-size: 14px;
  color: #EA5822;
  font-family: Poppins;
  font-weight: 700;
  line-height: 24px;

}

.step-description{
  font-size: 16px;
  color: #170903;
  font-family: Inter;
  font-weight: 400;
  line-height: 24px;
}
  
  .image-placeholder {
    width: 100%;
    height: 80px;
    background: #ddd;
    border-radius: 8px;
    margin: 10px 0;
  }
  
  .feedback-section {
    background: #170903;
    padding: 10px;
    text-align: center;
    border-radius: 7px;
    border: 1px solid #DCDAD9;
    margin-bottom: 10px;
  }
  
  .feedback-icons {
    font-size: 20px;
    margin: 0 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 25px;

  }
  .feedback-head{
    font-size: 16px;
    margin-top: 5px;
    font-family: Inter;
    color: #F8F8F7;
    font-weight: 400;
  }
  
  .chat-link {
    font-size: 12px;
    margin-top: 5px;
    font-family: Inter;
    color: #97908E;
    font-weight: 400;
  }
  .hr-div{
    border: 1px solid #DCDAD9
  }

