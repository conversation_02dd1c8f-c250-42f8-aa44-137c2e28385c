/* Full page center alignment */
.loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #ffffff;
  z-index: 0;
  }
  

  .loader {
    border: 8px solid #f3f3f3; /* Light grey */
    border-top: 8px solid #ff8303; /* Dark Green */
    border-radius: 50%;
    width: 50px; /* Reduced size */
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: spinloader 2s linear infinite;
  }
  
  /* Logo inside the loader */
  .loader img {
    height: 30px; /* Reduced size */
    width: 30px;
    animation: spinlogo 2s linear infinite;
  }
  
  @keyframes spinloader {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes spinlogo {
    0% {
      transform: rotate(360deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }
