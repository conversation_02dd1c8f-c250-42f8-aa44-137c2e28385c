html, body {
    margin: 0;
    padding: 0;
    height: 100%;
}

.no-color-button{
    background: none;
    color: #B9B5B3;
    font-family: "Inter";
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    border: 1px solid #746B68;
    border-radius: 4px;
    padding: 5px 10px;
    /*width: 125px;*/
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: left;
    cursor: pointer;
}
form {
    display: flex;
    flex-direction: column;
    width: 100%;
}

input {
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-family: "Inter";
    font-weight: 400;
    font-size: 16px;
}
.form-container{
    gap: 20px;
    padding: 20px 20px 20px 0;
}
.form-group{
    display: grid;
    grid-template-columns: 1fr;
    padding-top: 20px;
}

.main-button{
    background-color: #EF8963;
    height: 40px;
    padding: 10px 45px;
    border-radius: 5px;
    color: #170903;
    font-size: 14px;
    font-weight: 500;
    /*width: 145px;*/
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    border: none;
}
.eye-icon {
    width: 20px;
    height: 20px;
}
 input:disabled{
     background-color: #DCDAD9;
     pointer-events: none;
 }

 input.form-control:focus{
     box-shadow: inset 0px 0px 6px rgba(0, 0, 0, 0.2), inset 0px 0px 6px #DCDAD9 !important;
     outline:none;
 }

input:focus{
    box-shadow: inset 0px 0px 6px rgba(0, 0, 0, 0.2), inset 0px 0px 6px #DCDAD9 !important;
    outline:none;
}
.error-message{
    color: #f56c6c;
    text-align: start;
    margin: 0;
    font-size: 11px;
    font-family: "Inter";
}
.input-error{
    box-shadow: inset 0px 0px 6px #ea5822, inset 0px 0px 6px #ec3535 !important;
    outline:none;
}

.hover-zoom {
    transition: transform 0.3s ease-in-out; /* Smooth zoom */
}

.hover-zoom:hover {
    transform: scale(1.1) !important;
}
