import React, { useEffect, useState } from "react";
import "./Template-log.css";
import { <PERSON>, Pie, <PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { getMigrationPlans, getTemplateLog } from "../apiService";
import { useNavigate } from "react-router-dom";
import LoaderSpinner from "../loaderspinner";

const TemplateLog = () => {
  // const templateData = [
  //   { name: "Template name", createdBy: "", createdOn: "13, Jan, 2025" },
  //   { name: "Template name", createdBy: "", createdOn: "05, Jan, 2025" },
  //   { name: "Template name", createdBy: "", createdOn: "28, Dec, 2024" },
  //   { name: "Template name", createdBy: "", createdOn: "1, Jan, 2025" },
  //   { name: "Template name", createdBy: "", createdOn: "5, Jan, 2025" },
  //   { name: "Template name", createdBy: "", createdOn: "8, Dec, 2024" },
  // ];
  const [templateData, setTemplateData] = useState([]);

  const latestData = templateData.slice(-3);
  const [view, setView] = useState('week');
  const navigate = useNavigate()
  const [data, setData] = useState({
    week: { templates: 0, migrations: 0 }
  });


  // const data = {
  //   // quarter: { templates: 5, migrations: 2 },
  //   week: { templates: 3, migrations: 1 }
  // };

  const currentData = data[view];

  // Ensure chart always has valid data to display immediately
  const chartData = [
    { name: 'Templates', value: Math.max(currentData.templates, 0) || 0 },
    { name: 'Migrations', value: Math.max(currentData.migrations, 0) || 0 }
  ].filter(item => item.value > 0);

  // If no data, show placeholder chart
  const displayChartData = chartData.length > 0 ? chartData : [
    { name: 'No Data', value: 1 }
  ];
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const COLORS = ['#DCDAD980', '#EF8963'];
  const PLACEHOLDER_COLOR = ['#f5f5f5'];
  const [email, setEmail] = useState(localStorage.getItem('email'));
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetch = async () => {
      setIsLoading(true);
      try {
        const res = await getMigrationPlans(email);
        const temp = res.slice(-3)
        setTemplateData(temp);
        setIsLoading(false);
      } catch (e) {
        console.log(e);
        setIsLoading(false);
      }
    }
    fetch()
  }, []);

  useEffect(() => {
    const fetch = async () => {
      setIsLoading(true);
      try {
        const res = await getTemplateLog(email);
        console.log(res);
        setData({ week: { templates: res.templates_created, migrations: res.migrations_completed } }
        );
      } catch (e) {
        console.log(e);
      } finally {
        setIsLoading(false);
      }
    };
    fetch();
  }, []);

  return (
    <div className="activity-log-container">
      <div className="section">
        {/* <div style={{ border: "1px solid #DCDAD9", padding: "20px", borderRadius: "15px" }}> */}
        <div className="header-s" style={{ display: "flex" }}>
          <h1 className="migration-title">MIGRATION LOG</h1>
          <div className="view-buttons">
            {/*<button*/}
            {/*    className={`toggle-button ${view === 'quarter' ? 'active-toggle' : ''}`}*/}
            {/*    onClick={() => setView('quarter')}*/}
            {/*>*/}
            {/*  Last Quarter*/}
            {/*</button>*/}
            <button
              className={`toggle-button ${view === 'week' ? 'active-toggle' : ''}`}
              onClick={() => setView('week')}
            >
              Last Week
            </button>
          </div>
        </div>

        <div className="migration-content">
          <div className="metrics">
            <div className="metric-item">
              <div className="metric-number">
                {String(currentData.templates).padStart(2, '0')}
              </div>
              <div className="metric-label">
                <span className="dot template-dot"></span>
                <span className="dotName">Templates created</span>
              </div>
            </div>

            <div className="metric-item">
              <div className="metric-number">
                {String(currentData.migrations).padStart(2, '0')}
              </div>
              <div className="metric-label">
                <span className="dot migration-dot"></span>
                Migrations completed
              </div>
            </div>
          </div>

          <div className="chart-container-pie">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={displayChartData}
                  cx="50%"
                  cy="50%"
                  startAngle={180}
                  endAngle={-180}
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={0}
                  dataKey="value"
                  animationBegin={0}
                  animationDuration={600}
                  isAnimationActive={true}
                >
                  {displayChartData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={chartData.length > 0 ? COLORS[index] : PLACEHOLDER_COLOR[0]}
                    />
                  ))}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
        {/* </div> */}
      </div>

      <div className="section">
        <div className="template-container">
          <table className="template-table">
              <thead>
                <tr>
                  <th>Name of Template</th>
                  <th>Created by</th>
                  <th>Created on</th>
                </tr>
              </thead>
              <tbody style={{ width: "100%" }}>

                {isLoading ? (
                  // Loading state
                  <tr>
                    <td colSpan="9" style={{ textAlign: "center", padding: "40px 0" }}>
                      <div style={{ display: "flex", justifyContent: "center" }}>
                        <LoaderSpinner height={50} fullpage={false} />
                      </div>
                    </td>
                  </tr>
                ) : latestData.length === 0 ? (
                  // Empty state
                  <tr>
                    <td colSpan="9" style={{ textAlign: "center", padding: "40px 0" }}>
                      <div>
                        <p style={{ fontSize: "16px", color: "#888" }}>Yet to create template</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  // Data state
                  latestData.map((item, index) => (
                    <tr key={index}>
                      <td>
                        <div style={{ fontSize: "13px" }}>
                          {item.plan_name}
                        </div>
                      </td>
                      <td>
                        <div style={{ display: "flex", alignItems: "center", gap: "10px", fontSize: "13px" }}>
                          <img src="/assets/profile-orange.png" alt="User Avatar" className="userAvatar" />
                          <span style={{ marginLeft: "10px" }}>{item.email_id}</span>
                        </div>
                      </td>
                      <td>
                        <div style={{ fontSize: "13px" }}>
                          {formatTimestamp(item.createdAt)}
                        </div>
                      </td>
                    </tr>
                  ))
                )}

              </tbody>
            </table>
            <button className="main-button" onClick={() => navigate('/saved-templates')}>Go to History</button>
          </div>
      </div>
    </div>

  );
};

export default TemplateLog;
