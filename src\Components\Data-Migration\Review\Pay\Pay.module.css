.whiteGloveBox {
    background-color: #EFEEED;
    padding: 25px;
    border-radius: 10px;
    margin: 24px;
}
.dFlex{
    display: flex;
    align-items: center;
}
.section{
    flex: 1;
    width: 50%;
}

.tableContainer {
    margin: 24px;
}


.leftSection, .rightSection {
   width: 50%;
    flex: 1;
    padding: 20px;
    border-radius: 10px;

}

.sectionLabel {
    font-weight: bold;
    margin-bottom: 10px;
}

.currencyDropdownContainer {
    position: relative;
}

.currencyDropdown {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    background: #fff;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    border-radius: 5px;
}

.currencyOptions {
    position: absolute;
    width: 100%;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 5px;
    z-index: 10;
}

.currencyOption {
    width: 100%;
    padding: 10px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
}

.currencyOption:hover {
    background: #f0f0f0;
}

.autoDetectButton {
    gap: 5px;
    padding-right: 15px;
    padding-left: 15px;
    border-radius: 3px;
    border-width: 1px;
    background: #FFFFFF;
    margin: 20px 0;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    color: #514742;
    border: 1px solid #DCDAD9;
    cursor: pointer;



}

.estimatorTitle {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
}

.breakdownRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.numberInput {
    width: 60px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    text-align: right;
}

.finalPriceBox {
    background: #DCDAD9;
    padding: 10px;
    border-radius: 5px;
    /*margin-top: 10px;*/
    text-align: center;
    font-weight: bold;
}

.finalPriceValue {
    font-size: 24px;
    color: #333;
}


.currencySymbol {
    font-weight: bold;
}

.hr{
    border: 1px solid #DCDAD9;
}
.displayPrice{
    font-family: Poppins;
    font-weight: 600;
    font-size: 36px;
    line-height: 24px;
    color:#746B68;

}

.displayCon{
    width: 450px;
    height: 114px;
    min-width: 450px;
    gap: 10px;
    padding-top: 45px;
    padding-right: 50px;
    padding-bottom: 45px;
    padding-left: 50px;
    border-radius: 5px;
    border-width: 1px;
    border: 1px solid #DCDAD9;
    display: flex;
    align-items: center;
    justify-content: center;

}
.confirmDialog{
    background-color: #170903;
    /*width: 600px;*/
    height: 241px;
    padding: 20px 30px;
}
.gridContainer {
    display: grid;
    grid-template-columns: 1fr 2fr; /* First column auto width, second takes remaining space */
    gap: 10px;
    color: #F8F8F7;
    margin-top: 5px;
}

