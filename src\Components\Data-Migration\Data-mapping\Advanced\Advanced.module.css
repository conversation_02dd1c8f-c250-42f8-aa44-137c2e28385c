.dFlex{
    display: flex;
}
.header{
    background-color: #FFFFFF;
}
.content {
    padding: 0px;
    margin-bottom: 20px;
}

.mappingGuideSection {
    display: flex;
    padding: 8px 16px;
    align-items: center;
    gap: 5px;
    align-self: stretch;
    background: #EFEEED;
    border-radius: 4px;
    margin-bottom: 0px;
}
.mappingGuideLabel {
    color: #EA5822;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    margin-right: 4px;
}


.mappingGuideText {
    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    color: #170903;


}

.assignButton {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    margin: auto;
    margin-top: 30px;
}

.assignButton:hover {
    background-color: #f9f9f9;
}

.buttonIcon {
    width: 16px;
    height: 16px;
}
.assignText {
    color: #514742;
    text-align: center;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

