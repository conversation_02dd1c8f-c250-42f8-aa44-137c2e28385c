"use client"

import { useState } from "react"
import styles from "./ShareYourTemplate.module.css"
import globalStyles from "../globalStyles.module.css"

const ShareYourTemplate = ({
  onClose,
  templateName = "(Template name)",
  templateId = "(Template ID)",
}) => {
  const [emails, setEmails] = useState("")
  const [accessType, setAccessType] = useState("")

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log({ emails, accessType })
  }

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContainer}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>Share your template</h2>
          <button className={styles.closeButton} onClick={onClose}>
            ×
          </button>
        </div>

        <div className={styles.modalContent}>
          {/* TEMPLATE INFO */}
          <div className={styles.templateInfo}>
            <div className={styles.infoRow}>
              <span className={styles.infoLabel}>Template name</span>
              <span className={styles.infoValue}>{templateName}</span>

              <span className={styles.infoLabel} style={{ marginLeft: "30px" }}>
                Template ID
              </span>
              <span className={styles.infoValue}>{templateId}</span>
            </div>
          </div>

          {/* FORM FIELDS */}
          <div className={styles.detailsHeader}>
            <div className={styles.sectionTitle}>ENTER DETAILS</div>
            <button className={styles.copyLinkButton}>
              <span className={styles.linkIcon}>🔗</span>
              Copy link
            </button>
          </div>

          <div className={styles.formGroup}>
            <label className={styles.formLabel}>Add email IDs</label>
            <input
              type="text"
              className={styles.formInput}
              placeholder="Invite collaborators"
              value={emails}
              onChange={(e) => setEmails(e.target.value)}
            />
          </div>

          <div className={styles.formGroup}>
            <label className={styles.formLabel}>Security</label>
            <div className={styles.selectWrapper}>
              <select
                className={styles.formSelect}
                value={accessType}
                onChange={(e) => setAccessType(e.target.value)}
              >
                <option value="" disabled selected>
                  Choose access
                </option>
                <option value="view">View only</option>
                <option value="edit">Edit & review</option>
              </select>
              <div className={styles.selectArrow}>
                <img src="/assets/arrow-down-01.png" alt="Expand" />
              </div>
            </div>
          </div>

          <button
            className={globalStyles.mainButton}
            style={{ width: "100%" }}
            onClick={handleSubmit}
          >
            Confirm and share
          </button>
        </div>
      </div>
    </div>
  )
}

export default ShareYourTemplate
