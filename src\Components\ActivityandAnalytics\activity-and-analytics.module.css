.container {
  display: flex;
  min-height: 100vh;
  background-color: #fff;
  overflow: hidden;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.mainSection {
  margin-left: 240px;
  padding: 20px 45px 20px 45px;
  flex: 1;
  transition: margin-left 0.3s ease;
  background-color: #fff;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.mainSection.expanded {
  margin-left: 110px;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 45px;
}

.titleWithBackButton {
  display: flex;
  align-items: center;
  gap: 45px;
}

.pageTitle {
  font-size: 24px;
  font-weight: 600;
  color: #ea5822;
  font-family: "Poppins", sans-serif;
  margin: 0;
}

.headerControls {
  display: flex;
  gap: 45px;
}

.languageSelector {
  width: 40px !important;
}

.dashboardGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 45px;
  margin-bottom: 45px;
}

.card {
  border: 1px solid #dcdad9;
  border-radius: 8px;
  padding: 20px;
  background-color: white;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 45px;
}

.cardTitle {
  font-size: 14px;
  font-weight: 700;
  color: #ea5822;
  font-family: "Poppins", sans-serif;
  margin: 0;
}

.viewButtons {
  display: flex;
  gap: 8px;
}

.viewButton {
  padding: 3px 26px;
  border-radius: 7px;
  border: 1px solid #dcdad9;
  background-color: white;
  transition: background-color 0.3s ease;
  color: #514742;
  font-family: "Inter", sans-serif;
  font-size: 12px;
  font-weight: 400;
  cursor: pointer;
}

.viewButton.active {
  background-color: #efeeed;
  border-color: #efeeed;
}

.chartContainer {
  width: 100%;
  height: 250px;
}

.migrationStats {
  display: flex;
  width: 458px;
  align-items: flex-start;
  gap: 53px;
}

.statNumbers {
  display: flex;
  flex-direction: column;
  gap: 45px;
}

.statItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.statValue {
  font-size: 32px;
  font-weight: 600;
  color: #746B68;
  font-family: "Poppins" ;
}

.statLabel {
  font-size: 14px;
  color: #746b68;
  font-family: "Inter";
}

.pieChartContainer {
  width: 250px;
  height: 250px;
  flex-shrink: 0;
}

.activityHistorySection {

  border-radius: 5px;
  margin-bottom: 20px;
  overflow: hidden;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #170903;
  color: #ea5822;
  padding: 10px 15px;
  font-family: Poppins;
  margin-bottom: 45px;
}

.sectionHeader h2 {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.downloadReportButton {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 3px;
  border: 1px solid #dcdad9;
  background: #fff;
  color: #514742;
  font-family: "inter";
}

/* Activity Table Styles */
.activityTableWrapper {
  width: 100%;
  border: 1px solid #ddd;
}

.activityTable {
  width: 100%;
  border-collapse: collapse;
  font-family: "Inter", sans-serif;
}

.activityTable th {
  background-color: #f5f5f5;
  padding: 12px 16px;
  text-align: left;
  color: #746b68;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
}

.activityTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #170903;
}

.activityTable tr:last-child td {
  border-bottom: none;
}

/* Update the dateContainer styling */
.dateContainer {
  background-color: #f5f5f5;
  padding: 0;
  position: relative;
}

/* Update the dateHeader class to position the date in the center */
.dateHeader {
  font-weight: 600;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #746B68;
  padding: 12px 16px;
  position: relative;
}

/* Add a specific style for the date text to ensure it's centered */
.dateText {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* Style for the chevron to keep it at the edge */
.chevron {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
}

.chevronUp {
  transform: translateY(-50%) rotate(180deg);
}

.chevronIcon {
  width: 20px;
  height: 20px;
}

.userCell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.userAvatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #efeeed;
}

@media (max-width: 1024px) {
  .dashboardGrid {
    grid-template-columns: 1fr;
  }
  
  .activityTableWrapper {
    overflow-x: auto;
  }
}

@media (max-width: 768px) {
  .headerContainer {
    flex-direction: column;
    align-items: flex-start;
    gap: 45px;
  }

  .headerControls {
    width: 100%;
    justify-content: space-between;
  }
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  gap: 45px;
  margin-top: 45px;
}

.pageInfo,
.rowsPerPageContainer {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.pageButtons,
.rowsButtons {
  display: flex;
  gap: 5px;
}

.pageButton,
.rowsButton {
  border: 1px solid #ddd;
  background-color: white;
  padding: 5px 10px;
  cursor: pointer;
  min-width: 30px;
  text-align: center;
  font-size: 14px;
}

.active {
  background-color: #333;
  color: #514742;
}

.pageActive{
  background-color: #333;
  color: #F8F8F7;
}
.downloadButton {
  margin-right: 15px; /* Adjust as needed */
}
.paginationText {
  color: #170903;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}
.templateDot {
  background-color: #E5E7EB;
}
.migrationDot {
  background-color: #EF8963;
}
