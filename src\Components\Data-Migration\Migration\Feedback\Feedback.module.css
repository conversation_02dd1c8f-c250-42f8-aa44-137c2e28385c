.popup {
    background: #170903;
    color: white;
    padding: 30px;
    border-radius: 10px;
    /*width: 100%;*/
    position: relative;
}

.closeButton {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    color: #F8F8F7;
    font-size: 20px;
    cursor: pointer;
}

.dFlex{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.question {
    margin-bottom: 15px;
}
.fontStyle{
    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #F8F8F7;


}

.stars {
    display: flex;
    gap: 5px;
    cursor: pointer;
}

.starFilled {
    color: #EF8963;
    font-size: 20px;
}

.starEmpty {
    color: gray;
    font-size: 20px;
}

.toggleSwitch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggleSwitch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: gray;
    border-radius: 20px;
    transition: 0.4s;
}

.toggleSwitch input:checked + .slider {
    background-color: #ff7f50;
}

.slider::before {
    content: "";
    position: absolute;
    height: 18px;
    width: 18px;
    left: 4px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: 0.4s;
}

.toggleSwitch input:checked + .slider::before {
    transform: translateX(26px);
}


.formCheckInput:checked {
    appearance: none;
    /*background-color: #746B68;*/
    /*border-color: #746B68;*/
    position: relative;
    input{
        padding: 0 !important;
    }
}

.formCheckInput:checked::after {
    appearance: none;
    padding: 0;
    content: "";
    width: 8px;
    height: 8px;
    background-color: #DCDAD9;
    border-radius: 50%;
    /*display: block;*/
    position: absolute;
    /*top: 50%;*/
    /*left: 50%;*/
    transform: translate(-50%, -50%);

}
