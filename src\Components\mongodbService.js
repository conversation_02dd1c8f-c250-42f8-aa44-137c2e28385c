import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import * as Realm from 'realm-web';

const MongodbContext = createContext(null);

export const useMongodb = () => {
    const context = useContext(MongodbContext);
    if (!context) {
        throw new Error('useMongodb must be used within a MongodbProvider');
    }
    return context;
};

export const MongodbProvider = ({ children }) => {
    const [app] = useState(() => new Realm.App({ id: 'application-0-fsjxore' }));
    const [user, setUser] = useState(null);
    const [migrationDataMongo, setmigrationDataMongo] = useState(null);
    const [batchDataMongo, setBatchDataMongo] = useState(null);
    const [mongodb, setMongodb] = useState(null);
    const [collection_migration, setCollectionMigration] = useState(null);
    const [collection_batch, setCollectionBatch] = useState(null);
    const [isWatchingMigration, setIsWatchingMigration] = useState(false);
    const [isWatchingBatch, setIsWatchingBatch] = useState(false);

    const login = useCallback(async () => {
        try {
            const credentials = Realm.Credentials.apiKey('y31fX6itxvFQ6MTbOPDY9puTbxcn4DLUP0V20fzN1upRIzF8A3Uq7luoM9BKYrnT');
            const loggedInUser = await app.logIn(credentials);
            setUser(loggedInUser);
            return loggedInUser;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }, [app]);

    // **Initialize Collections**
    const initializeCollections = useCallback(async (currentUser) => {
        if (!currentUser) return;

        const mongoClient = currentUser.mongoClient('transformer');
        setMongodb(mongoClient);

        const migrationCollection = mongoClient.db('datatransformation').collection('migrations');
        setCollectionMigration(migrationCollection);

        const batchCollection = mongoClient.db('datatransformation').collection('batches');
        setCollectionBatch(batchCollection);

        return { mongoClient, migrationCollection, batchCollection };
    }, []);

    const watchMigrations = useCallback(async () => {
        let currentUser = user;
        if (!currentUser) {
            currentUser = await login();
        }

        if (currentUser) {
            const collections = await initializeCollections(currentUser);
            if (!collections) return;

            const { migrationCollection, batchCollection } = collections;

            const watchMigrationCollection = async () => {
                if (!isWatchingMigration) {
                    setIsWatchingMigration(true);
                    const pipeline = [{ $match: { 'fullDocument.migrationId': '10ee4e11-6ea4-4a69-82bb-41858c83bf4b' } }];

                    try {
                        for await (const change of migrationCollection.watch(pipeline)) {
                            setmigrationDataMongo(change);
                        }
                    } catch (error) {
                        setIsWatchingMigration(false);
                        setTimeout(() => watchMigrations(), 1000);
                    }
                }
            };


            const watchBatchCollection = async () => {
                if (!isWatchingBatch) {
                    setIsWatchingBatch(true);
                    const pipeline_batch = [{ $match: { 'fullDocument.batchId': "015d1fc3-9e58-47f1-a313-ce1298819a35" } }];

                    try {
                        for await (const change of batchCollection.watch(pipeline_batch)) {

                            setBatchDataMongo((prev) => ({ ...prev, ...change }));
                        }
                    } catch (error) {
                        setIsWatchingBatch(false);
                        setTimeout(() => watchMigrations(), 1000);
                    }
                }
            };

            // **Execute Watching Functions**
            Promise.all([watchMigrationCollection(), watchBatchCollection()]);
        } else {
            console.error('User is not logged in.');
        }
    }, [user, login, initializeCollections, isWatchingMigration, isWatchingBatch]);

    // **Close MongoDB Connection**
    const closeConnection = useCallback(async () => {
        if (user) {
            await user.logOut();
            setUser(null);
            setMongodb(null);
            setCollectionMigration(null);
            setCollectionBatch(null);
            setIsWatchingMigration(false);
            setIsWatchingBatch(false);
        }
    }, [user]);





    useEffect(() => {
        return () => {
            closeConnection();
        };
    }, [closeConnection]);

    const value = {
        user,
        migrationDataMongo,
        batchDataMongo,
        login,
        watchMigrations,
        closeConnection
    };

    return (
        <MongodbContext.Provider value={value}>
            {children}
        </MongodbContext.Provider>
    );
};


