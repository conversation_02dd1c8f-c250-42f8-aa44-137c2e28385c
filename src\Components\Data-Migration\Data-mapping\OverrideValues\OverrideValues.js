import React, { useState, useEffect } from "react";
import styles from "./OverrideValues.module.css";
import globalStyles from "../../../globalStyles.module.css"
import { CheckIcon } from "@heroicons/react/solid";
import {HiLink, HiXMark} from "react-icons/hi2";
import Dropdown from "../../Dropdown/Dropdown";
import {HiPlus} from "react-icons/hi";

export default function OverrideValues({
    close,
    attribute,
    target,
    onSave,
   selectedEntityData
}) {
    const [override, setOverride] = useState([]);
    const [sourceValues, setSourceValues] = useState(null);
    const [targetValue, setTargetValue] = useState(null);

    useEffect(() => {
        if (attribute && attribute.override) {
            setOverride(attribute.override || []);
        } else {
            setOverride([{ sourcevalue: "", targetvalue: "" }]);
        }
        console.log(attribute);
    }, [attribute]);

    const handleAddMapping = () => {
        setOverride([...override, { sourcevalue: "", targetvalue: "" }]);
    };
    const isSourceValueDuplicate = (mappings, currentIndex) => {
        if (!mappings || !mappings[currentIndex] || mappings[currentIndex].sourcevalue.trim() === '') {
            return { status: false, duplicateIndex: -1, duplicateValue: '' };
        }

        const currentValue = mappings[currentIndex].sourcevalue.trim(); // Trim spaces for accurate comparison

        for (let i = 0; i < currentIndex; i++) {
            if (mappings[i].sourcevalue.trim() === currentValue) {
                return { status: true, duplicateIndex: i, duplicateValue: currentValue };
            }
        }

        return { status: false, duplicateIndex: -1, duplicateValue: '' };
    };






    const handleDeleteMapping = (index) => {
        const newMappings = [...override];
        newMappings.splice(index, 1);
        setOverride(newMappings);
    };

    const handleSourceValueChange = (index, values) => {
        const newMappings = [...override];
        newMappings[index].sourcevalue = values;
        setOverride(newMappings);
    };

    const handleTargetValueChange = (index, value) => {
        const newMappings = [...override];
        if (attribute.type === "number") {
            newMappings[index].targetvalue = value === "" ? "" : Number(value);
        } else {
            newMappings[index].targetvalue = value;
        }

        setOverride(newMappings);
    };

    const handleSaveOverrides = () => {
        if (onSave) {
            onSave(override);
        }
        close();
    };
    const getStatusIcon = (mappings, k) => {
        const existingGroup =isSourceValueDuplicate(mappings, k);


        if (existingGroup.status) {
            return (
                <div className={globalStyles.statusIconError}>
                    <span className={globalStyles.iconText}>×</span>
                </div>
            );
        } else {
            return (
                <div className={globalStyles.statusIconSuccess}>
                    <span className={globalStyles.iconText}>✓</span>
                </div>
            );
        }
    };

    return (
        <div className={styles.overrideContainer}>
            <div className={styles.headerContainer}>
                <div className={styles.headerTitle}>
                    <h2>Override Values</h2>
                    <HiXMark className={styles.closeIcon} onClick={close} />
                </div>
                <div className={styles.mappingGuide}>
                    <span className={styles.guideLabel}>MAPPING GUIDE</span>
                    <span className={styles.guideText}>Override the target field data with custom values</span>
                </div>
            </div>

            <div className={styles.contentContainer}>
                <div className={styles.fieldInfoRow}>
                    <div className={styles.fieldInfoItem}  style={{display: "flex", gap: "175px", alignItems: "center", marginTop: "10px"}}>
                        <div className={globalStyles.poppinsHeaderStyle}>Data Type</div>
                        <div className={globalStyles.interSummaryStyle}>{target?.name || "Tickets"}</div>
                    </div>
                    <div className={styles.fieldInfoItem}  style={{display: "flex", gap: "100px", alignItems: "center", marginTop: "20px"}}>
                        <div className={globalStyles.poppinsHeaderStyle}>Source field selected</div>
                        <div className={globalStyles.interSummaryStyle}>{attribute?.sourcefield || "Description"}</div>
                    </div>
                </div>

                <div>
                    <table className={globalStyles.table} style={{marginBottom: "20px", width: "100%"}}>
                        <thead className={globalStyles.tableHeader}>
                        <tr className={globalStyles.rowStyles}>
                            <th className={globalStyles.headerCell}>Status</th>
                            <th className={globalStyles.headerCell}>Source values</th>
                            <th className={globalStyles.headerCell}>Target values</th>
                            <th className={globalStyles.headerCell}>Delete</th>
                        </tr>
                        </thead>
                        <tbody>

                        {override.map((mapping, index) => (
                            <React.Fragment key={index}>
                                <tr className={globalStyles.tableRow}>
                                    <td className={globalStyles.cell} style={{width: "80px", textAlign: "center"}}>
                                        {getStatusIcon(override, index)}
                                    </td>
                                    <td className={globalStyles.cell}>
                                        <input style={{width: "90%"}}
                                            type="text"
                                            className="form-control"
                                            placeholder="Enter source value to override"
                                            value={mapping.sourcevalue}
                                            onChange={(e) => handleSourceValueChange(index, e.target.value)}
                                        />
                                    </td>
                                    <td className={globalStyles.cell}>
                                        <input style={{width: "90%"}}
                                            type={attribute.type}
                                            className="form-control"
                                            placeholder="Enter target value to override"
                                            value={mapping.targetvalue}
                                            onChange={(e) => handleTargetValueChange(index, e.target.value)}
                                        />

                                    </td>
                                    <td className={globalStyles.cell}>
                                        <div>
                                            {index >= 1 && (
                                                <button onClick={() => handleDeleteMapping(index)}>
                                                    <span>-</span>
                                                </button>
                                            )}

                                        </div>
                                    </td>
                                </tr>


                            </React.Fragment>
                        ))}
                        </tbody>
                    </table>
                </div>

                <div className={styles.actionButtons}>
                    <button className={styles.addButton} onClick={handleAddMapping}>
                        <span>+</span> Add value for mapping
                    </button>
                    <button className={styles.overrideButton} onClick={handleSaveOverrides}>
                        <img
                            src="/assets/check list.png"
                            alt="Checklist Icon"
                            className={styles.buttonIcon}
                        /> Save Changes
                    </button>
                </div>
            </div>
        </div>
    );
}
