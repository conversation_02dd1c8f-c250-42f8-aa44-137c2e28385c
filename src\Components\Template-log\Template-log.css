.activity-log-container {
  display: flex;
  gap: 20px;
  padding: 5px 20px;
  margin: 0 auto;
}
.template-container {
    gap: 15px;
    width: 100%;
    /* padding: 17px 45px 17px 30px; */
}

  .template-table {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
  }

  .template-table th {
    font-family: Poppins;
    font-weight: 600;
    font-size: 13px;
    line-height: 24px;
    text-align: left;
    padding: 10px;
    background: #EFEFEF;
    color: #514742;
  }

  .template-table td {
    font-family: Inter;
    font-weight: 400;
    color: #170903;
    line-height: 24px;
    padding: 10px;
    border-bottom: 1px solid #ccc;
  }

  .creator-placeholder {
    width: 35px;
    height: 35px;
    background: #EFEEED;
    border-radius: 50%;
    display: inline-block;
  }

.migration-title {
  font-family: Poppins;
  font-size: 12px;
  font-weight: 700;
  color: #EA5822;
  margin-bottom: 10px;
}

.toggle-container {
  display: flex;
  gap: 12px;
}

.toggle-button {
  padding: 3px 26px;
  border-radius: 7px;
  border: 1px solid #DCDAD9;
  background-color: white;
  transition: background-color 0.3s ease;
  color: #514742;
  font-family: Inter;
  font-size: 12px;
  font-weight: 400;
}

.toggle-button.active-toggle {
  background-color: #EFEEED;
  border-color: #EFEEED;
}

.migration-content {
  display: flex;
  width: 458px;
  align-items: flex-start;
  gap: 53px;
}

.metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.metric-item {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: center;
  gap: 12px;
  margin-left: 15px;
}

.metric-number {
  font-size: 36px;
  font-weight: 600;
  color: #746B68;
  line-height: 24px;
  font-family: Poppins;
}

.metric-label {
  display: flex;
  align-items: center;
  color: #6B7280;
  font-size: 14px;
  justify-content: flex-start;
  font-family: "inter";
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}
.dotName{
  font-family: Inter;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  text-align: left;
}
.template-dot {
  background-color: #E5E7EB;
}

.migration-dot {
  background-color: #EF8963;
}

.chart-container-pie {
  width: 250px;
  height: 250px;
  flex-shrink: 0;
}

.section {
  flex: 1;
  width: 50%;
  padding: 20px;

}
.userAvatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #efeeed;
}


