.dFlex {
    display: flex;
    align-items: center;
}

.section {
    flex: 1;
    width: 50%;
    padding: 20px;

}

.migrationContent {
    display: flex;
    /*align-items: center;*/
    justify-content: space-around;
}

.metrics {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
}

.metricItem {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    justify-content: center;
    gap: 12px;
    margin-left: 15px;
}

.metricNumber {
    font-size: 36px;
    font-weight: 600;
    color: #746B68;
    line-height: 24px;
    font-family: Poppins;
}

.metricLabel {
    display: flex;
    align-items: center;
    color: #6B7280;
    font-size: 16px;
    justify-content: flex-end;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.dotName {
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #170903;
}

.templateDot {
    background-color: #A43E18;
}

.migrationDot {
    background-color: #EF8963;
}

.chartContainerPie {
    width: 50%;
    height: 250px;
}

.statusBadge {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3px 15px;
    border-radius: 7px;
    width: 75px;
    height: 30px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #170903;
}

.statCard {
    background-color: #ffffff;
    /*width: 261px;*/
    width: 50%;
    border-radius: 5px;
    padding: 20px 30px;
    border: 1px solid #DCDAD9;
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 20px;
    height: 78px;
}

/* @keyframes myAnim {
    0% {
        transform: scale(0.5);
    }

    100% {
        transform: scale(1);
    }
}

.sample-migration-animation {
    animation: myAnim 2s ease 0s 1 normal forwards;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    width: 200px;
    margin: 20px auto;
} */

@keyframes dataMigration {
    0% {
        box-shadow: 0 0 15px rgba(239, 121, 83, 0.3);
    }

    50% {
        box-shadow: 0 0 30px rgba(239, 121, 83, 0.7), 0 0 50px rgba(239, 121, 83, 0.2);
    }

    100% {
        box-shadow: 0 0 15px rgba(239, 121, 83, 0.3);
    }
}

@keyframes dataFlow {
    0% {
        transform: translateX(0%) scaleX(0.8);
        opacity: 0;
    }

    10% {
        transform: translateX(0%) scaleX(1);
        opacity: 1;
    }

    90% {
        transform: translateX(100%) scaleX(1);
        opacity: 1;
    }

    100% {
        transform: translateX(100%) scaleX(0.8);
        opacity: 0;
    }
}


.section {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 30px 0;
}

.migration-container {
    width: 100%;
    max-width: 400px;
    height: 200px;
    background: linear-gradient(125deg, #111111, #1e1209);
    border-radius: 22px;
    border: 2px solid rgba(239, 121, 83, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.5), inset 0 2px 2px rgba(255, 255, 255, 0.1);
    animation: dataMigration 5s ease infinite;
    margin: 0 auto;
}

/* Center area for packet */
.packets-area {
    position: absolute;
    height: 20px;
    /* 🔥 Reduced height */
    top: 50%;
    transform: translateY(-50%);
    left: 140px;
    /* 🔥 Adjust to start near left database */
    right: 140px;
    /* 🔥 Adjust to end exactly at right database */
    pointer-events: none;
}

.data-packet {
    width: 60px;
    height: 10px;
    background-color: #FF7F50;
    border-radius: 10px;
    animation: dataFlow 3s linear infinite;
}


.migration-text {
    position: absolute;
    bottom: 30px;
    /* Adjusted for larger container */
    font-size: 40px;
    /* Even larger and more futuristic */
    color: #ef7953;
    font-weight: 900;
    text-align: center;
    width: 100%;
    letter-spacing: 3px;
    font-family: 'Orbitron', 'Arial Narrow', sans-serif;
    text-shadow: 0 0 20px rgba(239, 121, 83, 0.9), 0 0 30px rgba(239, 121, 83, 0.6);
    animation: glow 2s infinite ease-in-out;
    text-transform: uppercase;
}

.db-icon-left,
.db-icon-right {
    position: absolute;
    height: 100px;
    /* Larger height */
    width: 70px;
    /* Larger width */
    border-radius: 10px;
    /* Larger radius */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
    padding-top: 15px;
    /* Adjusted padding */
}

.db-icon-left {
    left: 50px;
    /* Adjusted for larger container */
    background: linear-gradient(180deg, #444444, #333333);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.db-icon-right {
    right: 50px;
    /* Adjusted for larger container */
    background: linear-gradient(180deg, #ef7953, #d06944);
    box-shadow: 0 5px 15px rgba(239, 121, 83, 0.4);
    border: 2px solid rgba(239, 121, 83, 0.3);
}

.db-icon-left::before,
.db-icon-right::before {
    content: '';
    position: absolute;
    height: 15px;
    /* Larger height */
    width: 70px;
    /* Larger width */
    border-radius: 10px 10px 0 0;
    top: 0;
}

.db-icon-left::before {
    background: rgba(203, 213, 225, 0.3);
}

.db-icon-right::before {
    background: rgba(248, 177, 149, 0.5);
}

.db-lines {
    width: 50px;
    /* Larger width */
    height: 5px;
    /* Larger height */
    border-radius: 3px;
    /* Larger radius */
}

.db-icon-left .db-lines {
    background: rgba(255, 255, 255, 0.4);
}

.db-icon-right .db-lines {
    background: rgba(255, 255, 255, 0.7);
}