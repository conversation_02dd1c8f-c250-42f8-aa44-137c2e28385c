import config from "../Config/config.json";
import { showArticle } from "@intercom/messenger-js-sdk";

export const displayArticle = (title) => {
    console.log("Title:", title);
    const article = config.INTERCOM_ARTICLES_ENUM.find(item => item.title.toLocaleLowerCase() === title.toLocaleLowerCase());
    if (article) {
        console.log("Article:", article);
        showArticle(parseInt(article.id));
    }
}

export const parseJsonFields = (data) => {
    // Handle an array of objects
    if (Array.isArray(data)) {
        return data.map(item => {
            if (item && typeof item === 'object') {
                const parsed = { ...item };

                for (const key in parsed) {
                    if (typeof parsed[key] === 'string') {
                        const str = parsed[key].trim();
                        if (
                            (str.startsWith('{') && str.endsWith('}')) ||
                            (str.startsWith('[') && str.endsWith(']'))
                        ) {
                            try {
                                parsed[key] = JSON.parse(str);
                            } catch (err) {
                                console.warn(`Failed to parse JSON for key "${key}":`, err);
                            }
                        }
                    }
                }

                return parsed;
            }

            return item;
        });
    }

    // Handle a single object
    if (data && typeof data === 'object') {
        const parsed = { ...data };

        for (const key in parsed) {
            if (typeof parsed[key] === 'string') {
                const str = parsed[key].trim();
                if (
                    (str.startsWith('{') && str.endsWith('}')) ||
                    (str.startsWith('[') && str.endsWith(']'))
                ) {
                    try {
                        parsed[key] = JSON.parse(str);
                    } catch (err) {
                        console.warn(`Failed to parse JSON for key "${key}":`, err);
                    }
                }
            }
        }

        return parsed;
    }

    return data;
};
