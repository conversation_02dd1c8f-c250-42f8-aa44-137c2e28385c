/* Dialog styles */
.dialogOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialogContent {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.dialogHeader {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dialogHeader h3 {
    margin: 0;
    font-weight: 500;
    color: #333;
}

.closeDialogIcon {
    cursor: pointer;
    font-size: 20px;
    color: #666;
}

.dialogTabs {
    display: flex;
    border-bottom: 1px solid #eee;
}

.tabButton {
    padding: 12px 24px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.activeTab {
    border-bottom-color: #EF8963;
    color: #EF8963;
}

.dialogBody {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.statusLine {
    font-weight: 500;
    margin-bottom: 16px;
    font-size: 16px;
}

.successCode {
    color: #4CAF50;
    font-weight: bold;
}

.errorCode {
    color: #F44336;
    font-weight: bold;
}

.codeDisplay {
    background-color: #272822;
    color: #f8f8f2;
    padding: 16px;
    border-radius: 4px;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.5;
}

.dialogFooter {
    padding: 16px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

.closeButton {
    padding: 8px 16px;
    background-color: #EF8963;
    color: #170903;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    text-align: center;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    line-height: 24px; /* 150% */
}

.sourceSection {
    margin-bottom: 16px;
}

.sourceSection h4 {
    margin-bottom: 8px;
}

/* Add any additional styles that might be needed */
.dFlex {
    display: flex;
    align-items: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spin {
    animation: spin 1s linear infinite;
    display: inline-block;
}
