import React, { useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import SignIn from "../Components/Sign-in/Sign-in";
import SignUp from "../Components/Sign-up/Sign-up";
import ForgotPassword from "../Components/Forgot-password/Forgot-password";
import ResetPassword from "../Components/Reset-password/Reset-password";
import Home from "../Components/Home/Home";
import DataMigration from "../Components/Data-Migration/Data-Migration";
import Migration from "../Components/Data-Migration/Migration/Migration";
import Pay from "../Components/Data-Migration/Review/Pay/Pay";
import PaymentDetails from "../Components/Data-Migration/Review/paymentdetails";
import UploadCSV from "../Components/Data-Migration/UploadCSV/uploadcsv";
import MigrationHistory from "../Components/Migration-History/MigrationHistory";
import SavedTemplates from "../Components/Saved-templates/Saved-templates";
import LoaderSpinner from "../Components/loaderspinner";
import ProfileSettings from "../Components/Profile-settings/Profile-settings";
import ProfilePictureUpdate from "../Components/Profile-settings/ProfilePicture/profilePicture";
import Phonenumber from "../Components/Profile-settings/PhoneNumber/PhoneNumber";
import ChangePassword from "../Components/Profile-settings/ChangePaassword/ChangePassword";
import BillingPayment from "../Components/Billing-Payments/BillingPayment";
import DataSecurity from "../Components/Data-Security/Data-security";
import ProductPricing from "../Components/Product-Pricing/product-pricing";
import ActivityAndAnalytics from "../Components/ActivityandAnalytics/activity-and-analytics";
import ShareYourTemplate from "../Components/Share-your-template/ShareYourTemplate";
import APITester from "../Components/ApiTester/ApiTester";
import { SessionProvider, useSession } from "../SessionContext";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import SessionExpiredPopup from "../Components/SessionExpiredPopup/SessionExpiredPopup";
import config from './../Config/config.json';
import Intercom from '@intercom/messenger-js-sdk';
const CryptoJS = require('crypto-js');

const PrivateRoute = ({ element }) => {
    return localStorage.getItem("email") ? element : <Navigate to="/" />;
};

export default function AppRoutes() {
    useEffect(() => {
        const userEmail = localStorage.getItem("email");
        console.log("User Email:", userEmail);
        if (userEmail) {
            console.log("User Email found in localStorage");
            const secretKey = config.INTERCOM_KEY;
            const hash = CryptoJS.HmacSHA256(userEmail, secretKey).toString(CryptoJS.enc.Hex);

            const intercomConfig = {
                app_id: config.INTERCOM_APP_ID
            };

            if (userEmail && userEmail !== '') {
                intercomConfig.user_id = userEmail;
                intercomConfig.email = userEmail;
            }

            if (hash && hash !== '') {
                intercomConfig.user_hash = hash;
            }

            Intercom(intercomConfig);
        }
    }, []);

    return (
        <Router>
            <SessionProvider> {/* Wrap everything in SessionProvider */}
                <SessionExpiredPopup />
                <Routes>
                    <Route path="/" element={<SignIn />} />
                    <Route path="/sign-up" element={<SignUp />} />
                    <Route path="/forgotpassword" element={<ForgotPassword />} />
                    <Route path="/resetpassword" element={<ResetPassword />} />
                    {/*<Route path="/home" element={<Home />} />*/}
                    {/*<Route path="/data-migration" element={<DataMigration />} />*/}
                    {/*<Route path="/migration-results" element={<Migration />} />*/}
                    {/*<Route path="/pay" element={<Pay />} />*/}
                    {/*<Route path="/paymentdetails" element={<PaymentDetails />} />*/}
                    {/*<Route path="/upload-csv" element={<UploadCSV />} />*/}
                    <Route path="/home" element={<PrivateRoute element={<Home />} />} />
                    <Route path="/data-migration" element={<PrivateRoute element={<DataMigration />} />} />
                    <Route path="/migration-results" element={<PrivateRoute element={<Migration />} />} />
                    <Route path="/pay" element={<PrivateRoute element={<Pay />} />} />
                    <Route path="/paymentdetails" element={<PrivateRoute element={<PaymentDetails />} />} />
                    <Route path="/upload-csv" element={<PrivateRoute element={<UploadCSV />} />} />
                    {/*<Route path="/loader" element={<LoaderSpinner />} />*/}
                    <Route path="/migration-history" element={<PrivateRoute element={<MigrationHistory />} />} />
                    <Route path="/saved-templates" element={<PrivateRoute element={<SavedTemplates />} />} />
                    <Route path="/profile-settings" element={<PrivateRoute element={<ProfileSettings />} />} />
                    <Route path="/data-security" element={<PrivateRoute element={<DataSecurity />} />} />
                    <Route path="/billing-payments" element={<PrivateRoute element={<BillingPayment />} />} />
                    <Route path="/product-pricing" element={<PrivateRoute element={<ProductPricing />} />} />
                    <Route path="/activity-and-analytics" element={<PrivateRoute element={<ActivityAndAnalytics />} />} />
                    <Route path="/share-template" element={<PrivateRoute element={<ShareYourTemplate />} />} />
                    <Route path="/api-tester" element={<PrivateRoute element={<APITester />} />} />
                </Routes>
                <ToastContainer
                    position="top-right"
                    autoClose={5000}
                    hideProgressBar={false}
                    newestOnTop={false}
                    closeOnClick
                    rtl={false}
                    pauseOnFocusLoss
                    draggable
                    pauseOnHover
                    theme="light"
                    style={{ fontFamily: "Inter" }}
                    toastStyle={{ fontFamily: "Inter", fontWeight: "bold" }}
                />
            </SessionProvider>
        </Router>
    );
}

export { PrivateRoute };
