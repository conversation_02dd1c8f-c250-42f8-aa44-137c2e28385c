.defaultValueContainer {
    width: 100%;
    background-color: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color:#170903;
    padding: 12px 24px;
  }

  .title {
    color: #EA5822;
    font-family: Poppins;
    font-size: 27px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 88.889% */
}


  .closeButton {
    color: white;
    font-size: 24px;
    cursor: pointer;
    line-height: 1;
  }

  .content {
    padding: 0px;
  }

  .mappingGuideSection {
    display: flex;
    padding: 8px 16px;
    align-items: center;
    gap: 5px;
    align-self: stretch;
    background: #EFEEED;
    border-radius: 4px;
    margin-bottom: 0px;
}


  .mappingGuideLabel {
    color: #EA5822;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    margin-right: 4px;
}


  .mappingGuideText {
    font-family: Inter;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    color: #170903;


  }

  .formSection {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 0px;
    padding: 10px 25px;
}

  .formRow {
    display: flex;
    align-items: center;
  }

  .formLabel {
    color: #746B68;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    width: 40%;
}

.formValue {
    width: 60%;
    color: #170903;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}


  .formValueInput {
    width: 60%;
  }

  .inputField {
    width: 95%;
    padding: 10px;
    border: 1px solid #DCDAD9;
    border-radius: 3px;
    background: #FFF;
    font-family: "Inter";
    font-weight: 400;
    font-size: 16px;
}


  .actionSection {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .assignButton {
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    margin-bottom: 20px;
  }

  .assignButton:hover {
    background-color: #f9f9f9;
  }

  .buttonIcon {
    width: 16px;
    height: 16px;
  }
  .assignText {
    color: #514742;
    text-align: center;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}
