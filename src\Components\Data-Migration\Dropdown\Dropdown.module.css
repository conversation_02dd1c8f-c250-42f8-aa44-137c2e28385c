.dropdownContainer {
    position: relative;
    width: auto;
}

.dropdownHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #FFFFFF;
    cursor: pointer;
    height: 38px;
    box-sizing: border-box;
}
.hrLine{
    border: 1px solid #EFEEED;
    /*margin: 20px 0;*/
}

.selectedValue {
    flex: 1;
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #170903;
}
.options{
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #170903;

}

.placeholder {
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #170903;
}

.icon {
    color: #888;
    transition: transform 0.2s ease;
}

.rotated {
    transform: rotate(180deg);
}

.dropdownMenu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 250px;
    overflow-y: auto;
    background-color: #FFFFFF;
    border: 1px solid #E0E0E0;
    border-top: none;
    border-radius: 0 0 4px 4px;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.searchContainer {
    padding: 8px;
    border-bottom: 1px solid #E0E0E0;
    position: sticky;
    top: 0;
    background-color: #FFFFFF;
}

.searchInput {
    width: 90%;
    padding: 8px;
    border: 1px solid #E0E0E0;
    border-radius: 4px;
    font-size: 14px;
}

.optionsContainer {
    max-height: 250px;
    overflow-y: auto;
    /*width: 90%;*/

}

.option {
    padding: 5px 0px;
    cursor: pointer;
    font-size: 14px;
    height: 34px;
    text-align: center;
}
.selectAllButtonContainer {
    padding: 8px;
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #e2e8f0;
}

.selectAllButton {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    width: 100%;
    text-align: center;
}

.selectAllButton:hover {
    background-color: #edf2f7;
}
