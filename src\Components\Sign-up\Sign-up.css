.sign-in-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
}

.left-side {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.left-side-img {
    width: 80%;
    height: 90%;
    object-fit: cover;
}

.right-side {
    flex: 1;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 367px;
    /*height: 564px;*/
    background: #170903;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    padding: 45px 25px;
    text-align: center;
}

.button-container {
    display: flex;
    justify-content: flex-end;
}
.description{
    color: #F8F8F7;
    font-family: "poppins";
    text-align: left;
    font-size: 14px;
    font-weight: 600;
}

.password-container {
    position: relative;
    width: 100%;
    padding-top: 20px;
}

.password-input {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
    outline: none;
    font-size: 14px;
}


.password-toggle {
    position: absolute;
    right: 0px;
    top: 60%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    font-size: 18px;
}

.password-toggle:hover {
    color: #495057;
}

.password-toggle:focus {
    outline: none;
}


.Password{
    color:#B9B5B3;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    font-family: "Inter";
    margin-top: 0px;
    text-align: justify;
    margin-bottom: 25px;
}

.button-container-new{
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

}
.button-icon{
    height: 30px;
    width: 30px;
    padding-right: 10px;
}

