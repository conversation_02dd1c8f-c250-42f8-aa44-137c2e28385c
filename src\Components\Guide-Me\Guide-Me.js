import React, { useState } from "react";
import "./Guide-Me.css"
import { displayArticle } from "../../Helper/helper";

export default function GuideMe() {
    const [isVideoPlaying, setIsVideoPlaying] = useState(false);
    const menuItems = [
        {
            id: 1, title: "Guide to prepare for a migration"
        },
        {
            id: 2, title: "Setting up a migration in 4 simple steps"
        },
        {
            id: 3, title: "Creating & using templates for faster migration"
        },
        {
            id: 4, title: "Data mapping and transformation tips"
        },
        {
            id: 5, title: "Understanding migration reports and logs"
        }
    ];

    const handlePlayVideo = () => {
        setIsVideoPlaying(true);
    };

    return (
        <div className="resources-container">
            <div className="section">
                <div className="guides-content">
                    <h2 className="quick-guides-title">QUICK GUIDES FOR YOU</h2>
                    <ul className="guides-list">
                        {menuItems.map((item) => (
                            <li key={item.id}>
                                {item.title}
                                <img src="/assets/help.png" alt="Help" className="help-icon"
                                    onClick={() => displayArticle(item.title)}
                                />
                            </li>
                        ))}
                    </ul>
                </div>
            </div>

            <div className="section" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: 400, width: '100%' }}>
                <div className="video-content" onClick={handlePlayVideo} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', width: '100%', height: '100%', flex: 1 }}>
                    {/* Conditional rendering for showing play button or the video */}
                    {!isVideoPlaying ? (
                        <div style={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", width: '100%', height: '100%', flex: 1 }}>
                            <h2 className="text-video" style={{ marginBottom: 24, textAlign: 'center' }}>Watch the product demo</h2>
                            <button className="play-button" style={{ fontSize: 32, width: 90, height: 90, borderRadius: '50%', background: '#EA5822', color: '#fff', border: 'none', boxShadow: '0 2px 8px rgba(234,88,34,0.15)', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto' }}>▶</button>
                        </div>
                    ) : (
                        <iframe
                            style={{ borderRadius: 12, boxShadow: '0 2px 12px rgba(0,0,0,0.08)', maxWidth: 600, width: '100%', aspectRatio: '16/9', border: 0, minHeight: 200, background: '#fff', display: 'block', margin: '0 auto' }}
                            src="https://cdn.embedly.com/widgets/media.html?src=https%3A%2F%2Ffast.wistia.net%2Fembed%2Fiframe%2Fwrauox425u&display_name=Wistia%2C+Inc.&url=https%3A%2F%2Fsaasgenie.wistia.com%2Fmedias%2Fwrauox425u&image=https%3A%2F%2Fembed-ssl.wistia.com%2Fdeliveries%2F86ed1423ea8a73ca90bc752a2f2a1a7d.jpg%3Fimage_crop_resized%3D960x540&type=text%2Fhtml&schema=wistia"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                        ></iframe>
                    )}
                </div>
            </div>
        </div>
    );
}
