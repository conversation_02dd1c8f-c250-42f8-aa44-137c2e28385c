#root{
    height: 100vh;
    overflow: hidden; /* Ensure root doesn't have scroll */
}
.main-section {
    margin-left: 220px;
    padding: 12px 0;
    flex: 1;
    transition: margin-left 0.3s ease;
    background-color: #FFFF;
    height: 100vh;
}
.main-section.expanded {
    margin-left: 85px;
}
.dFlex {
    display: flex;
    align-items: center;

}

.searchBarContainer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    /*width: 100%;*/
    padding: 10px;
}

.searchBar {
    display: flex;
    gap: 10px;
    align-items: center;
}

.searchWrapper {
    display: flex;
    align-items: center;
    position: relative;
    margin-right: 20px;
}

.searchIcon {
    position: absolute;
    left: 10px;
    width: 20px;
    height: 20px;
    color: gray;
    padding-bottom: 10px;
}

.searchInput {
    width: 80px;
    padding: 10px 12px 10px 40px;
    border-radius: 7px;
    background-color: transparent;
    font-size: 12px;
    box-shadow: none;
}


.languageSelector {
    cursor: pointer;
    font-weight: bold;
}
.Mui-selected .stepNo {
    color: #170903 !important;
    font-size: 12px;
    font-weight: 600;
}
.stepNo{
    color:  #B9B5B3;
    font-family: Inter;
    font-size: 12px;
    font-weight: 400;
    text-align: left;
}
.stepHeader{
    height: 59px;
    background-color: #EFEEED;
    box-shadow:  0 5px 10px 0 rgba(0, 0, 0, 0.1);

}
.backButton{
    background-color: #FFFFFF;
    color: #170903;
    border: 1px solid #DCDAD9;
    padding: 8px 20px;
    margin: 0 15px 0 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    font-family: Inter;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.backButton:hover {
    background-color: #F8F8F7;
    border-color: #B9B5B3;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.backArrowButton {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    color: #170903;
}

.backArrowButton:hover {
    background-color: #F8F8F7;
}

.arrowIcon{
    height: 18px;
    width: 18px;
    margin-right: 8px;
    color: inherit;
}

.templateNameContainer {
    display: flex;
    align-items: center;
    gap: 15px;
}

.templateNameSection {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.templateNameSection:hover {
    background-color: #F8F8F7;
}

.templateInput {
    all: unset;
    font-family: Poppins;
    font-weight: 600;
    font-size: 16px;
    color: #EA5822;
    background-color: transparent;
    border: 1px solid #DCDAD9;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 200px;
}

.templateInput:focus {
    border-color: #EA5822;
    box-shadow: 0 0 0 2px rgba(234, 88, 34, 0.1);
}

.templateIdText {
    font-family: Inter;
    font-weight: 400;
    font-size: 14px;
    color: #6B7280;
    white-space: nowrap;
    margin-left: auto;
    margin-right: 25px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .backButton {
        padding: 6px 15px;
        margin: 0 10px 0 20px;
        font-size: 13px;
    }

    .backArrowButton {
        padding: 6px;
    }

    .templateNameContainer {
        gap: 10px;
    }

    .templateInput {
        min-width: 150px;
        font-size: 14px;
    }

    .templateIdText {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .backButton {
        padding: 5px 12px;
        margin: 0 8px 0 15px;
        font-size: 12px;
    }

    .backArrowButton {
        padding: 4px;
    }

    .arrowIcon {
        height: 16px;
        width: 16px;
        margin-right: 6px;
    }

    .templateNameContainer {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .templateInput {
        min-width: 120px;
        font-size: 13px;
    }

    .templateIdText {
        font-size: 11px;
    }
}

