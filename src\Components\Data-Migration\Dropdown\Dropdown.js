import React, { useState, useRef, useEffect } from 'react';

const dropdownStyles = `
  /* Search input styles */
  .simple-dropdown .search-container {
    padding: 8px;
    position: sticky;
    top: 0;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    z-index: 2;
  }
  .simple-dropdown .search-input {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    outline: none;
    transition: border-color 0.2s;
  }
  .simple-dropdown .search-input:focus {
    border-color: #EA5822;
    box-shadow: 0 0 0 2px rgba(234, 88, 34, 0.1);
  }

  .simple-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
  }
  .simple-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #111;
    background-color: #fff;
    border: 1px solid #fff;
    border-radius: 0.35rem;
    cursor: pointer;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-height: 42px;
    max-width: 260px;
    box-shadow: 0 2px 8px rgba(234,88,34,0.08);
    transition: border-color 0.2s, box-shadow 0.2s;
  }
  .simple-dropdown .dropdown-toggle:focus, .simple-dropdown .dropdown-toggle:hover {
    border-color:rgb(238, 104, 55);
    box-shadow: 0 0 0 1.5px #EA582233;
  }
  .simple-dropdown .dropdown-toggle span {
    max-width: 220px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    color: #111;
    font-size: 14px;
    font-weight: 300;
  }
  .simple-dropdown .dropdown-toggle::after {
    margin-left: auto;
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-top: 0.4em solid rgb(234, 105, 58);
    border-right: 0.4em solid transparent;
    border-left: 0.4em solid transparent;
  }
  .simple-dropdown .dropdown-menu {
    position: absolute;
    z-index: 1000;
    display: none;
    min-width: 100%;
    padding: 0;
    margin: 0;
    font-size: 13px;
    color: #111;
    background-color: #fff;
    border: 1px solid #EA5822;
    border-radius: 0.35rem;
    box-shadow: 0 4px 16px rgba(234,88,34,0.12);
    max-height: 320px;
    overflow-y: auto;
    margin-top: 2px;
  }
  .simple-dropdown .dropdown-menu.show {
    display: block;
  }
  .simple-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.5rem 1.2rem;
    font-weight: 500;
    color: #111;
    background-color: transparent;
    border: 0;
    cursor: pointer;
    max-width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: background 0.2s, color 0.2s;
  }
  .simple-dropdown .dropdown-item:hover, .simple-dropdown .dropdown-item.bg-light {
    background-color: #EA582222;
    color: #EA5822;
  }
  .simple-dropdown .dropdown-item input[type='checkbox'] {
    accent-color: #EA5822;
  }
  .simple-dropdown .dropdown-item.text-muted {
    color: #888;
    font-style: italic;
  }
`;

export default function SimpleDropdown({
  value = '',
  options = [],
  onChange = () => {},
  placeholder = 'Select...',
  required = false,
  displayKey = 'label',
  valueKey = 'value',
  multiSelect = false, // Add multiSelect prop
  showSelectAll = false, // Add showSelectAll prop
}) {
  // Use multiSelect and showSelectAll as passed in props, do not force for 'source' placeholder
  const effectiveMultiSelect = multiSelect;
  const effectiveShowSelectAll = showSelectAll;

  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);
  const [selectedValues, setSelectedValues] = useState(
    effectiveMultiSelect ? (Array.isArray(value) ? value : []) : value
  );

  // Update selected values when value prop changes
  useEffect(() => {
    if (effectiveMultiSelect) {
      setSelectedValues(Array.isArray(value) ? value : []);
    }
  }, [value, effectiveMultiSelect]);

  // Initialize filtered options when options change
  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  // Filter options based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredOptions(options);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = options.filter(option => {
        const label = typeof option === 'object'
          ? String(option[displayKey] || '').toLowerCase()
          : String(option).toLowerCase();
        return label.includes(query);
      });
      setFilteredOptions(filtered);
    }
  }, [searchQuery, options, displayKey]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (menuIsOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    } else {
      // Clear search when dropdown closes
      setSearchQuery('');
    }
  }, [menuIsOpen]);

  // Handle keyboard events for accessibility
  const handleKeyDown = (event) => {
    if (!menuIsOpen) return;

    switch (event.key) {
      case 'Escape':
        setMenuIsOpen(false);
        break;
      case 'ArrowDown':
        if (searchInputRef.current === document.activeElement) {
          // Move focus to the first option
          const firstOption = dropdownRef.current.querySelector('.dropdown-item');
          if (firstOption) {
            event.preventDefault();
            firstOption.focus();
          }
        }
        break;
      default:
        break;
    }
  };

  // Handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setMenuIsOpen(false);
      }
    };

    const handleKeyDownEvent = (event) => {
      handleKeyDown(event);
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDownEvent);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDownEvent);
    };
  }, [menuIsOpen]);

  const handleOptionClick = (option) => {
    if (effectiveMultiSelect) {
      const optValue = typeof option === 'object' ? option[valueKey] : option;
      let newSelected;
      if (selectedValues.includes(optValue)) {
        newSelected = selectedValues.filter((v) => v !== optValue);
      } else {
        newSelected = [...selectedValues, optValue];
      }
      setSelectedValues(newSelected);
      onChange(newSelected);
    } else {
      onChange(option[valueKey] ?? option);
      setMenuIsOpen(false);
    }
  };

  const handleSelectAll = () => {
    if (!effectiveMultiSelect) return;

    // Get all values from filtered options
    const filteredValues = filteredOptions.map(opt =>
      typeof opt === 'object' ? opt[valueKey] : opt
    );

    // Check if all filtered options are selected
    const allFilteredSelected = filteredValues.every(val =>
      selectedValues.includes(val)
    );

    if (allFilteredSelected) {
      // Deselect only the filtered options
      const newValues = selectedValues.filter(val =>
        !filteredValues.includes(val)
      );
      setSelectedValues(newValues);
      onChange(newValues);
    } else {
      // Select all filtered options (keeping previously selected ones)
      const newValues = [...new Set([...selectedValues, ...filteredValues])];
      setSelectedValues(newValues);
      onChange(newValues);
    }
  };

  const displayText = () => {
    if (effectiveMultiSelect) {
      if (!selectedValues || selectedValues.length === 0) {
        return `${placeholder}${required ? '*' : ''}`;
      }
      if (selectedValues.length > 2) {
        return `${selectedValues.length} selected`;
      }
      return options
        .filter((opt) => selectedValues.includes(typeof opt === 'object' ? opt[valueKey] : opt))
        .map((opt) => (typeof opt === 'object' ? opt[displayKey] : opt))
        .join(', ');
    } else {
      if (!value && value !== 0) {
        return `${placeholder}${required ? '*' : ''}`;
      }
      const selected = options.find(
        (opt) => (typeof opt === 'object' ? opt[valueKey] : opt) === value
      );
      if (selected) {
        return typeof selected === 'object' ? selected[displayKey] : selected;
      }
      return value;
    }
  };

  return (
    <div className="simple-dropdown" ref={dropdownRef}>
      <style>{dropdownStyles}</style>
      <div className="dropdown">
        <button
          className="dropdown-toggle"
          type="button"
          onClick={() => setMenuIsOpen((prev) => !prev)}
          aria-haspopup="listbox"
          aria-expanded={menuIsOpen}
          aria-label={`${placeholder} dropdown`}
        >
          <span>{displayText()}</span>
        </button>
        <div
          className={`dropdown-menu${menuIsOpen ? ' show' : ''}`}
          role="listbox"
          aria-multiselectable={effectiveMultiSelect}
          aria-label={`${placeholder} options`}
        >
          {/* Search input */}
          <div className="search-container">
            <input
              ref={searchInputRef}
              type="text"
              className="search-input"
              placeholder="Search options..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onClick={(e) => e.stopPropagation()}
              aria-label="Search dropdown options"
              aria-controls="dropdown-options"
              autoComplete="off"
              spellCheck="false"
            />
          </div>

          {/* Select All option for multi-select */}
          {effectiveMultiSelect && filteredOptions.length > 0 && (
            <div
              className="dropdown-item"
              onClick={handleSelectAll}
              style={{ fontWeight: 'bold', color: '#EA5822' }}
            >
              {filteredOptions.every(opt =>
                selectedValues.includes(typeof opt === 'object' ? opt[valueKey] : opt)
              )
                ? 'Deselect All'
                : 'Select All'
              }
            </div>
          )}

          {/* No options message */}
          {filteredOptions.length === 0 && (
            <div className="dropdown-item text-muted">
              {searchQuery ? 'No matching options' : 'No options available'}
            </div>
          )}

          {/* Option items */}
          {filteredOptions.map((option, idx) => {
            const optValue = typeof option === 'object' ? option[valueKey] : option;
            const optLabel = typeof option === 'object' ? option[displayKey] : option;
            const isSelected = effectiveMultiSelect ? selectedValues.includes(optValue) : value === optValue;
            return (
              <div
                key={optValue + '_' + idx}
                className={`dropdown-item${isSelected ? ' bg-light' : ''}`}
                onClick={() => handleOptionClick(option)}
                tabIndex="0"
                role="option"
                aria-selected={isSelected}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleOptionClick(option);
                  }
                }}
              >
                {effectiveMultiSelect && (
                  <input
                    type="checkbox"
                    checked={isSelected}
                    readOnly
                    style={{ marginRight: 8 }}
                  />
                )}
                {optLabel}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}