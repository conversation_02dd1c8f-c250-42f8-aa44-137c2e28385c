.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  border-radius: 16px;
  width: 450px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 30px 20px;
  text-align: center;
}

.modalBody {
  padding: 20px 10px 15px;
  font-family: "Inter", sans-serif;
  color: #333333;
  font-size: 22px;
  font-weight: 500;
}

.modalFooter {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 0 16px 16px;
}

.okButton {
  padding: 10px 30px;
  background-color: #F1815E;
  color: white;
  border: none;
  border-radius: 30px;
  font-family: "Inter", sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
}

.cancelButton {
  padding: 10px 30px;
  background-color: #E9F0F9;
  color: #5C5C5C;
  border: none;
  border-radius: 30px;
  font-family: "Inter", sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
}